.. index:: 
	single: Eval() and Debugging; Introduction

====================
Eval() and Debugging
====================

In this chapter we are going to learn about

* Error Handling using Try/Catch/Done

* Eval() function

* Raise() function

* Assert() function

.. index:: 
	pair: Eval() and Debugging; Try/Carch/Done

Try/Catch/Done
==============

Syntax:

.. code-block:: ring

	Try
		Statements...
	Catch
		Statements...
	Done

The statements in the Try block will be executed, if any error happens then
the statements in the catch block will be executed.

Inside the catch block we can use the variable cCatchError to get the error message

Example:

.. code-block:: ring

	Try
		see 5/0
	Catch
		see "Catch!" + nl + cCatchError
	Done

Output:

.. code-block:: ring

	Catch!
	Error (R1) : Can't divide by zero !


.. index:: 
	pair: Eval() and Debugging; Eval()

Eval() Function
===============

We can execute code during the runtime from string using the Eval() function

Syntax:

.. code-block:: ring

	Eval(cCode)

Example:

.. code-block:: ring

	Eval("nOutput = 5+2*5 " )
	See "5+2*5 = " + nOutput + nl			 
	Eval("for x = 1 to 10 see x + nl next")		 
	Eval("func test see 'message from test!' ")	 
	test()

Output:

.. code-block:: ring

	5+2*5 = 15
	1
	2
	3
	4
	5
	6
	7
	8
	9
	10
	message from test!


We can use the Return command to return a value

Example:

.. code-block:: ring

	see Eval("return 5*5")

Output:

.. code-block:: ring

	25

.. index:: 
	pair: Eval() and Debugging; Raise()

Raise() Function
================

We can raise an exception using the Raise() function

Syntax:

.. code-block:: ring

	Raise(cErrorMessage)

The function will display the error message then end the execution of the program.

We can use Try/Catch/Done to avoid exceptions generated by raise() function.

Example:

.. code-block:: ring

	nMode = 10

	if nMode < 0 or nMode > 5
		raise("Error : nMode not in the range 1:4")
	ok

Output:

.. code-block:: ring

	Line 4 Error : nMode not in the range 1:4
	In raise in file tests\raise.ring

Example:

.. code-block:: ring

	try 
		testmode(6)
	catch
		see "avoid raise!"
	done

	testmode(-1)

	func testmode nMode

		if nMode < 0 or nMode > 5
			raise("Error : nMode not in the range 1:4")
		ok

Output:

.. code-block:: ring

	avoid raise!
	Line 12 Error : nMode not in the range 1:4
	In raise In function testmode() in file tests\raise2.ring
	called from line 7  in file tests\raise2.ring

.. index:: 
	pair: Eval() and Debugging; Assert()

Assert() Function
=================

We can use the Assert() function to test conditions before executing the code

If the test fail the program will be terminated with an error message contains the assert condition.

Syntax:

.. code-block:: ring

	Assert( condition )

Example:

.. code-block:: ring

	x = 10
	assert( x = 10)
	assert( x = 100 ) 

Output:

.. code-block:: ring

	Line 3 Assertion Failed!
	In assert in file tests\assert.ring
