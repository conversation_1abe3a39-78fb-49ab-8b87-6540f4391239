class QueryBuilder
    table = ""
    columns = []
    conditions = []
    orderBy = ""
    limitValue = 0
    joins = []
    groupByColumns = []
    havingConditions = []
    unionQueries = []
    
    func init tableName
        table = tableName
        columns = []
        conditions = []
        orderBy = ""
        limitValue = 0
        joins = []
        groupByColumns = []
        havingConditions = []
        unionQueries = []
        return self
        
    func select columnsList
        if isList(columnsList)
            columns = columnsList
        else
            columns = [columnsList]
        ok
        return self
        
    func where column, operator, value
        conditions + [column, operator, value]
        return self
        
    func whereIn column, values
        if isList(values)
            conditions + [column, "IN", "(" + join(values, ",") + ")"]
        ok
        return self
        
    func whereBetween column, start, end
        conditions + [column, "BETWEEN", start + " AND " + end]
        return self
        
    func whereNull column
        conditions + [column, "IS", "NULL"]
        return self
        
    func whereNotNull column
        conditions + [column, "IS NOT", "NULL"]
        return self
        
    func orWhere column, operator, value
        if len(conditions) > 0
            conditions + ["OR"]
        ok
        conditions + [column, operator, value]
        return self
        
    func join tableName, firstColumn, operator, secondColumn
        joins + ["INNER JOIN " + tableName + " ON " + firstColumn + " " + operator + " " + secondColumn]
        return self
        
    func leftJoin tableName, firstColumn, operator, secondColumn
        joins + ["LEFT JOIN " + tableName + " ON " + firstColumn + " " + operator + " " + secondColumn]
        return self
        
    func rightJoin tableName, firstColumn, operator, secondColumn
        joins + ["RIGHT JOIN " + tableName + " ON " + firstColumn + " " + operator + " " + secondColumn]
        return self
        
    func groupBy columnsList
        if isList(columnsList)
            groupByColumns = columnsList
        else
            groupByColumns = [columnsList]
        ok
        return self
        
    func having column, operator, value
        havingConditions + [column, operator, value]
        return self
        
    func union queryBuilder
        unionQueries + ["UNION " + queryBuilder.build()]
        return self
        
    func unionAll queryBuilder
        unionQueries + ["UNION ALL " + queryBuilder.build()]
        return self
        
    func orderByDesc column
        orderBy = " ORDER BY " + column + " DESC"
        return self
        
    func orderByAsc column
        orderBy = " ORDER BY " + column + " ASC"
        return self
        
    func orderByRaw orderByClause
        orderBy = " ORDER BY " + orderByClause
        return self
        
    func limit value
        limitValue = value
        return self
        
    func build
        query = "SELECT "
        
        if len(columns) = 0
            query += "*"
        else
            query += join(columns, ",")
        ok
        
        query += " FROM " + table
        
        # إضافة JOIN
        if len(joins) > 0
            for join in joins
                query += " " + join
            next
        ok
        
        # إضافة WHERE
        if len(conditions) > 0
            query += " WHERE "
            for i = 1 to len(conditions)
                if conditions[i] = "OR"
                    query += " OR "
                    loop
                ok
                if i > 1 and conditions[i-1] != "OR"
                    query += " AND "
                ok
                if find(conditions[i], "IN") or find(conditions[i], "BETWEEN") or find(conditions[i], "NULL")
                    query += conditions[i]
                    i += 2
                else
                    query += conditions[i] + " " + conditions[i+1] + " ?"
                    i += 2
                ok
            next
        ok
        
        # إضافة GROUP BY
        if len(groupByColumns) > 0
            query += " GROUP BY " + join(groupByColumns, ",")
        ok
        
        # إضافة HAVING
        if len(havingConditions) > 0
            query += " HAVING "
            for i = 1 to len(havingConditions) step 3
                if i > 1
                    query += " AND "
                ok
                query += havingConditions[i] + " " + havingConditions[i+1] + " ?"
            next
        ok
        
        if orderBy != ""
            query += orderBy
        ok
        
        if limitValue > 0
            query += " LIMIT " + limitValue
        ok
        
        # إضافة UNION
        if len(unionQueries) > 0
            for unionQuery in unionQueries
                query += " " + unionQuery
            next
        ok
        
        return query
        
    private
        func join list, separator
            result = ""
            for i = 1 to len(list)
                if i > 1
                    result += separator
                ok
                result += list[i]
            next
            return result
