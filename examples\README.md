# أمثلة الاستخدام

هذا المجلد يحتوي على أمثلة توضيحية لاستخدام نظام Smart Transformer.

## الأمثلة المتوفرة

### 1. الترجمة الأساسية (01_basic_translation.ring)
- ترجمة نصوص من الإنجليزية إلى العربية
- ترجمة نصوص من العربية إلى الإنجليزية
- أمثلة متنوعة للترجمة

### 2. توليد الكود (02_code_generation.ring)
- توليد كود Ring من وصف نصي
- أمثلة متنوعة لتوليد الكود
- توليد كود مع معاملات مخصصة

### 3. إدارة قاعدة البيانات (03_database_management.ring)
- إضافة وتحديث البيانات
- البحث في قاعدة البيانات
- تنظيف البيانات
- إدارة مقاييس الأداء
- النسخ الاحتياطي

### 4. مثال متقدم (04_advanced_example.ring)
- دمج جميع المميزات في تطبيق واحد
- إدارة الترجمة وتوليد الكود
- تتبع الأداء
- إنشاء تقارير

## كيفية التشغيل

1. تأكد من تثبيت جميع المتطلبات:
   ```ring
   load "stdlib.ring"
   load "matrixlib.ring"
   load "sqlitelib.ring"
   ```

2. تشغيل المثال المطلوب:
   ```ring
   ring examples/01_basic_translation.ring
   ```

## ملاحظات
- تأكد من وجود اتصال بقاعدة البيانات قبل تشغيل الأمثلة
- يمكن تعديل الأمثلة حسب احتياجاتك
- راجع التعليقات في الكود للحصول على مزيد من التفاصيل
