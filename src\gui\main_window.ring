load "guilib.ring"
load "../utils/config.ring"
load "../utils/data_utils.ring"
load "../train/trainer.ring"
load "../core/advanced_evaluator.ring"

# النافذة الرئيسية للتطبيق
Class MainWindow {
    win
    trainer
    evaluator
    utils
    
    # عناصر واجهة المستخدم
    trainTab
    evalTab
    translateTab
    
    # مخططات التقدم
    lossChart
    accuracyChart
    
    func init {
        setupWindow()
        createTabs()
        setupTrainer()
        setupEvaluator()
    }
    
    func setupWindow {
        win = new qMainWindow() {
            setWindowTitle("المحول الذكي - لوحة التحكم")
            setGeometry(100, 100, 1200, 800)
            
            new qWidget() {
                setLayout(new qVBoxLayout()) {
                    tabs = new qTabWidget() {
                        setTabPosition(QTabWidget_North)
                    }
                }
            }
            show()
        }
    }
    
    func createTabs {
        # تبويب التدريب
        createTrainingTab()
        
        # تبويب التقييم
        createEvaluationTab()
        
        # تبويب الترجمة والتوليد
        createTranslationTab()
    }
    
    func createTrainingTab {
        trainTab = new qWidget() {
            setLayout(new qVBoxLayout()) {
                # إعدادات التدريب
                new qGroupBox("إعدادات التدريب") {
                    setLayout(new qGridLayout()) {
                        # معدل التعلم
                        addWidget(new qLabel("معدل التعلم:"), 0, 0)
                        lrEdit = new qLineEdit() { setText("0.001") }
                        addWidget(lrEdit, 0, 1)
                        
                        # عدد الحلقات
                        addWidget(new qLabel("عدد الحلقات:"), 1, 0)
                        epochsEdit = new qLineEdit() { setText("10") }
                        addWidget(epochsEdit, 1, 1)
                        
                        # حجم الدفعة
                        addWidget(new qLabel("حجم الدفعة:"), 2, 0)
                        batchEdit = new qLineEdit() { setText("32") }
                        addWidget(batchEdit, 2, 1)
                    }
                }
                
                # أزرار التحكم
                new qHBoxLayout() {
                    startBtn = new qPushButton("بدء التدريب") {
                        setClickEvent(Method(:startTraining))
                    }
                    stopBtn = new qPushButton("إيقاف التدريب") {
                        setEnabled(false)
                        setClickEvent(Method(:stopTraining))
                    }
                    addWidget(startBtn)
                    addWidget(stopBtn)
                }
                
                # مخططات التقدم
                new qGroupBox("تقدم التدريب") {
                    setLayout(new qGridLayout()) {
                        # مخطط الخسارة
                        lossChart = new qChartView() {
                            chart = new qChart() {
                                setTitle("الخسارة")
                                addSeries(new qLineSeries())
                            }
                            setChart(chart)
                        }
                        addWidget(lossChart, 0, 0)
                        
                        # مخطط الدقة
                        accuracyChart = new qChartView() {
                            chart = new qChart() {
                                setTitle("الدقة")
                                addSeries(new qLineSeries())
                            }
                            setChart(chart)
                        }
                        addWidget(accuracyChart, 0, 1)
                    }
                }
                
                # سجل التدريب
                new qGroupBox("سجل التدريب") {
                    setLayout(new qVBoxLayout()) {
                        logText = new qTextEdit() {
                            setReadOnly(true)
                        }
                        addWidget(logText)
                    }
                }
            }
        }
        
        tabs.addTab(trainTab, "التدريب")
    }
    
    func createEvaluationTab {
        evalTab = new qWidget() {
            setLayout(new qVBoxLayout()) {
                # اختيار النموذج
                new qGroupBox("النموذج") {
                    setLayout(new qHBoxLayout()) {
                        addWidget(new qLabel("اختر النموذج:"))
                        modelCombo = new qComboBox() {
                            addItem("آخر نموذج")
                            addItem("أفضل نموذج")
                        }
                        addWidget(modelCombo)
                        
                        evalBtn = new qPushButton("تقييم") {
                            setClickEvent(Method(:startEvaluation))
                        }
                        addWidget(evalBtn)
                    }
                }
                
                # نتائج التقييم
                new qGroupBox("النتائج") {
                    setLayout(new qGridLayout()) {
                        # نتائج الترجمة
                        addWidget(new qLabel("درجة BLEU:"), 0, 0)
                        bleuLabel = new qLabel("--")
                        addWidget(bleuLabel, 0, 1)
                        
                        # نتائج توليد الكود
                        addWidget(new qLabel("دقة الكود:"), 1, 0)
                        accuracyLabel = new qLabel("--")
                        addWidget(accuracyLabel, 1, 1)
                    }
                }
            }
        }
        
        tabs.addTab(evalTab, "التقييم")
    }
    
    func createTranslationTab {
        translateTab = new qWidget() {
            setLayout(new qVBoxLayout()) {
                # اختيار النوع
                new qGroupBox("نوع العملية") {
                    setLayout(new qHBoxLayout()) {
                        typeCombo = new qComboBox() {
                            addItem("ترجمة (عربي-إنجليزي)")
                            addItem("ترجمة (إنجليزي-عربي)")
                            addItem("توليد كود")
                        }
                        addWidget(typeCombo)
                    }
                }
                
                # المدخلات
                new qGroupBox("النص المدخل") {
                    setLayout(new qVBoxLayout()) {
                        inputText = new qTextEdit()
                        addWidget(inputText)
                    }
                }
                
                # زر التنفيذ
                new qHBoxLayout() {
                    processBtn = new qPushButton("تنفيذ") {
                        setClickEvent(Method(:processText))
                    }
                    addWidget(processBtn)
                }
                
                # المخرجات
                new qGroupBox("النتيجة") {
                    setLayout(new qVBoxLayout()) {
                        outputText = new qTextEdit() {
                            setReadOnly(true)
                        }
                        addWidget(outputText)
                    }
                }
            }
        }
        
        tabs.addTab(translateTab, "الترجمة والتوليد")
    }
    
    # وظائف التدريب
    func startTraining {
        # تعطيل زر البدء وتفعيل زر الإيقاف
        startBtn.setEnabled(false)
        stopBtn.setEnabled(true)
        
        # تحديث الإعدادات
        config = new TransformerConfig {
            fLearningRate = number(lrEdit.text())
            nEpochs = number(epochsEdit.text())
            nBatchSize = number(batchEdit.text())
        }
        
        # بدء التدريب
        trainer = new TransformerTrainer(config)
        trainer.onEpochEnd = Method(:updateTrainingProgress)
        trainer.loadTrainingData()
        trainer.train()
    }
    
    func stopTraining {
        if trainer != NULL {
            trainer.stop()
            startBtn.setEnabled(true)
            stopBtn.setEnabled(false)
        }
    }
    
    func updateTrainingProgress(epoch, loss, accuracy) {
        # تحديث المخططات
        lossChart.chart.series()[1].append(epoch, loss)
        accuracyChart.chart.series()[1].append(epoch, accuracy)
        
        # تحديث السجل
        logText.append("الحلقة " + epoch + ": الخسارة = " + loss + ", الدقة = " + accuracy)
    }
    
    # وظائف التقييم
    func startEvaluation {
        evaluator = new AdvancedEvaluator()
        
        # تحميل النموذج المختار
        modelPath = modelCombo.currentText() = "آخر نموذج" ? 
                   "latest_model.bin" : "best_model.bin"
        
        if evaluator.loadModel(modelPath) {
            # تنفيذ التقييم
            evaluator.evaluateOnTestData()
            
            # عرض النتائج
            bleuLabel.setText(string(evaluator.metrics["bleu"]))
            accuracyLabel.setText(string(evaluator.metrics["accuracy"]))
        }
    }
    
    # وظائف الترجمة والتوليد
    func processText {
        text = inputText.text()
        type = typeCombo.currentText()
        
        # تحميل النموذج إذا لم يكن محملاً
        if trainer = NULL {
            trainer = new TransformerTrainer()
            trainer.loadModel("latest_model.bin")
        }
        
        # معالجة النص حسب النوع
        result = switch type {
            case "ترجمة (عربي-إنجليزي)"
                trainer.translate(text, "ar", "en")
            case "ترجمة (إنجليزي-عربي)"
                trainer.translate(text, "en", "ar")
            case "توليد كود"
                trainer.generateCode(text)
        }
        
        # عرض النتيجة
        outputText.setText(result)
    }
}

# تشغيل التطبيق
func main {
    new qApp {
        win = new MainWindow()
        exec()
    }
}

if __name__ = "main" {
    main()
}
