.. index:: 
	single: Using RingPDFGen; Introduction

================
Using RingPDFGen
================

In this chapter we will learn about Using the RingPDFGen extension.

This extension is added to the Ring language starting from Ring 1.21.

Contents:

* Example
* Constants
* Functions

.. index:: 
	pair: Using RingPDFGen; Example

Example
=======

The next source code generate a PDF file using RingPDFGen extension

.. code-block:: ring

	load "pdfgen.ring"
	
	cPDFFileName = "output.pdf"
	
	pdf = pdf_create(PDF_A4_WIDTH, PDF_A4_HEIGHT, [
		:creator  = "My software",
		:producer = "My software",
		:title    = "My document",
		:author   = "My name",
		:subject  = "My subject",
		:date     = "Today"
	] )
	
	pdf_set_font(pdf, "Times-Roman")
	pdf_append_page(pdf)
	
	pdf_add_text(pdf, NULL, "This is text", 12, 50, 20, PDF_BLACK)
	pdf_add_line(pdf, NULL, 50, 24, 150, 24, 0, 0)
	pdf_add_text(pdf, NULL, "This is text", 24, 250, 20, PDF_BLUE)
	
	for t=1 to 30
		pdf_add_text(pdf, NULL, "Number: " + t, 14, 250, 50+(20*t), PDF_RED)
	next
	
	pdf_add_text(pdf, NULL, "I LOVE PROGRAMMING!", 48, 30, 700,PDF_BLUE)
	
	pdf_save(pdf, cPDFFileName)
	pdf_destroy(pdf)
	
	system(cPDFFileName)

Output:

.. image:: PDFGen.png
	:alt: PDFGen



.. index:: 
	pair: Using RingPDFGen; Constants

Constants
=========

.. code-block:: none

	IMAGE_PNG
	IMAGE_JPG
	IMAGE_PPM
	IMAGE_BMP
	IMAGE_UNKNOWN

	PNG_COLOR_GREYSCALE
	PNG_COLOR_RGB
	PNG_COLOR_INDEXED
	PNG_COLOR_GREYSCALE_A
	PNG_COLOR_RGBA
	PNG_COLOR_INVALID

	PPM_BINARY_COLOR_RGB
	PPM_BINARY_COLOR_GRAY

	PDF_LETTER_WIDTH
	PDF_LETTER_HEIGHT
	PDF_A4_WIDTH
	PDF_A4_HEIGHT
	PDF_A3_WIDTH
	PDF_A3_HEIGHT
	PDF_RED
	PDF_GREEN
	PDF_BLUE
	PDF_BLACK
	PDF_WHITE
	PDF_TRANSPARENT
	PDF_ALIGN_LEFT
	PDF_ALIGN_RIGHT
	PDF_ALIGN_CENTER
	PDF_ALIGN_JUSTIFY
	PDF_ALIGN_JUSTIFY_ALL
	PDF_ALIGN_NO_WRITE

.. index:: 
	pair: Using RingPDFGen; Functions

Functions
=========

.. code-block:: none


	struct pdf_doc *pdf_create@2(float width, float height,const struct pdf_info *info)
	void pdf_destroy(struct pdf_doc *pdf)

	const char *pdf_get_err(const struct pdf_doc *pdf, int *errval)
	void pdf_clear_err(struct pdf_doc *pdf)

	int pdf_set_font(struct pdf_doc *pdf, const char *font)
	int pdf_get_font_text_width(struct pdf_doc *pdf, const char *font_name,const char *text, float size, float *text_width);

	float pdf_height(const struct pdf_doc *pdf)
	float pdf_width(const struct pdf_doc *pdf)
	float pdf_page_height(const struct pdf_object *page)
	float pdf_page_width(const struct pdf_object *page)
	struct pdf_object *pdf_append_page(struct pdf_doc *pdf)
	struct pdf_object *pdf_get_page(struct pdf_doc *pdf, int page_number)
	int pdf_page_set_size(struct pdf_doc *pdf, struct pdf_object *page,float width, float height)

	int pdf_save(struct pdf_doc *pdf, const char *filename)
	int pdf_save_file(struct pdf_doc *pdf, FILE *fp)

	int pdf_add_text(struct pdf_doc *pdf, struct pdf_object *page,const char *text, float size, float xoff, float yoff,uint32_t colour)
	int pdf_add_text_rotate(struct pdf_doc *pdf, struct pdf_object *page,const char *text, float size, float xoff, float yoff,float angle, uint32_t colour)
	int pdf_add_text_wrap(struct pdf_doc *pdf, struct pdf_object *page,const char *text, float size, float xoff, float yoff,float angle, uint32_t colour, float wrap_width,int align, float *height)

	int pdf_add_line(struct pdf_doc *pdf, struct pdf_object *page, float x1,float y1, float x2, float y2, float width, uint32_t colour)
	int pdf_add_cubic_bezier(struct pdf_doc *pdf, struct pdf_object *page,float x1, float y1, float x2, float y2, float xq1,float yq1, float xq2, float yq2, float width,uint32_t colour)
	int pdf_add_quadratic_bezier(struct pdf_doc *pdf, struct pdf_object *page,float x1, float y1, float x2, float y2,float xq1, float yq1, float width,uint32_t colour)
	int pdf_add_custom_path(struct pdf_doc *pdf, struct pdf_object *page,const struct pdf_path_operation *operations,int operation_count, float stroke_width,uint32_t stroke_colour, uint32_t fill_colour)
	int pdf_add_ellipse(struct pdf_doc *pdf, struct pdf_object *page, float x,float y, float xradius, float yradius, float width,uint32_t colour, uint32_t fill_colour)
	int pdf_add_circle(struct pdf_doc *pdf, struct pdf_object *page, float x,float y, float radius, float width, uint32_t colour,uint32_t fill_colour)
	int pdf_add_rectangle(struct pdf_doc *pdf, struct pdf_object *page, float x,float y, float width, float height, float border_width,uint32_t colour)
	int pdf_add_filled_rectangle(struct pdf_doc *pdf, struct pdf_object *page,float x, float y, float width, float height,float border_width, uint32_t colour_fill,uint32_t colour_border)

	int pdf_add_bookmark(struct pdf_doc *pdf, struct pdf_object *page, int parent,const char *name)
	int pdf_add_link(struct pdf_doc *pdf, struct pdf_object *page, float x,float y, float width, float height,struct pdf_object *target_page, float target_x,float target_y)
	int pdf_add_barcode(struct pdf_doc *pdf, struct pdf_object *page, int code,float x, float y, float width, float height,const char *string, uint32_t colour)
	int pdf_add_image_data(struct pdf_doc *pdf, struct pdf_object *page, float x,float y, float display_width, float display_height,const uint8_t *data, size_t len)
	int pdf_add_rgb24(struct pdf_doc *pdf, struct pdf_object *page, float x,float y, float display_width, float display_height,const uint8_t *data, uint32_t width, uint32_t height)
	int pdf_add_grayscale8(struct pdf_doc *pdf, struct pdf_object *page, float x,float y, float display_width, float display_height,const uint8_t *data, uint32_t width, uint32_t height)
	int pdf_add_image_file(struct pdf_doc *pdf, struct pdf_object *page, float x,float y, float display_width, float display_height,const char *image_filename)
	int pdf_parse_image_header(struct pdf_img_info *info, const uint8_t *data,size_t length, char *err_msg,size_t err_msg_length)
	uint32_t PDF_RGB(int r, int g, int b)                                                    
	uint32_t PDF_ARGB(int r, int g, int b, int a)   