load "stdlib.ring"
load "stdlibcore.ring"

load "../utils/data_utils.ring"
load "DataValidator.ring"
load "DataTransformer.ring"
load "DataOptimizer.ring"
load "CacheManager.ring"
load "data_extractor.ring"

# تشغيل البرنامج
func main {
    processor = new DataProcessor()
    
    processor.utils.debug("بدء معالجة البيانات...")
    
    # بدء المعالجة
    processor.processFiles()

    ? len(processor.translationPairs) ? processor.translationPairs[1] ? nl
    ? len(processor.codeExamples)  
    //for i in processor.codeExamples   ? i + nl next

    processor.utils.debug("حفظ البيانات المعالجة...")
    if processor.saveProcessedData() {
        processor.utils.debug("تمت المعالجة بنجاح!")
    else
        processor.utils.debug("حدث خطأ أثناء المعالجة.")
    }
}

Class DataProcessor {
    validator
    transformer
    optimizer
    cache
    translationPairs
    codeExamples
    processedDir
    batchSize
    utils
    
    func init {
        validator = new DataValidator
        transformer = new DataTransformer
        optimizer = new DataOptimizer()
        cache = new CacheManager()
        utils = new DataUtils
        
        translationPairs = []
        codeExamples = []
        batchSize = 32
        
        # إنشاء المجلدات إذا لم تكن موجودة
        processedDir = "../../processed/"
        if !utils.isDirectory(processedDir) {
            system("mkdir " + processedDir)
        }
    }
    
    func setBatchSize(size) {
        if size > 0 {
            batchSize = size
        }
    }
    
    func processFiles {
        # معالجة ملفات الترجمة
        processDirectory("../../data/Translation", "translation")
        
        # معالجة ملفات الكود
        processDirectory("../../data/sources", "code")
        
        # تحسين البيانات للتدريب
       // optimizeForTraining()
    }
    
    func processDirectory(cDir, cType) {
        if ! utils.isDirectory(cDir) {
            system("mkdir " + cDir)
            utils.debug("تم إنشاء المجلد: " + cDir)
        }
        
        aFiles = dir(cDir)
        currentBatch = []
        
        for file in aFiles {
            if right(file[1], 4) = ".txt" {
                currentBatch + (cDir + "/" + file[1])
                
                # معالجة الدفعة عندما تمتلئ
                if len(currentBatch) >= batchSize {
                    processBatch(currentBatch, cType)
                    currentBatch = []
                }
            }
        }
        
        # معالجة الدفعة المتبقية
        if len(currentBatch) > 0 {
            processBatch(currentBatch, cType)
        }
    }
    
    func processBatch(files, cType) {
        for filePath in files {
            if cType = "translation" {
                processTranslationFile(filePath)
            else
                processCodeFile(filePath)
            }
        }
        
        # تحسين الذاكرة
        //gc()
    }
    
    func processTranslationFile(filePath) {
        # التحقق من الذاكرة المؤقتة
        cached = cache.getKey(filePath)
        if cached != null {
            for pair in cached {
                translationPairs + pair
            }
            utils.debug("تمت معالجة ملف ترجمة (مخزن مؤقت): " + filePath)
            return
        }
        
        # قراءة وتحليل الملف
        content = read(filePath)
        pairs = []
        
        lines = str2list(content)
        for line in lines {
            if line != "" {
                parts = split(line, char(9))
                if len(parts) = 2 {
                    # التحقق والتحويل
                    if validator.validateTranslation(parts[1], parts[2]) {
                        transformed = transformer.transformTranslation(parts[1], parts[2])
                        if len(transformed) = 2 {
                            optimized = optimizer.optimize(transformed)
                            pairs + optimized
                        }
                    }
                }
            }
        }
        
        # تخزين النتائج
        cache.setKey(filePath, pairs)
        
        for pair in pairs {
            translationPairs + pair
        }
        
        utils.debug("تمت معالجة ملف ترجمة: " + filePath)
    }
    
    func processCodeFile(filePath) {
        # التحقق من الذاكرة المؤقتة
        cached = cache.getKey(filePath)
        if cached != null {
            for example in cached {
                codeExamples + example
            }
            utils.debug("تمت معالجة ملف كود (مخزن مؤقت): " + filePath)
            return
        }
        
        # قراءة وتحليل الملف
        content = read(filePath)
        examples = []
        
        lines = str2list(content)
        currentCode = ""
        currentComment = ""
        
        for line in lines {
            line = trim(line)
            if left(line, 2) = "//" or left(line, 1) = "#" {
                if currentComment != "" {
                    currentComment += nl
                }
                currentComment += substr(line, 3)
            else
                if line != "" {
                    if currentCode != "" {
                        currentCode += nl
                    }
                    currentCode += line
                else
                    if currentCode != "" and currentComment != "" {
                        # التحقق والتحويل
                        if validator.validateCodeExample(currentCode, currentComment) {
                            transformed = transformer.transformCodeExample(currentCode, currentComment)
                            if len(transformed) = 2 {
                                optimized = optimizer.optimize(transformed)
                                examples + optimized
                            }
                        }
                        currentCode = ""
                        currentComment = ""
                    }
                }
            }
        }
        
        # تخزين النتائج
        cache.setKey(filePath, examples)
        
        for example in examples {
            codeExamples + example
        }
        
        utils.debug("تمت معالجة ملف كود: " + filePath)
    }
    
    func optimizeForTraining {
        # تنظيف وتحسين البيانات
        translationPairs = optimizer.cleanAndOptimize(translationPairs)
        codeExamples = optimizer.optimizeCodeExamples(codeExamples)
        
        # إضافة معلومات التموضع للترانسفورمر
        translationPairs = addPositionalInfo(translationPairs)
        codeExamples = addPositionalInfo(codeExamples)
    }
    
    func addPositionalInfo(data) {
        result = []
        for i = 1 to len(data) {
            item = data[i]
            item["position"] = i
            item["total_length"] = len(data)
            result + item
        }
        return result
    }
    
    func saveProcessedData {
        try {
            # حفظ أزواج الترجمة
            translationFile = processedDir + "translations.txt"
            translationContent = ""
            
            for i = 1 to len(translationPairs) step 2 {
                translationContent += translationPairs[i] + char(9) + 
                                    translationPairs[i+1] + nl
            }
            
            write(translationFile, translationContent)
            
            # حفظ أمثلة الكود
            codeFile = processedDir + "code_examples.txt"
            codeContent = ""
            
            for i = 1 to len(codeExamples) step 2 {
                codeContent += "# Code" + nl +
                              codeExamples[i] + nl +
                              "# Comment" + nl +
                              codeExamples[i+1] + nl + nl
            }
            
            write(codeFile, codeContent)
            
            return true
        catch
            return false
        }
    }
}

/*
func processCodeFile(filePath) {
        if !fexists(filePath) {
            utils.debug("خطأ: ملف الكود غير موجود: " + filePath)
            return []
        }

        # التحقق من الذاكرة المؤقتة
        cached = cache.getKey(filePath)
        if cached != null and len(cached) > 0 {
            utils.debug("تم استرجاع " + len(cached) + " مثال من الذاكرة المؤقتة")
            return cached
        }

        examples = []
        content = read(filePath)
        
        if len(content) = 0 {
            utils.debug("تحذير: ملف الكود فارغ: " + filePath)
            return []
        }

        lines = str2list(content)
        isInCodeBlock = false
        currentExample = ""

        for line in lines {
            line = trim(line)
            
            # بداية كتلة الكود
            if substr(line, ".. code-block:: ring") {
                isInCodeBlock = true
                currentExample = ""
                loop
            }
            
            # نهاية كتلة الكود (سطر فارغ)
            if isInCodeBlock and trim(line) = "" {
                if len(currentExample) > 0 {
                    add(examples, currentExample)
                    currentExample = ""
                }
                isInCodeBlock = false
                loop
            }
            
            # إضافة سطر إلى المثال الحالي
            if isInCodeBlock {
                currentExample += line + nl
            }
        }

        # حفظ الأمثلة في الذاكرة المؤقتة
        if len(examples) > 0 {
            cache.setKey(filePath, examples)
            utils.debug("تم استخراج " + len(examples) + " مثال من الملف: " + filePath)
        else
            utils.debug("لم يتم العثور على أمثلة في الملف: " + filePath)
        }

        return examples
    }
    