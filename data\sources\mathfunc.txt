.. index:: 
	single: Mathematical Functions; Introduction

======================
Mathematical Functions
======================

In this chapter we are going to learn about the mathematical functions

.. index:: 
	pair: Mathematical Functions; list of functions

List of functions
=================

The Ring programming language comes with the next mathematical functions

===============		=============================================================================
Function		Description
===============		=============================================================================
sin(x)			Returns the sine of an angle of x radians
cos(x)			Returns the cosine of an angle of x radians
tan(x)			Returns the tangent of an angle of x radians
asin(x)			Returns the principal value of the arc sine of x, expressed in radians
acos(x)			Returns the principal value of the arc cosine of x, expressed in radians
atan(x)			Returns the principal value of the arc tangent of x, expressed in radians
atan2(y,x)		Returns the principal arc tangent of y/x, in the interval [-pi,+pi] radians
sinh(x)			Returns the hyperbolic sine of x radians
cosh(x)			Returns the hyperbolic cosine of x radians
tanh(x)			Returns the hyperbolic tangent of x radians
exp(x)			Returns the value of e raised to the xth power
log(x)			Returns the natural logarithm of x (the base of e)
log(x,b)		Returns the logarithm of x to the base of b
log10(x)		Returns the common logarithm (base-10 logarithm) of x
ceil(x)			Returns the smallest integer value greater than or equal to x	
floor(x)		Returns the largest integer value less than or equal to x
fabs(x)			Returns the absolute value of x.
pow(x,y)		Returns x raised to the power of y 
sqrt(x)			Returns the square root of x
random(x)		Returns a random number in the range [0,x] or [0,-x]
srandom(x)		Initialize random number generator
unsigned(n,n,c)		Perform operation using unsigned numbers 
decimals(n)		Determine the decimals digits after the point in float/double numbers
=============== 	=============================================================================

.. index:: 
	pair: Mathematical Functions; Example

Example
========

.. code-block:: ring

	See "Mathematical Functions" + nl
	See "Sin(0) = " + sin(0) + nl
	See "Sin(90) radians = " + sin(90) + nl
	See "Sin(90) degree = " + sin(90*3.14/180) + nl

	See "Cos(0) = " + cos(0) + nl
	See "Cos(90) radians = " + cos(90) + nl
	See "Cos(90) degree = " + cos(90*3.14/180) + nl

	See "Tan(0) = " + tan(0) + nl
	See "Tan(90) radians = " + tan(90) + nl
	See "Tan(90) degree = " + tan(90*3.14/180) + nl

	See "asin(0) = " + asin(0) + nl
	See "acos(0) = " + acos(0) + nl
	See "atan(0) = " + atan(0) + nl
	See "atan2(1,1) = " + atan2(1,1) + nl

	See "sinh(0) = " + sinh(0) + nl
	See "sinh(1) = " + sinh(1) + nl
	See "cosh(0) = " + cosh(0) + nl
	See "cosh(1) = " + cosh(1) + nl
	See "tanh(0) = " + tanh(0) + nl
	See "tanh(1) = " + tanh(1) + nl

	See "exp(0) = " + exp(0) + nl
	See "exp(1) = " + exp(1) + nl
	See "log(1) = " + log(1) + nl
	See "log(2) = " + log(2) + nl
	See "log10(1) = " + log10(1) + nl
	See "log10(2) = " + log10(2) + nl
	See "log10(10) = " + log10(10) + nl

	See "Ceil(1.12) = " + Ceil(1.12) + nl
	See "Ceil(1.72) = " + Ceil(1.72) + nl

	See "Floor(1.12) = " + floor(1.12) + nl
	See "Floor(1.72) = " + floor(1.72) + nl

	See "fabs(1.12) = " + fabs(1.12) + nl
	See "fabs(1.72) = " + fabs(1.72) + nl

	See "pow(2,3) = " + pow(2,3) + nl

	see "sqrt(16) = " + sqrt(16) + nl


Program Output:

.. code-block:: ring

	Mathematical Functions
	Sin(0) = 0
	Sin(90) radians = 0.89
	Sin(90) degree = 1.00
	Cos(0) = 1
	Cos(90) radians = -0.45
	Cos(90) degree = 0.00
	Tan(0) = 0
	Tan(90) radians = -2.00
	Tan(90) degree = 1255.77
	asin(0) = 0
	acos(0) = 1.57
	atan(0) = 0
	atan2(1,1) = 0.79
	sinh(0) = 0
	sinh(1) = 1.18
	cosh(0) = 1
	cosh(1) = 1.54
	tanh(0) = 0
	tanh(1) = 0.76
	exp(0) = 1
	exp(1) = 2.72
	log(1) = 0
	log(2) = 0.69
	log10(1) = 0
	log10(2) = 0.30
	log10(10) = 1
	Ceil(1.12) = 2
	Ceil(1.72) = 2
	Floor(1.12) = 1
	Floor(1.72) = 1
	fabs(1.12) = 1.12
	fabs(1.72) = 1.72
	pow(2,3) = 8
	sqrt(16) = 4

.. index:: 
	pair: Mathematical Functions; Random()

Random() Function
=================

The Random() function generate a random number and we can set the maximum value (optional).

Syntax:

.. code-block:: ring

	Random(x) ---> Random number in the range [0,x]

Example:

.. code-block:: ring

	for x = 1 to 20
		see  "Random number : " + random() + nl +
		     "Random number Max (100) : " + random(100) + nl
	next

Program Output:

.. code-block:: ring

	Random number : 31881
	Random number Max (100) : 80
	Random number : 5573
	Random number Max (100) : 63
	Random number : 2231
	Random number Max (100) : 43
	Random number : 12946
	Random number Max (100) : 39
	Random number : 22934
	Random number Max (100) : 48
	Random number : 4690
	Random number Max (100) : 52
	Random number : 13196
	Random number Max (100) : 65
	Random number : 30390
	Random number Max (100) : 87
	Random number : 4327
	Random number Max (100) : 77
	Random number : 12456
	Random number Max (100) : 17
	Random number : 28438
	Random number Max (100) : 13
	Random number : 30503
	Random number Max (100) : 6
	Random number : 31769
	Random number Max (100) : 94
	Random number : 8274
	Random number Max (100) : 65
	Random number : 14390
	Random number Max (100) : 90
	Random number : 28866
	Random number Max (100) : 12
	Random number : 24558
	Random number Max (100) : 70
	Random number : 29981
	Random number Max (100) : 77
	Random number : 12847
	Random number Max (100) : 63
	Random number : 6632
	Random number Max (100) : 60

.. index:: 
	pair: Mathematical Functions; SRandom()

SRandom() Function
==================

The SRandom() function initialize random number generator.

Syntax:

.. code-block:: ring

	SRandom(x) 

.. index:: 
	pair: Mathematical Functions; Unsigned()

Unsigned() Function
===================

We can use unsigned numbers using the Unsigned() function.

Syntax:

.. code-block:: ring

	Unsigned(nNum1,nNum2,cOperator)	--> result of cOperator operation on nNum1,nNum2

Example:

.. code-block:: ring

	see oat_hash("hello") + nl

	# Jenkins hash function - https://en.wikipedia.org/wiki/Jenkins_hash_function
	func oat_hash cKey	
		h = 0
		for x in cKey
			h = unsigned(h,ascii(x),"+")
			h = unsigned(h,unsigned(h,10,"<<"),"+")
			r = unsigned(h,6,">>")
			h = unsigned(h, r,"^")
		next
		h = unsigned(h,unsigned(h,3,"<<"),"+")
		h = unsigned(h,unsigned(h,11,">>"),"^")
		h = unsigned(h,unsigned(h,15,"<<"),"+")
		return h  

Output:

.. code-block:: ring

	3372029979.00

.. index:: 
	pair: Mathematical Functions; Decimals()

Decimals() Functions
====================

We can determine the decimals numbers count after the point in float/double numbers using the decimals() function.

Syntax:

.. code-block:: ring

	Decimals(nDecimalsCount)

Example:

.. code-block:: ring

	x = 1.*********0123
	for d = 0 to 14
		decimals(d)
		see x + nl
	next


Output:

.. code-block:: ring


	1
	1.1
	1.12
	1.123
	1.1235
	1.12346
	1.123457
	1.1234568
	1.12345679
	1.*********
	1.*********0
	1.*********01
	1.*********012
	1.*********0123
	1.*********01230


.. index:: 
	pair: Mathematical Functions; Using _ in numbers

Using _ in numbers
==================

We can use _ between numbers digits.

Example:

.. code-block:: ring

	x = 1_000_000
	see type(x)+nl
	see x+1+nl

Output:

.. code-block:: ring

	NUMBER
	*********

.. index:: 
	pair: Mathematical Functions; Using f after numbers

Using f after numbers
=====================

We can use the 'f' letter after numbers.

Example:

.. code-block:: ring

	x = 19.99f
	see type(x) + nl

Output:

.. code-block:: ring

	NUMBER

Using Hexadecimal Numbers
=========================

We can write Hexadecimal number by preceding with "0x" or "0X"

Example:

.. code-block:: ring

	x = 0x10
	? x             # 16
	x = 0xff
	? x             # 255
	x = 0x0A
	? x             # 10
	? 0xFFFF        # 65535
	? 0x0A + 1      # 10+1 = 11

