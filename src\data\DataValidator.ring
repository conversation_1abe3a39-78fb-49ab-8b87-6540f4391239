
Class DataValidator {   

    func validateTranslation(source, target) {
        if !isString(source) or !isString(target) {
            debug("خطأ: النص المصدر أو الهدف غير صالح")
            return false 
        }
        
        # التحقق من الطول
        if !validateLength(source) or !validateLength(target) {
            debug("خطأ: طول النص غير مقبول")
            return false 
        }
        
        # التحقق من عدد الكلمات
        if !validateWordCount(source) or !validateWordCount(target) {
            debug("خطأ: عدد الكلمات غير مقبول")
            return false 
        }
        
        # التحقق من اللغة
        if !validateLanguage(source, "en") or !validateLanguage(target, "ar") {
            debug("خطأ: اللغة غير مدعومة")
            return false
        }
        
        debug("تم التحقق من صحة الترجمة")
        return true
    }

    func validateCodeExample(code, comment) {
        if !isString(code) or !isString(comment) {
            debug("خطأ: الكود أو التعليق غير صالح")
            return false 
        }
        
        # التحقق من الطول
        if !validateLength(code) or !validateLength(comment) {
            debug("خطأ: طول الكود أو التعليق غير مقبول")
            return false 
        }
        
        # التحقق من صحة الكود
        if !validateCodeSyntax(code) {
            debug("خطأ: صيغة الكود غير صحيحة")
            return false 
        }
        
        debug("تم التحقق من صحة مثال الكود")
        return true
    }

    # دالة للتتبع مع التحكم في الطباعة
    func debug(msg) {
        see "[VALIDATOR-DEBUG] " + msg + nl
    }
    private 

    
    minLength = 1
    maxLength = 1000
    minWords = 1
    maxWords = 200
    validLanguages = ["ar", "en"]

    func validateLength(text) {
        len = len(text)
        return len >= minLength and len <= maxLength
    }

    func validateWordCount(text) {
        words = split(text, " ")
        count = len(words)
        return count >= minWords and count <= maxWords
    }

    func validateLanguage(text, lang) {
        if !isString(lang) or !find(validLanguages, lang) return false ok
        
        # هنا يمكن إضافة المزيد من التحقق من اللغة
        # مثل التحقق من وجود حروف خاصة باللغة
        
        return true
    }

    func validateCodeSyntax(code) {
        try {
            # التحقق من توازن الأقواس
            if !checkBrackets(code) return false ok
            
            # التحقق من وجود أخطاء تركيبية واضحة
            if hasObviousSyntaxErrors(code) return false ok
            
            return true
        catch
            return false
        }
    }

    func checkBrackets(code) {
        stack = []
        brackets = [
            ["(" , ")"],
            ["{" , "}" ],
            ["[" , "]" ]
        ]
        
        for c in code {
            if find(brackets[1] , c )  {
                stack + c
            elseif find(brackets[2] , c )
                if len(stack) = 0 return false ok
                
                last = stack[len(stack)]
                if brackets[last] != c return false ok
                
                del(stack, len(stack))
            }
        }
        
        return len(stack) = 0
    }

    func hasObviousSyntaxErrors(code) {
        # قائمة بالكلمات المفتاحية التي يجب أن تتبع بأقواس
        keywords = ["if", "for", "while", "func", "class"]
        
        for keyword in keywords {
            pos = substr(code, keyword)
            if pos > 0 {
                # تخطي المسافات
                while pos <= len(code) and (code[pos] = " " or code[pos] = nl) {
                    pos++
                }
                
                # التحقق من وجود قوس
                if pos > len(code) or (code[pos] != "(" and code[pos] != "{") {
                    return true
                }
            }
        }
        
        return false
    }

}
