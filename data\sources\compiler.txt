.. index:: 
	single: Command Line Options; Introduction

====================
Command Line Options
====================

The ring language takes source code file (*.ring) or the object file (*.ringo) as input to execute, 
also the language provide other options like

============	=======================================================
Option		Description	
============	=======================================================
-tokens  	Print a list of tokens in the source code file
-rules   	Print grammar rules applied on the tokens
-ic 	 	Print the intermediate byte code (before execution)
-icfinal 	Print the final byte code (after execution)
-cgi		Print http response header before error messages
-norun		Don't run the program after compiling
-ins		Print instruction operation code before execution
-clock		Print clock before and after program execution
-go			Generate Object File
-geo		Generate embedded object file (C source code)
-w			Display Warnings 
============	=======================================================

.. index:: 
	pair: Command Line Options; Printing Tokens

Printing Tokens
===============

Example:

.. code-block:: ring

	Func Main
		See "Hello World" + nl
		for x = 1 to 10
			see x + nl
		next
		test()

	func test
		see "welcome" + nl
		o1 = new point { x=10 y=20 z=30 }
		see o1

	class point x y z

Command:

.. code-block:: ring

	ring test.ring -tokens -norun

Output:

.. code-block:: ring

	===================================================
	Tokens - Generated by the Scanner
	===================================================

	   Keyword : FUNC
	Identifier : main
	   EndLine
	   Keyword : SEE
	   Literal : Hello World
	  Operator : +
	Identifier : nl
	   EndLine
	   Keyword : FOR
	Identifier : x
	  Operator : =
	    Number : 1
	   Keyword : TO
	    Number : 10
	   EndLine
	   Keyword : SEE
	Identifier : x
	  Operator : +
	Identifier : nl
	   EndLine
	   Keyword : NEXT
	   EndLine
	Identifier : test
	  Operator : (
	  Operator : )
	   EndLine
	   Keyword : FUNC
	Identifier : test
	   EndLine
	   Keyword : SEE
	   Literal : welcome
	  Operator : +
	Identifier : nl
	   EndLine
	Identifier : o1
	  Operator : =
	   Keyword : NEW
	Identifier : point
	  Operator : {
	Identifier : x
	  Operator : =
	    Number : 10
	Identifier : y
	  Operator : =
	    Number : 20
	Identifier : z
	  Operator : =
	    Number : 30
	  Operator : }
	   EndLine
	   Keyword : SEE
	Identifier : o1
	   EndLine
	   Keyword : CLASS
	Identifier : point
	Identifier : x
	Identifier : y
	Identifier : z
	   EndLine

	===================================================

.. index:: 
	pair: Command Line Options; Printing Rules

Printing Rules
==============

Command:

.. code-block:: ring

	ring test.ring -rules -norun

Output:

.. code-block:: ring

	===================================================
	Grammar Rules Used by The Parser
	===================================================

	Rule : Program --> {Statement}

	Line 1
	Rule : Statement  --> 'Func' Identifier [ParaList]

	Line 2
	Rule : Factor --> Literal
	Rule : Range --> Factor
	Rule : Term --> Range
	Rule : Arithmetic --> Term
	Rule : Factor --> Identifier [ {Mixer} | Assignment | PlusPlus | MinusMinus]
	Rule : Range --> Factor
	Rule : Term --> Range
	Rule : Arithmetic --> Term
	Rule : Arithmetic --> Arithmetic + Arithmetic
	Rule : BitShift --> Arithmetic
	Rule : BitAnd --> BitShift
	Rule : BitOrXOR -->  BitAnd
	Rule : Compare --> BitOrXOR
	Rule : EqualOrNot --> Compare
	Rule : LogicNot -> EqualOrNot
	Rule : Expr --> LogicNot
	Rule : Statement  --> 'See' Expr

	Line 3
	Rule : Factor --> Number
	Rule : Range --> Factor
	Rule : Term --> Range
	Rule : Arithmetic --> Term
	Rule : BitShift --> Arithmetic
	Rule : BitAnd --> BitShift
	Rule : BitOrXOR -->  BitAnd
	Rule : Compare --> BitOrXOR
	Rule : EqualOrNot --> Compare
	Rule : LogicNot -> EqualOrNot
	Rule : Expr --> LogicNot
	Rule : Factor --> Number
	Rule : Range --> Factor
	Rule : Term --> Range
	Rule : Arithmetic --> Term
	Rule : BitShift --> Arithmetic
	Rule : BitAnd --> BitShift
	Rule : BitOrXOR -->  BitAnd
	Rule : Compare --> BitOrXOR
	Rule : EqualOrNot --> Compare
	Rule : LogicNot -> EqualOrNot
	Rule : Expr --> LogicNot
	Rule : Statement  --> 'For' Identifier '=' Expr to Expr ['step' Expr]

	Line 4
	Rule : Factor --> Identifier [ {Mixer} | Assignment | PlusPlus | MinusMinus]
	Rule : Range --> Factor
	Rule : Term --> Range
	Rule : Arithmetic --> Term
	Rule : Factor --> Identifier [ {Mixer} | Assignment | PlusPlus | MinusMinus]
	Rule : Range --> Factor
	Rule : Term --> Range
	Rule : Arithmetic --> Term
	Rule : Arithmetic --> Arithmetic + Arithmetic
	Rule : BitShift --> Arithmetic
	Rule : BitAnd --> BitShift
	Rule : BitOrXOR -->  BitAnd
	Rule : Compare --> BitOrXOR
	Rule : EqualOrNot --> Compare
	Rule : LogicNot -> EqualOrNot
	Rule : Expr --> LogicNot
	Rule : Statement  --> 'See' Expr

	Line 5
	Rule : Next --> 'Next'

	Line 6
	Rule : Mixer -> '(' [Expr { ',' Expr} ] ')'

	Line 8
	Rule : Factor --> Identifier [ {Mixer} | Assignment | PlusPlus | MinusMinus]
	Rule : Range --> Factor
	Rule : Term --> Range
	Rule : Arithmetic --> Term
	Rule : BitShift --> Arithmetic
	Rule : BitAnd --> BitShift
	Rule : BitOrXOR -->  BitAnd
	Rule : Compare --> BitOrXOR
	Rule : EqualOrNot --> Compare
	Rule : LogicNot -> EqualOrNot
	Rule : Expr --> LogicNot
	Rule : Statement  --> Expr
	Rule : Statement  --> 'Func' Identifier [ParaList]

	Line 9
	Rule : Factor --> Literal
	Rule : Range --> Factor
	Rule : Term --> Range
	Rule : Arithmetic --> Term
	Rule : Factor --> Identifier [ {Mixer} | Assignment | PlusPlus | MinusMinus]
	Rule : Range --> Factor
	Rule : Term --> Range
	Rule : Arithmetic --> Term
	Rule : Arithmetic --> Arithmetic + Arithmetic
	Rule : BitShift --> Arithmetic
	Rule : BitAnd --> BitShift
	Rule : BitOrXOR -->  BitAnd
	Rule : Compare --> BitOrXOR
	Rule : EqualOrNot --> Compare
	Rule : LogicNot -> EqualOrNot
	Rule : Expr --> LogicNot
	Rule : Statement  --> 'See' Expr

	Line 10
	Rule : Factor --> New Identifier {'.' Identifier }
	Rule : Mixer --> '{' {Statement} BraceEnd
	Rule : Factor --> Number
	Rule : Range --> Factor
	Rule : Term --> Range
	Rule : Arithmetic --> Term
	Rule : BitShift --> Arithmetic
	Rule : BitAnd --> BitShift
	Rule : BitOrXOR -->  BitAnd
	Rule : Compare --> BitOrXOR
	Rule : EqualOrNot --> Compare
	Rule : LogicNot -> EqualOrNot
	Rule : Expr --> LogicNot
	Rule : Assignment -> '=' Expr
	Rule : Factor --> Identifier [ {Mixer} | Assignment | PlusPlus | MinusMinus]
	Rule : Range --> Factor
	Rule : Term --> Range
	Rule : Arithmetic --> Term
	Rule : BitShift --> Arithmetic
	Rule : BitAnd --> BitShift
	Rule : BitOrXOR -->  BitAnd
	Rule : Compare --> BitOrXOR
	Rule : EqualOrNot --> Compare
	Rule : LogicNot -> EqualOrNot
	Rule : Expr --> LogicNot
	Rule : Statement  --> Expr
	Rule : Factor --> Number
	Rule : Range --> Factor
	Rule : Term --> Range
	Rule : Arithmetic --> Term
	Rule : BitShift --> Arithmetic
	Rule : BitAnd --> BitShift
	Rule : BitOrXOR -->  BitAnd
	Rule : Compare --> BitOrXOR
	Rule : EqualOrNot --> Compare
	Rule : LogicNot -> EqualOrNot
	Rule : Expr --> LogicNot
	Rule : Assignment -> '=' Expr
	Rule : Factor --> Identifier [ {Mixer} | Assignment | PlusPlus | MinusMinus]
	Rule : Range --> Factor
	Rule : Term --> Range
	Rule : Arithmetic --> Term
	Rule : BitShift --> Arithmetic
	Rule : BitAnd --> BitShift
	Rule : BitOrXOR -->  BitAnd
	Rule : Compare --> BitOrXOR
	Rule : EqualOrNot --> Compare
	Rule : LogicNot -> EqualOrNot
	Rule : Expr --> LogicNot
	Rule : Statement  --> Expr
	Rule : Factor --> Number
	Rule : Range --> Factor
	Rule : Term --> Range
	Rule : Arithmetic --> Term
	Rule : BitShift --> Arithmetic
	Rule : BitAnd --> BitShift
	Rule : BitOrXOR -->  BitAnd
	Rule : Compare --> BitOrXOR
	Rule : EqualOrNot --> Compare
	Rule : LogicNot -> EqualOrNot
	Rule : Expr --> LogicNot
	Rule : Assignment -> '=' Expr
	Rule : Factor --> Identifier [ {Mixer} | Assignment | PlusPlus | MinusMinus]
	Rule : Range --> Factor
	Rule : Term --> Range
	Rule : Arithmetic --> Term
	Rule : BitShift --> Arithmetic
	Rule : BitAnd --> BitShift
	Rule : BitOrXOR -->  BitAnd
	Rule : Compare --> BitOrXOR
	Rule : EqualOrNot --> Compare
	Rule : LogicNot -> EqualOrNot
	Rule : Expr --> LogicNot
	Rule : Statement  --> Expr
	Rule : BraceEnd --> '}'
	Rule : Range --> Factor
	Rule : Term --> Range
	Rule : Arithmetic --> Term
	Rule : BitShift --> Arithmetic
	Rule : BitAnd --> BitShift
	Rule : BitOrXOR -->  BitAnd
	Rule : Compare --> BitOrXOR
	Rule : EqualOrNot --> Compare
	Rule : LogicNot -> EqualOrNot
	Rule : Expr --> LogicNot
	Rule : Assignment -> '=' Expr
	Rule : Factor --> Identifier [ {Mixer} | Assignment | PlusPlus | MinusMinus]
	Rule : Range --> Factor
	Rule : Term --> Range
	Rule : Arithmetic --> Term
	Rule : BitShift --> Arithmetic
	Rule : BitAnd --> BitShift
	Rule : BitOrXOR -->  BitAnd
	Rule : Compare --> BitOrXOR
	Rule : EqualOrNot --> Compare
	Rule : LogicNot -> EqualOrNot
	Rule : Expr --> LogicNot
	Rule : Statement  --> Expr

	Line 11
	Rule : Factor --> Identifier [ {Mixer} | Assignment | PlusPlus | MinusMinus]
	Rule : Range --> Factor
	Rule : Term --> Range
	Rule : Arithmetic --> Term
	Rule : BitShift --> Arithmetic
	Rule : BitAnd --> BitShift
	Rule : BitOrXOR -->  BitAnd
	Rule : Compare --> BitOrXOR
	Rule : EqualOrNot --> Compare
	Rule : LogicNot -> EqualOrNot
	Rule : Expr --> LogicNot
	Rule : Statement  --> 'See' Expr

	Line 13
	Rule : Statement  --> 'Class' Identifier
	Rule : Factor --> Identifier [ {Mixer} | Assignment | PlusPlus | MinusMinus]
	Rule : Range --> Factor
	Rule : Term --> Range
	Rule : Arithmetic --> Term
	Rule : BitShift --> Arithmetic
	Rule : BitAnd --> BitShift
	Rule : BitOrXOR -->  BitAnd
	Rule : Compare --> BitOrXOR
	Rule : EqualOrNot --> Compare
	Rule : LogicNot -> EqualOrNot
	Rule : Expr --> LogicNot
	Rule : Statement  --> Expr
	Rule : Factor --> Identifier [ {Mixer} | Assignment | PlusPlus | MinusMinus]
	Rule : Range --> Factor
	Rule : Term --> Range
	Rule : Arithmetic --> Term
	Rule : BitShift --> Arithmetic
	Rule : BitAnd --> BitShift
	Rule : BitOrXOR -->  BitAnd
	Rule : Compare --> BitOrXOR
	Rule : EqualOrNot --> Compare
	Rule : LogicNot -> EqualOrNot
	Rule : Expr --> LogicNot
	Rule : Statement  --> Expr
	Rule : Factor --> Identifier [ {Mixer} | Assignment | PlusPlus | MinusMinus]
	Rule : Range --> Factor
	Rule : Term --> Range
	Rule : Arithmetic --> Term
	Rule : BitShift --> Arithmetic
	Rule : BitAnd --> BitShift
	Rule : BitOrXOR -->  BitAnd
	Rule : Compare --> BitOrXOR
	Rule : EqualOrNot --> Compare
	Rule : LogicNot -> EqualOrNot
	Rule : Expr --> LogicNot
	Rule : Statement  --> Expr

	===================================================

.. index:: 
	pair: Command Line Options; Printing Intermediate Code

Printing Intermediate Code
==========================

Command:

.. code-block:: ring

	ring test.ring -ic -norun

Output:

.. code-block:: ring

	===========================================================================
	 Byte Code - Before Execution by the VM
	===========================================================================
	
	     PC              OPCode                 Data
	
	      1          ReturnNull
	      2                Func
	      3             NewLine                    2
	      4            LoadFunc           ringvm_see
	      5               PushC          Hello World
	      6               LoadA                   nl
	      7               PushV
	      8                 Sum
	      9                Call                    0
	     10         NoOperation
	     11           FreeStack
	     12             NewLine                    3
	     13           SetOPCode                   72                  34
	     14         NoOperation
	     15          LoadAFirst                    x
	     16               PushN             1.000000
	     17          Assignment
	     18             NewLine                    3
	     19         StepFromReg             1.000000
	     20          LoadAPushV                    x
	     21               PushN            10.000000
	     22             JumpFor                   35
	     23             NewLine                    4
	     24            LoadFunc           ringvm_see
	     25               LoadA                    x
	     26               PushV
	     27               LoadA                   nl
	     28               PushV
	     29                 Sum
	     30                Call                    0
	     31         NoOperation
	     32           FreeStack
	     33             NewLine                    5
	     34             IncJump                    x                  21
	     35         NoOperation                    3
	     36             PopStep
	     37             NewLine                    6
	     38            LoadFunc                 test
	     39                Call                    0
	     40         NoOperation
	     41             NewLine                    8
	     42               PushV
	     43           FreeStack
	     44          ReturnNull
	     45                Func
	     46             NewLine                    9
	     47            LoadFunc           ringvm_see
	     48               PushC              welcome
	     49               LoadA                   nl
	     50               PushV
	     51                 Sum
	     52                Call                    0
	     53         NoOperation
	     54           FreeStack
	     55             NewLine                   10
	     56               LoadA                   o1
	     57   AssignmentPointer                    0                 111
	     58                 New                point                   0
	     59            SetScope
	     60               PushV
	     61          BraceStart
	     62    CheckBraceMethod           bracestart
	     63               JumpZ                   68
	     64            LoadFunc           bracestart
	     65                Call
	     66         NoOperation
	     67               PushV
	     68               LoadA                    x
	     69   AssignmentPointer                    0                  71
	     70               PushN            10.000000
	     71          Assignment
	     72    CheckBraceMethod        braceexpreval
	     73               JumpZ                   79
	     74            LoadFunc        braceexpreval
	     75                 Dup
	     76                Call
	     77         NoOperation
	     78               PushV
	     79           FreeStack
	     80               LoadA                    y
	     81   AssignmentPointer                    0                  83
	     82               PushN            20.000000
	     83          Assignment
	     84    CheckBraceMethod        braceexpreval
	     85               JumpZ                   91
	     86            LoadFunc        braceexpreval
	     87                 Dup
	     88                Call
	     89         NoOperation
	     90               PushV
	     91           FreeStack
	     92               LoadA                    z
	     93   AssignmentPointer                    0                  95
	     94               PushN            30.000000
	     95          Assignment
	     96    CheckBraceMethod        braceexpreval
	     97               JumpZ                  103
	     98            LoadFunc        braceexpreval
	     99                 Dup
	    100                Call
	    101         NoOperation
	    102               PushV
	    103           FreeStack
	    104    CheckBraceMethod             braceend
	    105               JumpZ                  110
	    106            LoadFunc             braceend
	    107                Call
	    108         NoOperation
	    109               PushV
	    110            BraceEnd
	    111          Assignment
	    112           FreeStack
	    113             NewLine                   11
	    114            LoadFunc           ringvm_see
	    115               LoadA                   o1
	    116               PushV
	    117                Call                    0
	    118         NoOperation
	    119           FreeStack
	    120             NewLine                   13
	    121          ReturnNull
	    122               Class                point    000001934ECD9560
	    123            NewLabel
	    124            FileName            test.ring
	    125               LoadA                    x
	    126               PushV
	    127           FreeStack
	    128               LoadA                    y
	    129               PushV
	    130           FreeStack
	    131               LoadA                    z
	    132               PushV
	    133           FreeStack
	    134          ReturnNull
	
	===========================================================================

.. index:: 
	pair: Command Line Options; Printing Final Intermediate Code

Printing Final Intermediate Code
================================

Command:

.. code-block:: ring

	ring test.ring -icfinal

Output:

.. code-block:: ring

	Hello World
	1
	2
	3
	4
	5
	6
	7
	8
	9
	10
	welcome
	x: 10
	y: 20
	z: 30
	
	
	===========================================================================
	 Byte Code - After Execution by the VM
	===========================================================================
	
	     PC              OPCode                 Data
	
	      1          ReturnNull                    0                  0
	      2                Func                    0                  0
	      3             NewLine                    2                  0
	      4           LoadFuncP           ringvm_see   00007FFB57B8C640
	      5               PushC          Hello World                  0
	      6               LoadA                   nl                  0
	      7               PushV                    0                  0
	      8                 Sum                    0                  0
	      9                Call                    0                  0
	     10         NoOperation                    0                  0
	     11           FreeStack                    0                  0
	     12             NewLine                    3                  0
	     13           SetOPCode                   72                 34
	     14         NoOperation                    0                  0
	     15          LoadAFirst                    x                  0
	     16               PushN             1.000000                  0
	     17          Assignment                    0                  0
	     18             NewLine                    3                  0
	     19         StepFromReg             1.000000                  0
	     20          LoadAPushV                    x                  0
	     21               PushN            10.000000                  0
	     22             JumpFor                   35                  1
	     23             NewLine                    4                  0
	     24           LoadFuncP           ringvm_see   00007FFB57B8C640
	     25          PushPLocal                    x   0000024816180C48
	     26               PushV                    0                  0
	     27               LoadA                   nl                  0
	     28               PushV                    0                  0
	     29                 Sum                    0                  0
	     30                Call                    0                  0
	     31         NoOperation                    0                  0
	     32           FreeStack                    0                  0
	     33             NewLine                    5                  0
	     34      IncLPJumpStep1                    x                 21
	     35         NoOperation                    3   0000024816180DA8
	     36             PopStep                    0                  0
	     37             NewLine                    6                  0
	     38           LoadFuncP                 test   0000000000000000
	     39                Call                    0                  0
	     40         NoOperation                    0                  0
	     41             NewLine                    8                  0
	     42               PushV                    0                  0
	     43           FreeStack                    0                  0
	     44          ReturnNull                    0                  0
	     45                Func                    0                  0
	     46             NewLine                    9                  0
	     47           LoadFuncP           ringvm_see   00007FFB57B8C640
	     48               PushC              welcome                  0
	     49               LoadA                   nl                  0
	     50               PushV                    0                  0
	     51                 Sum                    0                  0
	     52                Call                    0                  0
	     53         NoOperation                    0                  0
	     54           FreeStack                    0                  0
	     55             NewLine                   10                  0
	     56               LoadA                   o1                  0
	     57   AssignmentPointer                    0                111
	     58                 New                point                  0
	     59            SetScope                    0                  0
	     60               PushV                    0                  0
	     61          BraceStart                    0                  0
	     62    CheckBraceMethod           bracestart                  0
	     63               JumpZ                   68                  0
	     64            LoadFunc           bracestart                  0
	     65                Call                    0                  0
	     66         NoOperation                    0                  0
	     67               PushV                    0                  0
	     68               LoadA                    x                  0
	     69   AssignmentPointer                    0                 71
	     70               PushN            10.000000                  0
	     71         SetProperty                    0                  0
	     72    CheckBraceMethod        braceexpreval                  0
	     73               JumpZ                   79                  0
	     74            LoadFunc        braceexpreval                  0
	     75                 Dup                    0                  0
	     76                Call                    0                  0
	     77         NoOperation                    0                  0
	     78               PushV                    0                  0
	     79           FreeStack                    0                  0
	     80               LoadA                    y                  0
	     81   AssignmentPointer                    0                 83
	     82               PushN            20.000000                  0
	     83         SetProperty                    0                  0
	     84    CheckBraceMethod        braceexpreval                  0
	     85               JumpZ                   91                  0
	     86            LoadFunc        braceexpreval                  0
	     87                 Dup                    0                  0
	     88                Call                    0                  0
	     89         NoOperation                    0                  0
	     90               PushV                    0                  0
	     91           FreeStack                    0                  0
	     92               LoadA                    z                  0
	     93   AssignmentPointer                    0                 95
	     94               PushN            30.000000                  0
	     95         SetProperty                    0                  0
	     96    CheckBraceMethod        braceexpreval                  0
	     97               JumpZ                  103                  0
	     98            LoadFunc        braceexpreval                  0
	     99                 Dup                    0                  0
	    100                Call                    0                  0
	    101         NoOperation                    0                  0
	    102               PushV                    0                  0
	    103           FreeStack                    0                  0
	    104    CheckBraceMethod             braceend                  0
	    105               JumpZ                  110                  0
	    106            LoadFunc             braceend                  0
	    107                Call                    0                  0
	    108         NoOperation                    0                  0
	    109               PushV                    0                  0
	    110            BraceEnd                    0                  0
	    111          Assignment                    0                  0
	    112           FreeStack                    0                  0
	    113             NewLine                   11                  0
	    114           LoadFuncP           ringvm_see   00007FFB57B8C640
	    115               LoadA                   o1                  0
	    116               PushV                    0                  0
	    117                Call                    0                  0
	    118         NoOperation                    0                  0
	    119           FreeStack                    0                  0
	    120             NewLine                   13                  0
	    121          ReturnNull                    0                  0
	    122               Class                point   0000024815FA1D50
	    123            NewLabel                    0                  0
	    124            FileName            test.ring                  0
	    125               LoadA                    x                  0
	    126               PushV                    0                  0
	    127           FreeStack                    0                  0
	    128               LoadA                    y                  0
	    129               PushV                    0                  0
	    130           FreeStack                    0                  0
	    131               LoadA                    z                  0
	    132               PushV                    0                  0
	    133           FreeStack                    0                  0
	    134          ReturnNull                    0                  0
	
	===========================================================================

.. index:: 
	pair: Command Line Options; CGI Support


CGI Support
===========

Command:

.. code-block:: ring

	ring test.ring -cgi

.. index:: 
	pair: Command Line Options; No Run

No Run 
======

Command:

.. code-block:: ring

	ring test.ring -norun

.. index:: 
	pair: Command Line Options; Printing Instruction Operation Code

Printing Instruction Operation Code
===================================

Command:

.. code-block:: ring

	ring test.ring -ins


Output:

.. code-block:: ring

	===========================================================================
	VM Pointer    : 00000273A5CAACE0
	VM IR Pointer : 00000273A5D543C0
	Operation     : ReturnNull
	PC            : 1
	Scopes Count  : 1
	Scope Pointer : 00000273A5CCA4E0
	File Name     : test.ring
	Line Number   : 1
	
	SP (After)    : 0
	FuncSP        : 0
	LineNumber    : 1
	===========================================================================
	.....
	.....
	.....
	
.. tip:: Output removed from the previous example because it's very large!

.. index:: 
	pair: Command Line Options; Clock

Clock
===========

Command:

.. code-block:: ring

	ring test.ring -clock

Output:

.. code-block:: ring

	===================================================
	Date  : 2024/08/19 Time : 20:52:39
	Clock : 0 
	===================================================
	Hello World
	1
	2
	3
	4
	5
	6
	7
	8
	9
	10
	welcome
	x: 10
	y: 20
	z: 30

	===================================================
	Date  : 2024/08/19 Time : 20:52:39
	Clock : 0 
	===================================================

.. index:: 
	pair: Command Line Options; Generate Object File

Generate Object File
====================

You can generate object file (*.ringo) from your source code file (*.ring) 
using -go option

.. tip::

	You will get one object file to use for distributing/running your application which may contains one or many
	ring source files that you can keep or distribute based on the application (commercial or open source).

Command:

.. code-block:: ring

	ring test.ring -go

To run the compiled object file

.. code-block:: ring

	ring test.ringo


.. index:: 
	pair: Command Line Options; Generate Embedded Object File

Generate Embedded Object File
=============================

You can generate embedded object file (C source code) from your source code file (*.ring) 
using -geo option

Command:

.. code-block:: ring

	ring test.ring -geo

This command will generate at least three files

.. code-block:: ring

	test.c
	ringappcode.c
	ringappcode.h

More files could be generated based on the project size

The generated files will pass the byte code to Ring VM to be executed
