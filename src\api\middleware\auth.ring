load "../../core/jwt.ring"
load "../config.ring"

class AuthMiddleware
    jwt
    config
    
    func init
        jwt = new JWT
        config = new APIConfig
    
    func authenticate request
        try
            # استخراج التوكن من الترويسة
            token = request.headers["Authorization"]
            if not token return false ok
            
            # التحقق من صحة التوكن
            token = substr(token, 7)  # إزالة "Bearer "
            payload = jwt.verify(token, config.JWT_SECRET)
            
            # التحقق من انتهاء الصلاحية
            if payload[:exp] < time() return false ok
            
            # إضافة معلومات المستخدم للطلب
            request.user = payload
            return true
            
        catch
            return false
        done
    
    func generateToken user
        payload = [
            :user_id = user[:id],
            :username = user[:username],
            :role = user[:role],
            :exp = time() + config.JWT_EXPIRY
        ]
        return jwt.sign(payload, config.JWT_SECRET)
    
    func requireRole roles
        return func(request)
            if not request.user return false ok
            return roles.contains(request.user[:role])
        end
