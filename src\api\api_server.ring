load "httplib.ring"
load "../core/transformer.ring"
load "../core/parallel_processor.ring"

# خادم API
Class APIServer {
    # المتغيرات
    server
    transformer
    processor
    
    func init {
        transformer = new SmartTransformer()
        processor = new ParallelProcessor(4)
        server = new Server()
        
        setupRoutes()
    }
    
    # إعداد المسارات
    func setupRoutes {
        # مسارات الترجمة
        server.route(:Post, "/api/translate", :handleTranslate)
        server.route(:Get, "/api/translate/status/:id", :handleTranslateStatus)
        
        # مسارات توليد الكود
        server.route(:Post, "/api/generate", :handleGenerate)
        server.route(:Get, "/api/generate/status/:id", :handleGenerateStatus)
        
        # مسارات التدريب
        server.route(:Post, "/api/train", :handleTrain)
        server.route(:Get, "/api/train/status", :handleTrainStatus)
        
        # مسارات النموذج
        server.route(:Get, "/api/model/info", :handleModelInfo)
        server.route(:Post, "/api/model/save", :handleModelSave)
        server.route(:Post, "/api/model/load", :handleModelLoad)
    }
    
    # تشغيل الخادم
    func start {
        ? "بدء تشغيل خادم API على المنفذ 8080..."
        server.listen("0.0.0.0", 8080)
    }
    
    # معالجة طلب الترجمة
    func handleTranslate {
        try {
            # استخراج البيانات من الطلب
            data = json2list(server.request.body)
            
            # التحقق من البيانات
            if not data["text"] {
                return sendError("النص مطلوب")
            }
            
            # إنشاء مهمة ترجمة
            task = new Task {
                type = "translation"
                data = data["text"]
                direction = data["direction"] ? data["direction"] : "ar-en"
            }
            
            # إضافة المهمة للمعالج
            taskId = processor.addTask(task)
            
            # إرسال معرف المهمة
            response = [
                "task_id": taskId,
                "status": "pending"
            ]
            
            server.setContent(list2json(response), "application/json")
        catch
            sendError(cCatchError)
        }
    }
    
    # معالجة طلب حالة الترجمة
    func handleTranslateStatus {
        try {
            taskId = server.params["id"]
            
            # الحصول على حالة المهمة
            task = processor.getTask(taskId)
            if not task {
                return sendError("مهمة غير موجودة")
            }
            
            # إعداد الاستجابة
            response = [
                "task_id": taskId,
                "status": task.status,
                "result": task.result
            ]
            
            server.setContent(list2json(response), "application/json")
        catch
            sendError(cCatchError)
        }
    }
    
    # معالجة طلب توليد الكود
    func handleGenerate {
        try {
            # استخراج البيانات
            data = json2list(server.request.body)
            
            # التحقق من البيانات
            if not data["prompt"] {
                return sendError("الوصف مطلوب")
            }
            
            # إنشاء مهمة توليد كود
            task = new Task {
                type = "code_generation"
                data = data["prompt"]
                language = data["language"] ? data["language"] : "ring"
            }
            
            # إضافة المهمة
            taskId = processor.addTask(task)
            
            # إرسال الاستجابة
            response = [
                "task_id": taskId,
                "status": "pending"
            ]
            
            server.setContent(list2json(response), "application/json")
        catch
            sendError(cCatchError)
        }
    }
    
    # معالجة طلب حالة توليد الكود
    func handleGenerateStatus {
        try {
            taskId = server.params["id"]
            
            # الحصول على حالة المهمة
            task = processor.getTask(taskId)
            if not task {
                return sendError("مهمة غير موجودة")
            }
            
            # إعداد الاستجابة
            response = [
                "task_id": taskId,
                "status": task.status,
                "code": task.result
            ]
            
            server.setContent(list2json(response), "application/json")
        catch
            sendError(cCatchError)
        }
    }
    
    # معالجة طلب التدريب
    func handleTrain {
        try {
            # استخراج البيانات
            data = json2list(server.request.body)
            
            # بدء التدريب
            transformer.startTraining(data)
            
            # إرسال الاستجابة
            response = [
                "status": "training_started",
                "message": "بدأ التدريب بنجاح"
            ]
            
            server.setContent(list2json(response), "application/json")
        catch
            sendError(cCatchError)
        }
    }
    
    # معالجة طلب حالة التدريب
    func handleTrainStatus {
        try {
            # الحصول على حالة التدريب
            status = transformer.getTrainingStatus()
            
            # إعداد الاستجابة
            response = [
                "status": status.status,
                "progress": status.progress,
                "metrics": status.metrics
            ]
            
            server.setContent(list2json(response), "application/json")
        catch
            sendError(cCatchError)
        }
    }
    
    # معالجة طلب معلومات النموذج
    func handleModelInfo {
        try {
            # الحصول على معلومات النموذج
            info = transformer.getModelInfo()
            
            server.setContent(list2json(info), "application/json")
        catch
            sendError(cCatchError)
        }
    }
    
    # معالجة طلب حفظ النموذج
    func handleModelSave {
        try {
            # حفظ النموذج
            path = transformer.saveModel()
            
            response = [
                "status": "success",
                "path": path
            ]
            
            server.setContent(list2json(response), "application/json")
        catch
            sendError(cCatchError)
        }
    }
    
    # معالجة طلب تحميل النموذج
    func handleModelLoad {
        try {
            # استخراج البيانات
            data = json2list(server.request.body)
            
            # تحميل النموذج
            transformer.loadModel(data["path"])
            
            response = [
                "status": "success",
                "message": "تم تحميل النموذج بنجاح"
            ]
            
            server.setContent(list2json(response), "application/json")
        catch
            sendError(cCatchError)
        }
    }
    
    # إرسال رسالة خطأ
    func sendError(message) {
        response = [
            "error": message
        ]
        server.setContent(list2json(response), "application/json")
    }
}
