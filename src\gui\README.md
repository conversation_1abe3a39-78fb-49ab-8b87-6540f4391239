# 🖥️ واجهة المستخدم الرسومية للمحول الذكي

## 📝 نظرة عامة
واجهة مستخدم رسومية متكاملة للتحكم في نظام المحول الذكي، مبنية باستخدام مكتبة Qt في Ring. توفر الواجهة تحكماً كاملاً في عمليات التدريب والتقييم والترجمة وتوليد الكود.

## 🎯 المميزات الرئيسية

### 1. لوحة التدريب 🎓
- إعدادات التدريب القابلة للتخصيص:
  * معدل التعلم
  * عدد الحلقات
  * حجم الدفعة
- مخططات مباشرة لتتبع التقدم:
  * مخطط الخسارة
  * مخطط الدقة
- سجل تدريب مباشر
- أزرار تحكم:
  * بدء التدريب
  * إيقاف التدريب

### 2. لوحة التقييم 📊
- اختيار النموذج للتقييم:
  * آخر نموذج
  * أفضل نموذج
- عرض نتائج التقييم:
  * درجة BLEU للترجمة
  * دقة توليد الكود
- تقارير تفصيلية للأداء

### 3. لوحة الترجمة والتوليد 🔄
- دعم متعدد العمليات:
  * ترجمة من العربية إلى الإنجليزية
  * ترجمة من الإنجليزية إلى العربية
  * توليد الكود
- واجهة سهلة الاستخدام:
  * مربع نص للمدخلات
  * مربع نص للمخرجات
  * زر تنفيذ سريع

## 🛠️ كيفية الاستخدام

### تشغيل التطبيق
```ring
load "main_window.ring"
```

### التدريب
1. افتح تبويب "التدريب"
2. اضبط إعدادات التدريب:
   - معدل التعلم (مثال: 0.001)
   - عدد الحلقات (مثال: 10)
   - حجم الدفعة (مثال: 32)
3. انقر على "بدء التدريب"
4. راقب التقدم في المخططات والسجل

### التقييم
1. افتح تبويب "التقييم"
2. اختر النموذج المراد تقييمه
3. انقر على "تقييم"
4. راجع النتائج المعروضة

### الترجمة وتوليد الكود
1. افتح تبويب "الترجمة والتوليد"
2. اختر نوع العملية
3. أدخل النص المراد معالجته
4. انقر على "تنفيذ"
5. راجع النتيجة في مربع المخرجات

## 🎨 تخصيص الواجهة

### تغيير الألوان والمظهر
يمكن تخصيص مظهر الواجهة عن طريق تعديل الأنماط في `main_window.ring`:
```ring
win = new qMainWindow() {
    setStyleSheet("
        QMainWindow { background-color: #f0f0f0; }
        QPushButton { 
            background-color: #4CAF50;
            color: white;
            padding: 5px;
        }
    ")
}
```

### إضافة مخططات جديدة
يمكن إضافة مخططات إضافية في تبويب التدريب:
```ring
new qChartView() {
    chart = new qChart() {
        setTitle("مخطط جديد")
        addSeries(new qLineSeries())
    }
}
```

## 🔧 المتطلبات التقنية
- Ring Programming Language
- مكتبة Qt
- مكتبات الرسوم البيانية Qt Charts

## 📈 التحسينات المستقبلية
1. إضافة دعم للسمات المظلمة والفاتحة
2. تحسين تخطيط الرسوم البيانية
3. إضافة خيارات تصدير النتائج
4. دعم المزيد من اللغات
5. إضافة لوحة معلومات متقدمة

## 💡 نصائح للاستخدام
1. راقب مخططات التدريب باستمرار للتأكد من تحسن النموذج
2. استخدم زر الإيقاف عند ملاحظة استقرار الأداء
3. جرب أحجام دفعات مختلفة للحصول على أفضل أداء
4. احفظ إعدادات التدريب الناجحة للاستخدام المستقبلي

## 🐛 حل المشكلات الشائعة
1. **المخططات لا تتحدث**: تأكد من اتصال إشارات التحديث
2. **بطء الواجهة**: قلل عدد نقاط البيانات في المخططات
3. **أخطاء الذاكرة**: أغلق وأعد فتح التطبيق بعد جلسات طويلة
4. **مشاكل العرض**: تأكد من تثبيت جميع مكتبات Qt المطلوبة
