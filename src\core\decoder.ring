# فك التشفير (Decoder)
# يقوم بتوليد المخرجات النهائية (نص مترجم أو كود) باستخدام مخرجات المشفر
# يتميز بوجود طبقة انتباه إضافية للربط مع مخرجات المشفر
Load "stdlibcore.ring"
Load "../utils/math.ring"
Load "attention.ring"

# طبقة فك التشفير
Class DecoderLayer {
    # المتغيرات
    oSelfAttention       # طبقة الانتباه الذاتي
    oEncoderAttention   # طبقة الانتباه مع مخرجات المشفر
    oFFN                # الشبكة العصبية الأمامية
    nDModel             # البعد الأساسي للنموذج
    
    # دالة التهيئة
    # المدخلات:
    #   d_model: البعد الأساسي للنموذج
    #   num_heads: عدد رؤوس الانتباه
    #   d_ff: البعد الداخلي للشبكة العصبية
    func init(d_model, num_heads, d_ff) {
        nDModel = d_model
        
        # إنشاء طبقات الانتباه والشبكة العصبية
        oSelfAttention = new MultiHeadAttention(num_heads, d_model)
        oEncoderAttention = new MultiHeadAttention(num_heads, d_model)
        oFFN = new FeedForward(d_model, d_ff)
    }
    
    # دالة التقدم الأمامي
    # المدخلات:
    #   x: المدخلات (التسلسل المستهدف)
    #   enc_output: مخرجات المشفر
    #   look_ahead_mask: قناع لمنع رؤية الكلمات المستقبلية
    #   padding_mask: قناع للحشو
    # المخرجات:
    #   المخرجات بعد تطبيق طبقة فك التشفير
    func forward(x, enc_output, look_ahead_mask, padding_mask) {
        # طبقة الانتباه الذاتي مع التطبيع والاتصال المتخطي
        attn1_output = oSelfAttention.forward(x, look_ahead_mask)
        attn1_norm = []
        for i = 1 to len(x)
            row = []
            for j = 1 to len(x[i])
                add(row, x[i][j] + attn1_output[i][j])
            next
            add(attn1_norm, layer_norm(row))
        next
        
        # طبقة الانتباه مع مخرجات المشفر
        attn2_output = oEncoderAttention.forward(attn1_norm, enc_output, padding_mask)
        attn2_norm = []
        for i = 1 to len(attn1_norm)
            row = []
            for j = 1 to len(attn1_norm[i])
                add(row, attn1_norm[i][j] + attn2_output[i][j])
            next
            add(attn2_norm, layer_norm(row))
        next
        
        # طبقة الشبكة العصبية مع التطبيع والاتصال المتخطي
        ffn_output = oFFN.forward(attn2_norm)
        output = []
        for i = 1 to len(attn2_norm)
            row = []
            for j = 1 to len(attn2_norm[i])
                add(row, attn2_norm[i][j] + ffn_output[i][j])
            next
            add(output, layer_norm(row))
        next
        
        return output
    }
}

# فك التشفير الكامل
Class Decoder {
    # المتغيرات
    aLayers      # مصفوفة من طبقات فك التشفير
    nNumLayers   # عدد الطبقات
    nDModel      # البعد الأساسي للنموذج
    
    # دالة التهيئة
    # المدخلات:
    #   num_layers: عدد طبقات فك التشفير
    #   d_model: البعد الأساسي للنموذج
    #   num_heads: عدد رؤوس الانتباه
    #   d_ff: البعد الداخلي للشبكة العصبية
    func init(num_layers, d_model, num_heads, d_ff) {
        nNumLayers = num_layers
        nDModel = d_model
        aLayers = []
        
        # إنشاء طبقات فك التشفير
        for i = 1 to num_layers
            add(aLayers, new DecoderLayer(d_model, num_heads, d_ff))
        next
    }
    
    # دالة التقدم الأمامي
    # المدخلات:
    #   x: المدخلات (التسلسل المستهدف)
    #   enc_output: مخرجات المشفر
    #   look_ahead_mask: قناع لمنع رؤية الكلمات المستقبلية
    #   padding_mask: قناع للحشو
    # المخرجات:
    #   المخرجات النهائية لفك التشفير
    func forward(x, enc_output, look_ahead_mask = null, padding_mask = null) {
        # تطبيق كل طبقات فك التشفير بالتتابع
        output = x
        for layer in aLayers
            output = layer.forward(output, enc_output, look_ahead_mask, padding_mask)
        next
        return output
    }
    
    # دالة توليد القناع المستقبلي
    # المدخلات:
    #   size: حجم التسلسل
    # المخرجات:
    #   مصفوفة القناع
    func create_look_ahead_mask(size) {
        mask = []
        for i = 1 to size
            row = []
            for j = 1 to size
                if j > i
                    add(row, 0)  # منع رؤية الكلمات المستقبلية
                else
                    add(row, 1)  # السماح برؤية الكلمات السابقة والحالية
                ok
            next
            add(mask, row)
        next
        return mask
    }
}
