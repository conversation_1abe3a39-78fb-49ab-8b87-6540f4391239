load "httplib.ring"

# عميل API
Class APIClient {
    # المتغيرات
    client
    baseURL
    
    func init(url) {
        baseURL = url
        client = new Client(url)
    }
    
    # طلب ترجمة
    func translate(text, direction ) { # = "ar-en"
        data = [
            "text": text,
            "direction": direction
        ]
        
        response = post("/api/translate", data)
        return response
    }
    
    # الحصول على حالة الترجمة
    func getTranslateStatus(taskId) {
        return get("/api/translate/status/" + taskId)
    }
    
    # طلب توليد كود
    func generateCode(prompt, language) { #  = "ring" 
        data = [
            "prompt": prompt,
            "language": language
        ]
        
        response = post("/api/generate", data)
        return response
    }
    
    # الحصول على حالة توليد الكود
    func getGenerateStatus(taskId) {
        return get("/api/generate/status/" + taskId)
    }
    
    # بدء التدريب
    func startTraining(config) {
        return post("/api/train", config)
    }
    
    # الحصول على حالة التدريب
    func getTrainingStatus {
        return get("/api/train/status")
    }
    
    # الحصول على معلومات النموذج
    func getModelInfo {
        return get("/api/model/info")
    }
    
    # حفظ النموذج
    func saveModel {
        return post("/api/model/save", [])
    }
    
    # تحميل النموذج
    func loadModel(path) {
        data = [
            "path": path
        ]
        return post("/api/model/load", data)
    }
    
    # طلب GET
    func get(endpoint) {
        try {
            response = client.download(endpoint)
            return json2list(response)
        catch
            return ["error": cCatchError]
        }
    }
    
    # طلب POST
    func post(endpoint, data) {
        try {
            headers = [
                "Content-Type": "application/json"
            ]
            
            response = client.post(endpoint, list2json(data), headers)
            return json2list(response)
        catch
            return ["error": cCatchError]
        }
    }
}
