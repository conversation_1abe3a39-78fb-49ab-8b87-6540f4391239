load "../../core/transformer.ring"
load "../services/cache_service.ring"
load "../config.ring"

class CodeGenerationController
    transformer
    cache
    config
    
    func init
        transformer = new Transformer
        cache = new CacheService
        config = new APIConfig
    
    func generateCode request, response
        try
            # التحقق من صحة المدخلات
            if not request.body[:description]
                return response.badRequest("وصف الكود مطلوب")
            ok
            
            language = request.body[:language] or "Ring"
            withTests = request.body[:with_tests] or false
            withDocs = request.body[:with_docs] or false
            
            # التحقق من التخزين المؤقت
            if config.CACHE_ENABLED
                cacheKey = "code:" + request.body[:description] + ":" + 
                          language + ":" + withTests + ":" + withDocs
                cached = cache.get(cacheKey)
                if cached return response.ok(cached) ok
            ok
            
            # توليد الكود
            result = transformer.generateCode(
                request.body[:description],
                language,
                withTests,
                withDocs
            )
            
            # تخزين النتيجة في الذاكرة المؤقتة
            if config.CACHE_ENABLED
                cache.set(cacheKey, result, config.CACHE_TIMEOUT)
            ok
            
            return response.ok(result)
            
        catch
            return response.error("حدث خطأ أثناء توليد الكود")
        done
    
    func improveCode request, response
        try
            if not request.body[:code]
                return response.badRequest("الكود مطلوب")
            ok
            
            suggestions = request.body[:suggestions] or []
            
            improved = transformer.improveCode(
                request.body[:code],
                suggestions
            )
            
            return response.ok([
                :original = request.body[:code],
                :improved = improved,
                :changes = transformer.explainChanges(
                    request.body[:code],
                    improved
                )
            ])
            
        catch
            return response.error("حدث خطأ أثناء تحسين الكود")
        done
    
    func generateTests request, response
        try
            if not request.body[:code]
                return response.badRequest("الكود مطلوب")
            ok
            
            language = request.body[:language] or "Ring"
            
            tests = transformer.generateTests(
                request.body[:code],
                language
            )
            
            return response.ok([
                :code = request.body[:code],
                :tests = tests,
                :language = language
            ])
            
        catch
            return response.error("حدث خطأ أثناء توليد الاختبارات")
        done
