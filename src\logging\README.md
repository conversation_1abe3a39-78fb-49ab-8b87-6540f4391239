# 📝 نظام التوثيق والتسجيل

## 📋 نظرة عامة
نظام متكامل للتوثيق وتسجيل الأحداث والأخطاء وقياس الأداء.

## 🔧 المكونات

### 1. نظام التسجيل الأساسي (logger.ring)
- تسجيل الأحداث بمستويات مختلفة
- تدوير الملفات تلقائياً
- تنسيق قابل للتخصيص

### 2. مسجل الأداء (performance_logger.ring)
- قياس وقت العمليات
- تتبع مقاييس التدريب
- مراقبة استخدام الموارد

### 3. مسجل الأخطاء (error_logger.ring)
- تسجيل الأخطاء مع التفاصيل
- تتبع الاستثناءات
- توليد تقارير الأخطاء

## 🚀 كيفية الاستخدام

### التسجيل الأساسي
```ring
load "logger.ring"
logger = new Logger("logs/app.log")

# تسجيل رسائل بمستويات مختلفة
logger.debug("رسالة تصحيح")
logger.info("معلومات")
logger.warning("تحذير")
logger.error("خطأ")
logger.critical("خطأ حرج")
```

### تسجيل الأداء
```ring
load "performance_logger.ring"
perfLogger = new PerformanceLogger("logs/performance.log")

# قياس أداء عملية
perfLogger.startOperation("training")
# ... العملية ...
perfLogger.endOperation("training")

# تسجيل مقاييس التدريب
perfLogger.logTrainingMetrics(1, 0.5, 0.85)

# توليد تقرير
perfLogger.generateReport("reports/performance_report.txt")
```

### تسجيل الأخطاء
```ring
load "error_logger.ring"
errorLogger = new ErrorLogger("logs/errors.log")

# تسجيل خطأ
errorLogger.logError("ValidationError", 
                    "بيانات غير صالحة",
                    "القيمة خارج النطاق المسموح")

# توليد تقرير
errorLogger.generateErrorReport("reports/error_report.txt")
```

## 📊 التقارير

### تقرير الأداء
- وقت تنفيذ العمليات
- مقاييس التدريب
- استخدام الموارد
- إحصائيات عامة

### تقرير الأخطاء
- قائمة الأخطاء حسب النوع
- تفاصيل كل خطأ
- تتبع المكدس
- ملخص إحصائي

## ⚙️ التكوين

### مستويات التسجيل
```ring
LEVEL_DEBUG = 1
LEVEL_INFO = 2
LEVEL_WARNING = 3
LEVEL_ERROR = 4
LEVEL_CRITICAL = 5
```

### خيارات التدوير
- حجم الملف الأقصى
- عدد النسخ الاحتياطية
- تنسيق اسم الملف

## 🔍 الميزات المتقدمة

### 1. تدوير الملفات
- تدوير تلقائي عند الوصول للحجم الأقصى
- حفظ نسخ احتياطية
- تنظيف تلقائي

### 2. تتبع الأداء
- قياس وقت التنفيذ
- تتبع استخدام الموارد
- تحليل الأداء

### 3. تحليل الأخطاء
- تصنيف الأخطاء
- تتبع المصدر
- إحصائيات تفصيلية

## 🔧 إرشادات التطوير

### إضافة مستوى تسجيل جديد
1. تعريف المستوى في logger.ring
2. إضافة دالة التسجيل
3. تحديث التوثيق

### تخصيص التقارير
1. تعديل تنسيق التقرير
2. إضافة معلومات جديدة
3. تحسين العرض

## 📝 ملاحظات هامة

1. **الأداء**:
   - تجنب التسجيل المفرط
   - استخدام التدوير المناسب
   - مراقبة حجم الملفات

2. **الأمان**:
   - عدم تسجيل معلومات حساسة
   - حماية ملفات السجل
   - تشفير المعلومات الهامة

3. **الصيانة**:
   - مراجعة السجلات دورياً
   - تنظيف الملفات القديمة
   - تحديث التكوين
