load "DatabaseManager.ring"
load "QueryBuilder.ring"

class BaseModel
    db
    tableName = ""
    
    func init
        db = new DatabaseManager
        
    func find id
        query = new QueryBuilder(tableName)
        query.where("id", "=", id)
        return db.sqlite_query(db.db, query.build(), [id])
        
    func all
        query = new QueryBuilder(tableName)
        return db.sqlite_query(db.db, query.build(), [])
        
    func where column, value
        query = new QueryBuilder(tableName)
        query.where(column, "=", value)
        return db.sqlite_query(db.db, query.build(), [value])

class Translation from BaseModel
    func init
        Super.init()
        tableName = "translations"
        
    func create source_text, target_text, source_lang, target_lang
        return db.addTranslation(source_text, target_text, source_lang, target_lang)
        
    func findBySourceText text
        return where("source_text", text)

class CodeGeneration from BaseModel
    func init
        Super.init()
        tableName = "code_generations"
        
    func create description, code, language
        return db.addCodeGeneration(description, code, language)
        
    func findByLanguage lang
        return where("language", lang)

class PerformanceMetric from BaseModel
    func init
        Super.init()
        tableName = "performance_metrics"
        
    func create operation_type, execution_time, memory_usage, success_rate
        return db.addPerformanceMetric(operation_type, execution_time, memory_usage, success_rate)
        
    func getStats operation_type
        return db.getPerformanceStats(operation_type)
