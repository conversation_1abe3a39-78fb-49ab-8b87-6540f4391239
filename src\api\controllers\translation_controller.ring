load "stdlib.ring"
load "../../core/transformer.ring"
load "../services/cache_service.ring"
load "../config.ring"

class TranslationController
    transformer
    cache
    config
    
    func init
        transformer = new Transformer
        cache = new CacheService
        config = new APIConfig
    
    func translate request, response
        try
            # التحقق من صحة المدخلات
            if not request.body[:text]
                return response.badRequest("النص مطلوب")
            ok
            
            sourceLang = request.body[:source_lang] or config.DEFAULT_SOURCE_LANG
            targetLang = request.body[:target_lang] or config.DEFAULT_TARGET_LANG
            
            # التحقق من دعم اللغات
            if not config.SUPPORTED_LANGUAGES.contains(sourceLang) or
               not config.SUPPORTED_LANGUAGES.contains(targetLang)
                return response.badRequest("اللغة غير مدعومة")
            ok
            
            # التحقق من التخزين المؤقت
            cacheKey = "trans:" + request.body[:text] + ":" + sourceLang + ":" + targetLang
            if config.CACHE_ENABLED
                cached = cache.get(cacheKey)
                if cached return response.ok(cached) ok
            ok
            
            # إجراء الترجمة
            result = transformer.translate(
                request.body[:text],
                sourceLang,
                targetLang
            )
            
            # تخزين النتيجة في الذاكرة المؤقتة
            if config.CACHE_ENABLED
                cache.set(cacheKey, result, config.CACHE_TIMEOUT)
            ok
            
            return response.ok([
                :original = request.body[:text],
                :translated = result,
                :source_lang = sourceLang,
                :target_lang = targetLang
            ])
            
        catch
            return response.error("حدث خطأ أثناء الترجمة")
        done
    
    func batchTranslate request, response
        try
            # التحقق من صحة المدخلات
            if not request.body[:texts] or len(request.body[:texts]) = 0
                return response.badRequest("النصوص مطلوبة")
            ok
            
            sourceLang = request.body[:source_lang] or config.DEFAULT_SOURCE_LANG
            targetLang = request.body[:target_lang] or config.DEFAULT_TARGET_LANG
            
            results = []
            for text in request.body[:texts]
                translated = transformer.translate(text, sourceLang, targetLang)
                add(results, [
                    :original = text,
                    :translated = translated
                ])
            next
            
            return response.ok([
                :results = results,
                :count = len(results),
                :source_lang = sourceLang,
                :target_lang = targetLang
            ])
            
        catch
            return response.error("حدث خطأ أثناء الترجمة المجمعة")
        done
    
    func analyzeQuality request, response
        try
            if not request.body[:original] or not request.body[:translated]
                return response.badRequest("النص الأصلي والمترجم مطلوبان")
            ok
            
            sourceLang = request.body[:source_lang] or config.DEFAULT_SOURCE_LANG
            targetLang = request.body[:target_lang] or config.DEFAULT_TARGET_LANG
            
            quality = transformer.analyzeTranslationQuality(
                request.body[:original],
                request.body[:translated],
                sourceLang,
                targetLang
            )
            
            return response.ok(quality)
            
        catch
            return response.error("حدث خطأ أثناء تحليل جودة الترجمة")
        done
