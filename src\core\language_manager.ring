# مدير اللغات والذاكرة
# يوفر دعماً للغات متعددة مع تحسين استخدام الذاكرة

Load "stdlibcore.ring"
Load "../utils/math.ring"

# فئة لإدارة الذاكرة المؤقتة للتضمينات
Class EmbeddingCache {
    # المتغيرات
    aCache      # مخزن مؤقت للتضمينات
    nMaxSize    # الحد الأقصى لحجم المخزن المؤقت
    
    func init(max_size)
        nMaxSize = max_size
        aCache = new Map
    
    # تخزين تضمين مع سياسة LRU (الأقل استخداماً مؤخراً)
    func store(key, embedding)
        if len(aCache) >= nMaxSize
            removeOldest()
        ok
        aCache[key] = [embedding, clock()]
    
    # استرجاع تضمين
    func get(key)
        if exists(aCache, key)
            aCache[key][2] = clock()  # تحديث وقت الاستخدام
            return aCache[key][1]
        ok
        return null
    
    private
    # إزالة أقدم عنصر في المخزن المؤقت
    func removeOldest
        oldest_time = clock()
        oldest_key = null
        for key in aCache.keys()
            if aCache[key][2] < oldest_time
                oldest_time = aCache[key][2]
                oldest_key = key
            ok
        next
        aCache.remove(oldest_key)
}

# فئة لإدارة اللغات المتعددة
Class LanguageManager {
    # المتغيرات
    aLanguages       # قائمة اللغات المدعومة
    aTokenizers     # محلل الكلمات لكل لغة
    oEmbCache       # مخزن مؤقت للتضمينات
    
    func init
        aLanguages = ["ar", "en", "fr", "es"]  # إضافة الفرنسية والإسبانية
        aTokenizers = new Map
        oEmbCache = new EmbeddingCache(1000)  # تخزين مؤقت لـ 1000 تضمين
        initializeTokenizers()
    
    # تهيئة محللات الكلمات لكل لغة
    func initializeTokenizers
        for lang in aLanguages
            aTokenizers[lang] = createTokenizer(lang)
        next
    
    # إنشاء محلل كلمات مناسب للغة
    func createTokenizer(lang)
        switch lang
            case "ar"
                return new ArabicTokenizer
            case "en"
                return new EnglishTokenizer
            case "fr"
                return new FrenchTokenizer
            case "es"
                return new SpanishTokenizer
        off
    
    # تحليل النص حسب اللغة
    func tokenize(text, lang)
        if exists(aTokenizers, lang)
            return aTokenizers[lang].tokenize(text)
        ok
        raise("Language not supported: " + lang)
    
    # الحصول على تضمين مع استخدام المخزن المؤقت
    func getEmbedding(token, lang)
        key = lang + "_" + token
        cached = oEmbCache.get(key)
        if cached != null
            return cached
        ok
        
        # إنشاء تضمين جديد وتخزينه
        embedding = generateEmbedding(token, lang)
        oEmbCache.store(key, embedding)
        return embedding
    
    private
    # توليد تضمين جديد للكلمة
    func generateEmbedding(token, lang)
        # تنفيذ خوارزمية التضمين حسب اللغة
        # يمكن استخدام خوارزميات مختلفة لكل لغة
        return computeEmbedding(token, lang)
}

# محللات الكلمات لكل لغة
Class BaseTokenizer {
    func tokenize(text)
        return text.split(" ")
}

Class ArabicTokenizer from BaseTokenizer {
    func tokenize(text)
        # معالجة خاصة للغة العربية
        text = normalizeArabic(text)
        return super.tokenize(text)
    
    func normalizeArabic(text)
        # تطبيع النص العربي 
        # إزالة التشكيل وتوحيد الأحرف المتشابهة
        
        return text
}

Class EnglishTokenizer from BaseTokenizer {
    func tokenize(text)
        # معالجة خاصة للغة الإنجليزية
        text = text.lower()
        return super.tokenize(text)
}

Class FrenchTokenizer from BaseTokenizer {
    func tokenize(text)
        # معالجة خاصة للغة الفرنسية
        text = normalizeFrench(text)
        return super.tokenize(text)
    
    func normalizeFrench(text)
        # تطبيع النص الفرنسي
        # معالجة الحروف المميزة
        return text
}

Class SpanishTokenizer from BaseTokenizer {
    func tokenize(text)
        # معالجة خاصة للغة الإسبانية
        text = normalizeSpanish(text)
        return super.tokenize(text)
    
    func normalizeSpanish(text)
        # تطبيع النص الإسباني
        # معالجة الحروف المميزة
        return text
}
