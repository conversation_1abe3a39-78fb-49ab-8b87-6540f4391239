load "src/core/transformer.ring"
load "src/data/database/DatabaseManager.ring"
load "src/testing/TestGenerator.ring"

class AdvancedCodeGenerator
    transformer
    db
    testGen
    
    func init
        transformer = new Transformer
        db = new DatabaseManager
        testGen = new TestGenerator
    
    func generateWithTests description, language = "Ring"
        # توليد الكود
        generatedCode = transformer.generateCode(description, language)
        
        # توليد الاختبارات
        tests = testGen.generateTests(generatedCode, language)
        
        # تشغيل الاختبارات
        testResults = testGen.runTests(tests)
        
        # تحسين الكود بناءً على نتائج الاختبارات
        if testResults[:passRate] < 1.0
            generatedCode = transformer.improveCode(
                generatedCode,
                testResults[:failedTests]
            )
            # إعادة تشغيل الاختبارات
            tests = testGen.generateTests(generatedCode, language)
            testResults = testGen.runTests(tests)
        ok
        
        # تخزين النتائج
        query = "INSERT INTO code_generations 
                (description, language, code, tests, pass_rate) 
                VALUES (?, ?, ?, ?, ?)"
        db.sqlite_execute(db.db, query, [
            description, language, generatedCode,
            tests, testResults[:passRate]
        ])
        
        return [
            :code = generatedCode,
            :tests = tests,
            :results = testResults
        ]
    
    func generateWithDocs description, language = "Ring"
        # توليد الكود مع التوثيق
        codeWithDocs = transformer.generateCodeWithDocs(description, language)
        
        # استخراج التوثيق
        docs = transformer.extractDocs(codeWithDocs)
        
        # توليد أمثلة استخدام
        examples = transformer.generateExamples(codeWithDocs)
        
        return [
            :code = codeWithDocs,
            :documentation = docs,
            :examples = examples
        ]
    
    func close
        db.close()

# مثال على الاستخدام
generator = new AdvancedCodeGenerator

? "مثال متقدم لتوليد الكود مع اختبارات"
? "=================================="

# توليد كود مع اختبارات
description = "دالة لفرز مصفوفة باستخدام خوارزمية الفرز السريع"
result = generator.generateWithTests(description)

? "الكود المولد:"
? "------------"
? result[:code]
? ""

? "الاختبارات المولدة:"
? "-----------------"
? result[:tests]
? ""

? "نتائج الاختبارات:"
? "---------------"
? "نسبة النجاح: " + result[:results][:passRate]
? "عدد الاختبارات: " + result[:results][:totalTests]
? "الاختبارات الناجحة: " + result[:results][:passedTests]
? ""

# توليد كود مع توثيق
docDescription = "صنف لإدارة قائمة المهام مع دعم الأولويات والمواعيد النهائية"
docResult = generator.generateWithDocs(docDescription)

? "الكود مع التوثيق:"
? "---------------"
? docResult[:code]
? ""

? "التوثيق المولد:"
? "--------------"
? docResult[:documentation]
? ""

? "أمثلة الاستخدام:"
? "--------------"
for example in docResult[:examples]
    ? example
    ? ""
next

generator.close()
