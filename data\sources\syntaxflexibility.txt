.. index:: 
	single: Syntax Flexibility; Introduction

==================
Syntax Flexibility
==================

In this chapter we will learn about some options that are provided automatically by the Ring
compiler for syntax flexibility.

.. index:: 
	pair: Syntax Flexibility; Change Language Keywords

Change Language Keywords
========================

We can change any keyword using the ChangeRingKeyword command.

.. note:: Remember to restore the keyword again if the team will mix between styles in the same project.

.. tip:: The ChangeRingKeyword command is executed in the scanner stage by the compiler (before parsing).


Syntax:

.. code-block:: ring
	
	ChangeRingKeyword  <oldkeyword>  <newkeyword>

Example:

.. code-block:: ring

	ChangeRingKeyword see print
	
	print "welcome" + nl

	ChangeRingKeyword print see

	see "Welcome" + nl

Example:

.. code-block:: ring

	ChangeRingKeyword  func function
	ChangeRingKeyword  see  print
	ChangeRingKeyword  ok   endif
	ChangeRingKeyword  next endfor
	ChangeRingKeyword  end  endwhile

	x = 10
	while x > 0
		print "x = " + x + nl
		for t = 1 to 10
			if t = 3
				print "number three" + nl
			endif
		endfor	

	x--
	endwhile

	test() 

	function test
		print "message from test" + nl

	ChangeRingKeyword  function func
	ChangeRingKeyword  print see
	ChangeRingKeyword  endif ok
	ChangeRingKeyword  endfor next
	ChangeRingKeyword  endwhile end


.. index:: 
	pair: Syntax Flexibility; Change Language Operators

Change Language Operators
=========================

We can change any operator using the ChangeRingOperator command.

.. note:: Remember to restore the operator again if the team will mix between styles in the same project.

.. tip:: The ChangeRingOperartor command is executed in the scanner stage by the compiler (before parsing).


Syntax:

.. code-block:: ring
	
	ChangeRingOperator  <oldkeyword>  <newkeyword>

Example:

The next program hide the + operator by changing it to _+

.. code-block:: ring

	changeringoperator + _+
	changeringkeyword SEE PRINT

	try
		print 5 + 10
	catch
		print nl print "error" print nl
	done

	changeringoperator _+ +

The next program change the + operator to "plus".

.. code-block:: ring

	changeringoperator + plus 
	changeringkeyword SEE PRINT

	Print 5 plus 5  

	changeringoperator plus +
	changeringkeyword PRINT SEE 


.. index:: 
	pair: Syntax Flexibility; Using "()" around the function parameters


.. index:: 
	pair: Syntax Flexibility; Load Syntax Files


Load Syntax Files
=================

You may store a group of ChangeRingKeyword and ChangeRingOperator commands in a file to use later
in many source files. You can't use the Load command to call these files because

* ChangeRingKeyword and ChangeRingOperator commands are executed in the scanner phase by the compiler (before parsing).
* The load command is executed in the parsing phase (after the scanner phase).

Solution: Use the LoadSyntax Command which is executed in the scanner phase.

Syntax:

.. code-block:: ring

	LoadSyntax	"syntaxfile.ring"

Example:

File : StyleBasicOn.ring

.. code-block:: ring

	ChangeRingKeyword 	see 	print
	ChangeRingKeyword 	ok 	endif
	ChangeRingKeyword 	next 	endfor
	ChangeRingKeyword 	end 	endwhile

File : StyleBasicOff.ring

.. code-block:: ring

	ChangeRingKeyword  print 	see
	ChangeRingKeyword  endif 	ok
	ChangeRingKeyword  endfor 	next
	ChangeRingKeyword  endwhile 	end

File : UseStyleBasic.ring

.. code-block:: ring

	LoadSyntax "stylebasicon.ring"

	x = 10
	while x > 0
		print "x = " + x + nl
		for t = 1 to 10
			if t = 3
				print "number three" + nl
			endif
		endfor	
		x--
	endwhile

	LoadSyntax "stylebasicoff.ring"

	see "done" + nl

.. note:: files called by the LoadSyntax command must contains ChangeRingKeyword and ChangeRingOperator commands only. 

.. tip:: files called by the LoadSyntax command doesn't support functions, packages and classes. just imperative commands only.

.. note:: Using this feature you can create many styles that you can use in the same project and you can support Ring translation to other languages like Arabic, French and so on.

.. tip:: The effect of LoadSyntax command is related to the current source code file only.

.. tip:: Using LoadSyntax command is optional, See the (Automatic loading for syntax files) section.
	
Using "()" around the function parameters
=========================================

We can use () around the function parameters (optional).

Example:

.. code-block:: ring

	hello()
	sum(3,4)

	func hello()
		see "Hello" + nl

	func sum(x,y)
		see x+y+nl
	
Output:

.. code-block:: ring

	Hello
	7

Example:

.. code-block:: ring

	myfunc = func x,y { see x + y + nl }

	call myfunc (3,4)

	myfunc2 = func (x,y) { see x+y+nl }

	call myfunc(3,4)

Output:

.. code-block:: ring

	7
	7

.. index:: 
	pair: Syntax Flexibility; Using Semi-colon after and between statements

Using Semi-colon after and between statements
=============================================

In Ring we can use semi-colon after and between statements (optional).

Example:

.. code-block:: ring

	# Using semi-colon is optional

	see "Hello" + nl ; see "How are you?" + nl  ; see "Welcome to Ring" + nl ;
	one() ; two() ; three() ;
	func one ; see "one" + nl ;
	func two ; see "two" + nl ;
	func three ; see "three" + nl ;


Output:

.. code-block:: ring


	Hello
	How are you?
	Welcome to Ring
	one
	two
	three

.. index:: 
	pair: Syntax Flexibility; Using $ and @ in the start of the variable name

Using $ and @ in the start of the variable name
===============================================

You can use any unicode character in the variable name also we can use $ and @ in the name.

This feature may help, for example we can start global variables with $ and the object attributes
with @. 

In other languages like Ruby this is the rule, In the Ring language this is just an option without
any force from the Compiler.

example:

.. code-block:: ring

	$global_variable = 5

	new test { hello() }	

	class test

		@instance_variable = 10

		func hello

			local_variable = 15

			see "Global   : " + $global_variable + nl + 
			    "Instance : " + @instance_variable + nl +
			    "Local    : " + local_variable + nl

Output:


.. code-block:: ring

	Global   : 5
	Instance : 10
	Local    : 15

.. index:: 
	pair: Syntax Flexibility; Using the 'elseif' keyword as 'but' in if statement

Using the 'elseif' keyword as 'but' in if statement
===================================================

if you don't like the 'but' keyword in if statement
Then you can use the 'elseif' keyword.

Example:

.. code-block:: ring

	give x
	if x = 1 see "one"
	elseif x=2 see "two"
	elseif x=3 see "three"
	elseif x=4 see "four"
	else see "other"
	ok
	see nl

.. index:: 
	pair: Syntax Flexibility; Using the 'else' keyword as 'other' in switch statement

Using the 'else' keyword as 'other' in switch statement
=======================================================

if you don't like the 'other' keyword in switch statement
Then you can use the 'else' keyword.

Also you can replace 'else' with 'other' in if statement.

i.e. 'other' keyword is the same as 'else' keyword.

Example:

.. code-block:: ring

	x = 1
	switch x
		on 10 
			see "10" + nl
		else 
			see "not 10" + nl
	end

Output:

.. code-block:: ring

	not 10

.. index:: 
	pair: Syntax Flexibility; Using the 'end' keyword in different control structures

Using the 'end' keyword in different control structures
=======================================================

We can use the 'end' keyword to close different control structures

* If statement
* For loop
* Switch 
* While
* Try-Catch

Example:

.. code-block:: ring

	see "if statement.." + nl
	x = 1
	if x = 1 
		see "one" + nl
	elseif x=2 
		see "two" + nl
	elseif x=3 
		see "three" + nl
	end
	see "for loop.." + nl
	for t = 1 to 10
		see t 
	end
	see nl
	see "switch..." + nl
	x = 1

	switch x
		on 1 see "one" + nl
		on 2 see "two" + nl
	end

	see "try catch..." + nl
	try
		x = 1 / 0
	catch
		see "catching error" + nl
	end

Output:

.. code-block:: ring

	if statement..
	one
	for loop..
	12345678910
	switch...
	one
	try catch...
	catching error

.. index:: 
	pair: Syntax Flexibility; Using braces to start and end different control structures

Using braces to start and end different control structures
==========================================================


We can use braces { } to start and end different control structures

* If statement
* For loop
* Switch 
* While
* Try-Catch

Example:

.. code-block:: ring

	see "if statement.." + nl
	x = 1
	if x = 1 {
		see "one" + nl
	elseif x=2 
		see "two" + nl
	elseif x=3 
		see "three" + nl
	}
	see "for loop.." + nl
	for t = 1 to 10 {
		see t 
	}
	see nl
	see "switch..." + nl
	x = 1

	switch x {
		on 1 see "one" + nl
		on 2 see "two" + nl
	}

	see "try catch..." + nl
	try {
		x = 1 / 0
	catch
		see "catching error" + nl
	}

Output:

.. code-block:: ring

	if statement..
	one
	for loop..
	12345678910
	switch...
	one
	try catch...
	catching error


.. index:: 
	pair: Syntax Flexibility; Using 'put' and 'get' as 'see' and 'give'

Using 'put' and 'get' as 'see' and 'give'
=========================================

We can replace the 'see' keyword with the 'put' keyword.

Also we can replace the 'give' keyword with the 'get' keyword.

Example:

.. code-block:: ring

	put "Hello World" + nl
	put "Enter Your Name ? " Get Name
	Put "Hello " + Name

.. index:: 
	pair: Syntax Flexibility; Using 'case' as 'on' in switch statements

Using 'case' as 'on' in switch statements
=========================================

We can replace the 'on' keyword with 'case' keyword in the switch statement.

Example (1) :

.. code-block:: ring

	for x=1 to 10 
		switch x 
		case 1 put "one" + nl
		case 2 put "two" + nl
		case 3 put "three" + nl
		else put "else" + nl
		end
	end

Example (2) :

.. code-block:: ring

	for x=1 to 10 {
		switch x {
		case 1 put "one" + nl
		case 2 put "two" + nl
		case 3 put "three" + nl
		else put "else" + nl
		}
	}


.. index:: 
	pair: Syntax Flexibility; Using 'def' as 'func' in functions/methods definition

Using 'def' as 'func' in functions/methods definition
=====================================================

We can use the 'def' keyword as the 'func' keyword to define functions and methods.

Example:

.. code-block:: ring

	one() two()

	def one put "one" + nl
	def two put "two" + nl

.. index:: 
	pair: Syntax Flexibility; Using braces { } in Packages/Classes/Functions

Using braces { } in Packages/Classes/Functions
==============================================

Example:

.. code-block:: ring

	load "stdlib.ring"

	import mypackage

	new myclass {
		myfunc() 
	}

	package mypackage 
	{
		class myclass 
		{
			func myfunc 
			{
				print("Hello, World!\n")
			}
		}
	}

.. index:: 
	pair: Syntax Flexibility; Using 'break'/'continue' keywords

Using 'break'/'continue' keywords
=================================

Instead of using Exit/Loop commands we can use Break/Continue

Example:

.. code-block:: ring

	for t=1 to 10 {
		if t=3 {
			continue
		elseif t=5
			break
		}
		? t
	}

Output:

.. code-block:: none

	1
	2
	4

.. index:: 
	pair: Syntax Flexibility; Using 'end' keyword after Packages/Classes/Functions

Using 'end' keyword after Packages/Classes/Functions
====================================================

Example:

.. code-block:: ring

	import mypackage

	new myclass {
		myfunc() 
	}

	package mypackage 
		class myclass 
			def myfunc 
				put "Hello, World!"
			end
		end
	end

.. index:: 
	pair: Syntax Flexibility; Using 'function'/'endfunction' keywords

Using 'function'/'endfunction' keywords
=======================================

Example:

.. code-block:: ring

	one() two() three()
	function one 
		? :one 
	endfunction
	function two
		? :two
	endfunction
	function three
		? :three
	endfunction


.. index:: 
	pair: Syntax Flexibility; Using 'endif'/'endfor'/'endwhile'/'endswitch'/'endtry' keywords

Using 'endif'/'endfor'/'endwhile'/'endswitch'/'endtry' keywords
===============================================================

Example:

.. code-block:: ring

	for t=1 to 10 
		if t=3 
			? :three
		endif
	endfor


.. index:: 
	pair: Syntax Flexibility; Using 'endpackage'/'endclass'/'endfunc' keywords after Packages/Classes/Functions

Using 'endpackage'/'endclass'/'endfunc' keywords after Packages/Classes/Functions
=================================================================================

Example:

.. code-block:: ring

	import mypackage

	new myclass { myfunc() }

	package mypackage
		class myclass
			func myfunc			
				see "welcome"  + nl
			endfunc
		endclass
	endpackage

.. index:: 
	pair: Syntax Flexibility; Ignore new lines after keywords

Ignore new lines after keywords
===============================

Starting from Ring 1.8 the compiler will ignore new lines after keywords that expect tokens after it

Example:

.. code-block:: ring

	see 
	"
		Hello, World!
	"
	test()

	func 
	#======================#
		Test
	#======================#

		?
		"
	
		Hello from the Test function

		"

Output:

.. code-block:: none


	        Hello, World!


	        Hello from the Test function

.. index:: 
	pair: Syntax Flexibility; Automatic loading for syntax files

Automatic loading for syntax files
==================================

Starting from Ring 1.18 we have better syntax flexibility

* Support running source code files with any extension 

* Automatic loading for (ringsyntax.ring) file that exist in the current folder

For example in the the next screen shot

We have (ringsyntax.ring) that translate some of the Ring keywords to Arabic language

When we execute the file with Arabic name which means in English (hello.ring)

Ring will automatically execute (ringsyntax.ring) using Load Syntax command

Each Folder in the program could have it's optional (ringsyntax.ring) file

We can mix styles in the same project 

.. image:: autoloadsyntax.png
	:alt: autoloadsyntax

For Windows users, To use Arabic source code files with Ring, Set the language settings.

.. image:: winlangsettings.png
	:alt: winlangsettings


.. index:: 
	pair: Syntax Flexibility; Enable/Disable Hash Comments

Enable/Disable Hash Comments
============================

Starting from Ring 1.20 we have the next two commands supported by the Ring Scanner

* EnableHashComments
* DisableHashComments

Example:

.. code-block:: ring

	DisableHashComments
	#define = 10
	EnableHashComments
	# Just a comment
	DisableHashComments
	? #define
	EnableHashComments
	# End of program

