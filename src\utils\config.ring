# إعدادات نموذج الترانسفورمر
# يحتوي هذا الملف على جميع المعلمات والإعدادات اللازمة لتشغيل النموذج
#Load "stdlibcore.ring"

Class TransformerConfig {
    # معلمات النموذج الأساسية
    # هذه المعلمات تحدد حجم وقدرة النموذج
    nDModel = 512        # البعد الأساسي للنموذج (يؤثر على قوة التمثيل)
    nNumHeads = 8        # عدد رؤوس الانتباه (يسمح بالتركيز على جوانب مختلفة)
    nNumLayers = 6       # عدد طبقات المشفر وفك التشفير (يؤثر على عمق التعلم)
    nFFNDim = 2048      # بُعد الشبكة العصبية الأمامية (يؤثر على قدرة المعالجة)
    nMaxSeqLength = 512  # أقصى طول للتسلسل (يحدد طول النص المدخل)
    
    # معلمات المفردات
    # تحدد كيفية معالجة النصوص والكلمات
    nVocabSize = 32000   # حجم المفردات (يشمل الكلمات العربية والإنجليزية ورموز Ring)
    
    # معلمات التدريب
    # تتحكم في عملية تدريب النموذج
    nBatchSize = 32      # حجم الدفعة (يؤثر على سرعة واستقرار التدريب)
    nEpochs = 100       # عدد الدورات التدريبية
    fLearningRate = 0.0001 # معدل التعلم (يتحكم في حجم خطوات التحديث)
    fDropout = 0.1      # نسبة الإسقاط (تساعد في منع المبالغة في التخصيص)
    
    # الرموز الخاصة
    # رموز خاصة تستخدم في معالجة النصوص
    cPAD = "[PAD]"      # رمز الحشو (لجعل كل التسلسلات بنفس الطول)
    cUNK = "[UNK]"      # رمز غير معروف (للكلمات خارج المفردات)
    cBOS = "[BOS]"      # بداية التسلسل
    cEOS = "[EOS]"      # نهاية التسلسل
    cSEP = "[SEP]"      # فاصل (يفصل بين أجزاء المدخلات)
    cTRANS = "[TRANS]"  # علامة مهمة الترجمة
    cCODE = "[CODE]"    # علامة مهمة الكود
    
    # مسارات حفظ الملفات
    # تحدد أماكن حفظ النموذج والبيانات والسجلات
    cModelPath = "models/"  # مسار حفظ النماذج المدربة
    cDataPath = "data/"    # مسار البيانات
    cLogPath = "logs/"     # مسار ملفات السجل
    
    # دالة التهيئة
    # تقوم بإنشاء المجلدات اللازمة إذا لم تكن موجودة
    func init {
        if not DirExists(cModelPath) makedir(cModelPath) ok
        if not DirExists(cDataPath) makedir(cDataPath) ok
        if not DirExists(cLogPath) makedir(cLogPath) ok
    }
}
