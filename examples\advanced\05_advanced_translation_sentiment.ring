load "src/core/transformer.ring"
load "src/data/database/DatabaseManager.ring"
load "src/data/database/DataAnalyzer.ring"

class AdvancedTranslationService
    transformer
    db
    analyzer
    
    func init
        transformer = new Transformer
        db = new DatabaseManager
        analyzer = new DataAnalyzer
    
    func translateWithSentiment text, sourceLang, targetLang
        # ترجمة النص
        translated = transformer.translate(text, sourceLang, targetLang)
        
        # تحليل المشاعر للنص الأصلي والمترجم
        sourceScore = analyzer.analyzeSentiment(text, sourceLang)
        targetScore = analyzer.analyzeSentiment(translated, targetLang)
        
        # تخزين النتائج
        query = "INSERT INTO sentiment_analysis 
                (original_text, translated_text, source_lang, target_lang, 
                 source_sentiment, target_sentiment, sentiment_preserved) 
                VALUES (?, ?, ?, ?, ?, ?, ?)"
                
        sentimentPreserved = abs(sourceScore - targetScore) <= 0.2
        db.sqlite_execute(db.db, query, [
            text, translated, sourceLang, targetLang,
            sourceScore, targetScore, sentimentPreserved
        ])
        
        return [
            :original = text,
            :translated = translated,
            :sourceSentiment = sourceScore,
            :targetSentiment = targetScore,
            :preserved = sentimentPreserved
        ]
    
    func batchTranslateWithContext texts, sourceLang, targetLang
        results = []
        context = ""
        
        for text in texts
            # إضافة السياق للترجمة
            translationWithContext = transformer.translateWithContext(
                text, context, sourceLang, targetLang
            )
            
            # تحديث السياق
            context = text + " " + translationWithContext
            
            add(results, [
                :original = text,
                :translated = translationWithContext,
                :context = context
            ])
        next
        
        return results
    
    func analyzeTranslationQuality text, translation, sourceLang, targetLang
        return analyzer.evaluateTranslation(text, translation, sourceLang, targetLang)
    
    func close
        db.close()

# مثال على الاستخدام
service = new AdvancedTranslationService

? "مثال متقدم للترجمة مع تحليل المشاعر"
? "====================================="

# ترجمة مع تحليل المشاعر
texts = [
    "I absolutely love this amazing software!",
    "This is the worst experience ever.",
    "The product is okay, but could be better."
]

? "ترجمة وتحليل المشاعر:"
? "-------------------"
for text in texts
    result = service.translateWithSentiment(text, "en", "ar")
    ? "النص الأصلي: " + result[:original]
    ? "الترجمة: " + result[:translated]
    ? "مشاعر النص الأصلي: " + result[:sourceSentiment]
    ? "مشاعر الترجمة: " + result[:targetSentiment]
    ? "الحفاظ على المشاعر: " + (result[:preserved] ? "نعم" : "لا")
    ? ""
next

# ترجمة مع سياق
conversationTexts = [
    "Hi, my name is John.",
    "I work as a software developer.",
    "I specialize in Ring programming."
]

? "ترجمة مع سياق:"
? "------------"
results = service.batchTranslateWithContext(conversationTexts, "en", "ar")
for result in results
    ? "النص الأصلي: " + result[:original]
    ? "الترجمة: " + result[:translated]
    ? "السياق المستخدم: " + result[:context]
    ? ""
next

# تحليل جودة الترجمة
originalText = "The weather is beautiful today"
translation = "الطقس جميل اليوم"
quality = service.analyzeTranslationQuality(
    originalText, translation, "en", "ar"
)

? "تحليل جودة الترجمة:"
? "-----------------"
? "النص الأصلي: " + originalText
? "الترجمة: " + translation
? "درجة الدقة: " + quality[:accuracy]
? "درجة الطبيعية: " + quality[:fluency]
? "الدرجة الكلية: " + quality[:overall]

service.close()
