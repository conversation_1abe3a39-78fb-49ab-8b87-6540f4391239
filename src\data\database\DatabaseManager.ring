load "sqlitelib.ring"

class DatabaseManager
    sqlite db
    
    func init
        db = sqlite_init()
        if not fileExists("database.db")
            createDatabase()
        ok
        sqlite_open(db, "database.db")
        
    func createDatabase
        sqlite_open(db, "database.db")
        # إنشاء جدول الترجمات
        sqlite_execute(db, "
            CREATE TABLE translations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                source_text TEXT,
                target_text TEXT,
                source_lang VARCHAR(10),
                target_lang VARCHAR(10),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ")
        
        # إنشاء جدول توليد الكود
        sqlite_execute(db, "
            CREATE TABLE code_generations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                description TEXT,
                generated_code TEXT,
                language VARCHAR(50),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ")
        
        # إنشاء جدول مقاييس الأداء
        sqlite_execute(db, "
            CREATE TABLE performance_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                operation_type VARCHAR(50),
                execution_time FLOAT,
                memory_usage FLOAT,
                success_rate FLOAT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ")
        
    func addTranslation source_text, target_text, source_lang, target_lang
        query = "INSERT INTO translations (source_text, target_text, source_lang, target_lang) 
                VALUES (?, ?, ?, ?)"
        sqlite_execute(db, query, [source_text, target_text, source_lang, target_lang])
        return sqlite_last_insert_rowid(db)
        
    func getTranslation id
        query = "SELECT * FROM translations WHERE id = ?"
        return sqlite_query(db, query, [id])
        
    func addCodeGeneration description, code, language
        query = "INSERT INTO code_generations (description, generated_code, language) 
                VALUES (?, ?, ?)"
        sqlite_execute(db, query, [description, code, language])
        return sqlite_last_insert_rowid(db)
        
    func getCodeGeneration id
        query = "SELECT * FROM code_generations WHERE id = ?"
        return sqlite_query(db, query, [id])
        
    func addPerformanceMetric operation_type, execution_time, memory_usage, success_rate
        query = "INSERT INTO performance_metrics 
                (operation_type, execution_time, memory_usage, success_rate) 
                VALUES (?, ?, ?, ?)"
        sqlite_execute(db, query, [operation_type, execution_time, memory_usage, success_rate])
        
    func getPerformanceStats operation_type
        query = "SELECT AVG(execution_time) as avg_time, 
                       AVG(memory_usage) as avg_memory,
                       AVG(success_rate) as avg_success
                FROM performance_metrics 
                WHERE operation_type = ?"
        return sqlite_query(db, query, [operation_type])
        
    func close
        sqlite_close(db)
