.. index:: 
	Single: Using ZeroLib;  Introduction

=============
Using ZeroLib
=============

In this chapter we will learn how to use the ZeroLib library.


.. index:: 
	pair: Using ZeroLib; Introduction

Introduction
============

ZeroLib is a simple library written in Ring.

The library provide classes for Lists and String where the index starts from 0.


.. index:: 
	pair: Using ZeroLib; Z() function

Z() function
============

Syntax:

.. code-block:: none

	Z(String|List) ---> New Object (ZeroBasedString|ZeroBasedList)

.. index:: 
	pair: Using ZeroLib; ZeroBasedList Class

ZeroBasedList Class
===================

Simple class provide a List where the index starts from zero.

Methods:

===========================	======================================================================
Method						Description/Output
===========================	======================================================================
Init(List)			
Add(Value)			Add item to the list
Insert(nIndex,Value)		Inset Item after nIndex
Find(Value)			Find item
Delete(nIndex)			Delete item from the list 
Item(nIndex)			Get item from the list
First()				Get the first item in the list
Last()				Get the last item in the list
Set(nIndex,Value)		Set item value
FindInColumn(nCol,Value)	Find item in a column 
Sort()				Sort items - return new list
Reverse()			Reverse items - return new list
Swap(nIndex1,nIndex2)		Swap two items
===========================	======================================================================


Example:

.. code-block:: ring

	load "zerolib.ring"

	? "Using List - Index start from 0"
	List = Z( [1,2,3] )
	List.Add(4)
	List.Add(5)
	? List[0]
	? List[1]
	? List[2]
	? List[3]
	? List[4]
	nIndex = List.find(2)
	? "Find(2) = " + nIndex
	List.delete(0)
	? "After deleting the first item : List[0]" 
	? "Now List[0] = " + List[0] 

Output:

.. code-block:: ring 

	Using List - Index start from 0
	1
	2
	3
	4
	5
	Find(2) = 1
	After deleting the first item : List[0]
	Now List[0] = 2

.. index:: 
	pair: Using ZeroLib; ZeroBasedString Class

ZeroBasedString Class
=====================

Simple class provide a String where the index starts from zero.

===========================	======================================================================
Method				Description/Output
===========================	======================================================================
Init(String|Number) 
Lower()  			New String - Lower case characters
Upper()  			New String - Upper case characters
Left(x)  			New String - contains x characters from the left
Right(x) 			New String - contains x characters from the right
Lines()  			Number - Lines count
Trim()   			New String - Remove Spaces
Copy(x)  			New String - repeat string x times
strcmp(cString)   		Compare string with cString  
tolist()	  		List (String Lines to String Items)
tofile(cFileName) 		Write string to file
mid(nPos1,nPos2)  		New String - from nPos1 to nPos2
getfrom(nPos1)	  		New String - from nPos1 to the end of the string
replace(cStr1,cStr2,lCase) 	New String - Replace cStr1 with cStr2 , lCase (True=Match Case)
split()		  		List - Each Word as list item
startswith(substring)		Return true if the start starts with a substring 
endswith(substring)		Return true if the start ends with a substring
===========================	======================================================================

Example:

.. code-block:: ring

	load "zerolib.ring"

	? "Using String - Index start from 0"
	String = Z( "Welcome" )
	? String[0]
	? String[1]
	? String[2]
	? String[3]
	? String[4]
	? String[5]
	? String[6]

Output:

.. code-block:: ring 

	Using String - Index start from 0
	W
	e
	l
	c
	o
	m
	e

.. index:: 
	pair: Using ZeroLib; Source Code

Source Code
===========

We can find the library source code in this folder 

URL : https://github.com/ring-lang/ring/tree/master/libraries/zerolib



