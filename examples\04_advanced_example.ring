load "src/core/transformer.ring"
load "src/data/database/DatabaseManager.ring"
load "src/data/database/DataUpdater.ring"

class TranslationService
    transformer
    db
    updater
    
    func init
        transformer = new Transformer
        db = new DatabaseManager
        updater = new DataUpdater
        
    func translateAndStore text, sourceLang, targetLang
        try
            # ترجمة النص
            translated = transformer.translate(text, sourceLang, targetLang)
            
            # قياس الأداء
            executionTime = clock()  # وقت البدء
            
            # تخزين الترجمة
            translations = [[text, translated, sourceLang, targetLang]]
            updater.batchUpdateTranslations(translations)
            
            # حساب وتخزين مقاييس الأداء
            executionTime = clock() - executionTime
            metrics = [
                ["translation", executionTime, 100, 1.0]
            ]
            updater.updatePerformanceMetrics(metrics)
            
            return translated
            
        catch
            ? "حدث خطأ: " + cCatchError
            return ""
        done
        
    func generateAndStoreCode description
        try
            # توليد الكود
            executionTime = clock()
            generatedCode = transformer.generateCode(description)
            executionTime = clock() - executionTime
            
            # تخزين الكود المولد
            query = "INSERT INTO code_generations (description, generated_code, language) 
                    VALUES (?, ?, 'Ring')"
            db.sqlite_execute(db.db, query, [description, generatedCode])
            
            # تخزين مقاييس الأداء
            metrics = [
                ["code_generation", executionTime, 150, 1.0]
            ]
            updater.updatePerformanceMetrics(metrics)
            
            return generatedCode
            
        catch
            ? "حدث خطأ: " + cCatchError
            return ""
        done
        
    func getPerformanceReport
        query = new QueryBuilder("performance_metrics")
        query.select([
            "operation_type",
            "COUNT(*) as total_operations",
            "AVG(execution_time) as avg_time",
            "AVG(success_rate) as avg_success"
        ])
        query.groupBy("operation_type")
        return db.sqlite_query(db.db, query.build())
        
    func close
        db.close()

# مثال على الاستخدام
? "مثال متقدم على استخدام النظام"
? "=============================="

service = new TranslationService

# ترجمة نصوص
texts = [
    "Welcome to our advanced example",
    "This is a comprehensive test",
    "Let's try code generation too"
]

? "ترجمة نصوص متعددة:"
? "----------------"
for text in texts
    translated = service.translateAndStore(text, "en", "ar")
    ? "النص الأصلي: " + text
    ? "الترجمة: " + translated
    ? ""
next

# توليد كود
descriptions = [
    "دالة لحساب المتوسط الحسابي لمصفوفة من الأرقام",
    "صنف يمثل نقطة في المستوى الإحداثي"
]

? "توليد كود من أوصاف متعددة:"
? "------------------------"
for desc in descriptions
    code = service.generateAndStoreCode(desc)
    ? "الوصف: " + desc
    ? "الكود المولد:"
    ? code
    ? ""
next

# عرض تقرير الأداء
? "تقرير الأداء:"
? "------------"
report = service.getPerformanceReport()
for stat in report
    ? "نوع العملية: " + stat[:operation_type]
    ? "عدد العمليات: " + stat[:total_operations]
    ? "متوسط وقت التنفيذ: " + stat[:avg_time]
    ? "متوسط معدل النجاح: " + stat[:avg_success]
    ? ""
next

# إغلاق الخدمة
service.close()
