.. Ring documentation master file, created by
   sphinx-quickstart on Sun May 03 10:07:42 2015.
   You can adapt this file completely to your liking, but it should at least
   contain the root `toctree` directive.

Welcome to Ring's documentation!
================================

Contents:

.. toctree::
   :maxdepth: 2

   Applications developed in a few hours <ringapps>
   Introduction <introduction>
   Using Ring Notepad <ringnotepad>
   Getting Started - First Style <getting_started>
   Getting Started - Second Style <getting_started2>
   Getting Started - Third Style <getting_started3>
   Variables <variables>
   Operators <operators>
   Control Structures - First Style <controlstructures>
   Control Structures - Second Style <controlstructures2>
   Control Structures - Third Style <controlstructures3>
   Getting Input <getinput>
   Functions - First Style <functions>
   Functions - Second Style <functions2>
   Functions - Third Style <functions3>
   Program Structure <programstructure>
   Lists <lists>
   Strings <strings>
   Date and Time <dateandtime>
   Check Data Type and Conversion <checkandconvert>
   Mathematical Functions <mathfunc>
   Files <files>
   System Functions <systemfunc>
   Eval() and Debugging <evaldebug>
   Demo Programs <demo>
   ODBC Functions <odbc>
   MySQL Functions <mysql>
   SQLite Functions <sqlite>	
   PostgreSQL Functions <postgresql>
   Security and Internet Functions <secfunc>
   Object Oriented Programming (OOP) <oop>
   Functional Programming (FP) <fp>
   Reflection and Meta-programming <metaprog>
   Declarative Programming using Nested Structures <declarative>
   Natural language programming	<natural>
   Using the Natural Library <naturallibrary>
   Scope Rules for Variables and Attributes <scope>
   Scope Rules for Functions and Methods <scope2>
   Syntax Flexibility <syntaxflexibility>
   The Type Hints Library <typehints>
   The Trace Library and the Interactive Debugger <debug>
   Embedding Ring Language in Ring Programs <ringemb>
   Stdlib Functions <stdlib>
   Stdlib Classes <stdlibclasses>
   Desktop, WebAssembly and Mobile development using RingQt <qt>
   Using the Form Designer <formdesigner>
   Graphics Programming using RingQt3D <qt3d>
   Objects Library for RingQt Application <ringqtobjects>
   Multi-language Applications <multilanguage>
   Building RingQt Applications for Mobile <qtmobile>
   Building RingQt Applications for WebAssembly <qtwebassembly>
   Web Development (CGI Library) <web>
   Deploying Web Applications in the Cloud <deployincloud>
   Graphics and 2D Games programming using RingAllegro <allegro>	
   Demo Project - Game Engine for 2D Games <gameengine>	
   Building Games For Android <gameengineandorid>
   Developing Games using RingRayLib <ringraylib>
   Using RingOpenGL and RingFreeGLUT for 3D Graphics <usingopengl>
   Using RingOpenGL and RingAllegro for 3D Graphics <usingopengl2>
   Demo Project - The Gold Magic 800 Game <goldmagic800>
   Using RingTilengine <tilengine>
   Performance Tips <performancetips>
   Command Line Options	<compiler>
   Distributing Ring Applications (Manual) <distribute>
   Distributing Ring Applications using Ring2EXE <distribute_ring2exe>
   The Ring Package Manager (RingPM) <ringpm>
   ZeroLib Functions Reference <zerolib>
   FoxRing Functions Reference <foxringfuncsdoc>
   BigNumber Functions Reference <bignumber>
   CSVLib Functions Reference <csvlib>
   JSONLib Functions Reference <jsonlib>
   HTTPLib Functions Reference <httplib>
   TokensLib Functions Reference <tokenslib>
   Using RingLibCurl <libcurl>
   RingLibCurl Functions Reference <ringlibcurlfuncsdoc>
   Using RingSockets <socket>
   Using RingThreads <threads>
   Using RingLibui <libui>
   Using RingZip <ringzip>
   RingLibZip Functions Reference <ringlibzipfuncsdoc>
   RingMurmurHash Functions Reference <ringmurmurhashfuncsdoc>
   RingConsoleColors Functions Reference <ringconsolecolorsfuncsdoc>
   Using RingRogueUtil <usingrogueutil>
   RingAllegro Functions Reference <ringallegrofuncsdoc>
   Using RingLibSDL <libsdl>
   RingLibSDL Functions Reference <ringlibsdlfuncsdoc>
   Using Ringlibuv <libuv>
   RingLibuv Functions Reference <ringlibuvfuncsdoc>
   RingFreeGLUT Functions Reference <ringfreeglutfuncsdoc>
   RingStbImage Functions Reference <ringstbimage>	
   RingOpenGL (OpenGL 3.2) Functions Reference <ringopengl32funcsdoc>
   RingQt Classes and Methods Reference <qtclassesdoc>
   Using FastPro Extension <usingfastpro>
   Using RingPDFGen Extension <usingringpdfgen>
   Using References <usingref>
   Low Level Functions <lowlevel>
   Tutorial: Ring Extensions in C/C++  <extension_tutorial>
   Extension using the C/C++ languages <extension>
   Embedding Ring Language in C/C++ Programs <embedding>
   Code Generator for wrapping C/C++ Libraries <codegenerator>
   Create your first extension using the Code Generator <ringbeep>
   Using Ring for Raspberry Pi Pico Microcontroller <usingpico>
   Release Notes: Version 1.0 <languagedesign>
   Release Notes: Version 1.1 <whatisnew>
   Release Notes: Version 1.2 <whatisnew2>
   Release Notes: Version 1.3 <whatisnew3>
   Release Notes: Version 1.4 <whatisnew4>
   Release Notes: Version 1.5 <whatisnew5>
   Release Notes: Version 1.6 <whatisnew6>
   Release Notes: Version 1.7 <whatisnew7>
   Release Notes: Version 1.8 <whatisnew8>
   Release Notes: Version 1.9 <whatisnew9>
   Release Notes: Version 1.10 <whatisnew10>
   Release Notes: Version 1.11 <whatisnew11>
   Release Notes: Version 1.12 <whatisnew12>
   Release Notes: Version 1.13 <whatisnew13>
   Release Notes: Version 1.14 <whatisnew14>
   Release Notes: Version 1.15 <whatisnew15>
   Release Notes: Version 1.16 <whatisnew16>
   Release Notes: Version 1.17 <whatisnew17>
   Release Notes: Version 1.18 <whatisnew18>
   Release Notes: Version 1.19 <whatisnew19>
   Release Notes: Version 1.20 <whatisnew20>
   Release Notes: Version 1.21 <whatisnew21>
   Release Notes: Version 1.22 <whatisnew22>
   Using Other Code Editors <codeeditors>
   Frequently Asked Questions (FAQ) <faq>
   Building From Source Code <sourcecode>
   How to contribute? <contribute>
   Language Specification <reference>
   Resources <resources>