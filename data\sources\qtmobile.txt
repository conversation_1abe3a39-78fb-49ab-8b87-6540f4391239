.. index:: 
	single: Building RingQt Applications for Mobile; Introduction

=======================================
Building RingQt Applications for Mobile
=======================================

In this chapter we will learn about Building RingQt Applications for Mobile.

.. index:: 
	pair: Building RingQt Applications for Mobile; Download Requirements

Download Requirements
=====================

* Install Qt 5.15.2 
* Install Qt Creator (Tested using Qt Creator 6.0.1)

.. index:: 
	pair: Building RingQt Applications for Mobile; Install Qt for Android

Install Qt for Android
======================

(1) Check the next link : http://doc.qt.io/qt-5/androidgs.html

* Run Qt Creator
* Select Tools > Options > Devices > Android 
* Set the path for JDK, Android NDK and Android SDK Tools

(2) Using Qt Creator, We can download these requirements

* The Android SDK Tools (Tested using version 2.1)
* The Android NDK (Tested using version 21.3)
* Java SE Development Kit (JDK) v6 or later

(3) Update the Android SDK to get the API and tools packages required for development

* Tested using Android 7.1 (API 25)

Screen Shot:

.. image:: qtmobiletools.png
	:alt: Qt for Android

.. index:: 
	pair: Building RingQt Applications for Mobile; Using Ring2EXE

Using Ring2EXE
==============

We can use Ring2EXE to quickly prepare Qt project for our application

Example:

.. code-block:: none

	ring2exe myapp.ring -dist -mobileqt


.. note:: We can use the Distribute Menu in Ring Notepad

.. tip:: The option ( Prepare Qt project for Mobile devices ) in the Distribute Menu

.. index:: 
	pair: Building RingQt Applications for Mobile; The Qt project for your Ring application

The Qt project for your Ring application
========================================

After using Ring2EXE or the Distribute Menu in Ring Notepad 

*  Using the Qt Creator Open the generated Qt project 

	Folder : target/mobile/qtproject

	Project file : project.pro

.. image:: qtmobile_shot1.png
	:alt: Qt for Android - Shot 1

* Using Qt Creator, You will find the compiled Ring application in the resources (YourAppName.ringo)

	This file (Ring Object File) is generated by the Ring compiler using

.. code-block:: none

	ring YourAppName.ring -go -norun

* You can build your application using Qt Creator

When we open the project file, We can select the Kit

.. image:: qtmobile_shot2.png
	:alt: Qt for Android - Shot 2

After selecting the Kit, Click Configure Project

.. image:: qtmobile_shot3.png
	:alt: Qt for Android - Shot 3

Now We can build & Run the application

.. image:: qtmobile_shot4.png
	:alt: Qt for Andr-oid - Shot 4

The next screen shot for the application during the runtime

.. image:: qtmobile_shot5.png
	:alt: Qt for Android - Shot 5

(1) You can add your application images to the resources

	Or You can use any text editor (Notepad) and modify : project.qrc

(2) To find images from your Ring application, You need to use the file name in resources

	Example 

.. code-block:: ring

	if isandroid()
 		mypic = new QPixmap(":/cards.jpg")
	else
    		mypic = new QPixmap("cards.jpg")
	ok

.. index:: 
	pair: Building RingQt Applications for Mobile; Comments about developing for Android using RingQt

Comments about developing for Android using RingQt
==================================================

(1) The main project file is main.cpp 

	This file load Ring Compiler/Virtual Machine and RingQt 

	Then get the Ring Object File during the runtime from the resources

	Then run the Ring Object File (ringapp.ringo) using the Ring VM 

	Through main.cpp you can extract more files from the resources to temp. folder once you
	add them (create projects with many files).

(2) The next functions are missing from this Ring edition

	* Database (ODBC, SQLite & MySQL)

	* Security and Internet functions (LibCurl & OpenSSL)

	* RingAllegro (Allegro Library)

	* RingLibSDL (LibSDL Library)

	Just use Qt Classes through RingQt.

	For database access use the QSqlDatabase Class

.. note:: All of the missing libraries (LibCurl, OpenSSL & Allegro) can be compiled for Android, but they are not included in this Qt project.

(3) use if isandroid() when you want to modify the code just for android

Example:

.. code-block:: ring

	if isandroid()
		// Android code
	else
  		// other platforms
	ok

(4) Sometimes you will find that the button text/image is repeated in drawing !
it's Qt problem that you can avoid using the next code.

.. code-block:: ring

	if isandroid()
		setStyleSheet("
			border-style: outset;
			border-width: 2px;
			border-radius: 4px;
			border-color: black;
			padding: 6px;")
	 ok

(5)  Always use Layouts instead of manual setting of controls position and size. 

This is the best way to get the expected user interface to avoid problems like (controls with small/extra size)

(6) When you deal with Qt Classes you can determine the images from resources (you don't need to copy them using main.cpp)


Example: 

.. code-block:: ring

	if isandroid()
	    mypic = new QPixmap(":/cards.jpg")
	else
	    mypic = new QPixmap("cards.jpg")
	ok

Now RingQt comes with the AppFile() function to determine the file name 

Example:

.. code-block:: ring

	mypic = new QPixmap(AppFile("cards.jpg"))  # Desktop or Android

(7) When you update your project code, You don't have to use Ring2EXE to generate the Qt project again

Just use the Distribute Menu in Ring Notepad and select (Generate Ring Object File)

Then copy the YourAppName.ringo file to target/mobile/qtproject folder and accept replacing files.

(8) If your application folder contains a Qt resource file (project.qrc)

Then when you use Ring2EXE or Ring Notepad (Distribute - Prepare Qt project for Mobile devices) the 
resource file will be used 

See ring/applications/cards game as an example.
