.. index:: 
	single: Stdlib Functions; Introduction

================
Stdlib Functions
================

In this chapter we are going to learn about functions in the stdlib.ring

Before using the functions in the library, We must load the library first

.. code-block:: ring

	load "stdlib.ring"

Instead of using stdlib.ring we can use stdlibcore.ring

Using stdlibcore.ring we can use the StdLib functions (Without Classes)

.. code-block:: ring

	load "stdlibcore.ring"

This is useful when developing standalone console applications

Because using stdlib.ring (functions & classes) will load libraries like RingLibCurl, RingOpenSSL, etc.

Also, Using stdlibclasses.ring we can load stdlib classes without loading functions or extensions like RingLibCurl, RingOpenSSL, etc.

.. code-block:: ring

	load "stdlibclasses.ring"


.. index:: 
	pair: Stdlib Functions; isappcompiled() Function

IsAppCompiled() Function
========================

check whether the application has been compiled using Ring2EXE

Syntax:

.. code-block:: ring

	IsAppCompiled() ---> True/False

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	Puts("Test IsAppCompiled()")
	if IsAppCompiled() see "Application has been compiled using Ring2EXE"
	else see "Application is running under Ring interpreter" ok

.. index:: 
	pair: Stdlib Functions; apparguments() Function

AppArguments() Function
=======================

Get the effective arguments passed to the Ring script

Syntax:

.. code-block:: ring

	AppArguments() ---> The arguments as a list of strings

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	# Application Arguments
	Puts("Test AppArguments()")
	argsList = AppArguments()
	argsCount = Len(argsList)
	if argsCount = 0 see "No arguments passed to the Ring script" + nl
	else see "Ring script arguments = " + nl + list2str(argsList) + nl ok

.. index:: 
	pair: Stdlib Functions; apppath() Function

AppPath() Function
==================

Get the path of the application folder

Syntax:

.. code-block:: ring

	AppPath() ---> The path as String

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	# Application Path
	Puts("Test AppPath()")
	See AppPath() + nl

.. index:: 
	pair: Stdlib Functions; JustFilePath() Function

JustFilePath() Function
=======================

Get the path of the file, remove the file name.

Syntax:

.. code-block:: ring

	JustFilePath(cFile) ---> The path as String

Example:

.. code-block:: ring

	load "stdlibcore.ring"
 
	see  justfilePath("b:\ring\applications\rnote\rnote.ring")

Output:

.. code-block:: ring

	b:\ring\applications\rnote\


.. index:: 
	pair: Stdlib Functions; JustFileName() Function

JustFileName() Function
=======================

Get the file, remove the file path.

Syntax:

.. code-block:: ring

	JustFileName(cFile) ---> The file name as String

Example:

.. code-block:: ring

	load "stdlibcore.ring"
 
	see justfileName("b:\ring\applications\rnote\rnote.ring") 

Output:

.. code-block:: ring

	rnote.ring


.. index:: 
	pair: Stdlib Functions; value() Function

Value() Function
================

create a copy from a list or object

Syntax:

.. code-block:: ring

	value(List) ---> new list 

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	aList = 1:10
	del(value(aList),1) # delete first item
	see aList 	    # print numbers from 1 to 10

.. index:: 
	pair: Stdlib Functions; times() Function

Times() Function
================

Execute a Function nCount times

Syntax:

.. code-block:: ring

	Times(nCount,function)

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	Puts("Test Times()")
	Times ( 3 , func { see "Hello, World!" + nl } )

.. index:: 
	pair: Stdlib Functions; map() Function

Map() Function
==============

Execute a Function on each list item

Syntax:

.. code-block:: ring

	 Map(alist,function) ---> List

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	Puts("Test Map()")
	See Map( 1:10, func x { return x*x } )

.. index:: 
	pair: Stdlib Functions; filter() Function

Filter() Function
=================

Execute a Function on each list item to filter items

Syntax:

.. code-block:: ring

	Filter(alist,function) ---> List

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	Puts("Test Filter()")
	See Filter( 1:10 , func x { if x <= 5 return true else return false ok } )

.. index:: 
	pair: Stdlib Functions; reduce() Function

Reduce() Function
=================

Apply function cFunc to each result xResult from a list aList, return an 
accumulated value xResult

The input list aList, the optional intial value xInitial and the output xResult, need 
to be the same Type

Syntax:

.. code-block:: ring

	Reduce(aList,cFunc,xInitial) ---> final value

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	? Reduce( 1:3, func x, y { return x + y }, 0 )
	? Reduce( ["I","Love","Ring"], func x, y { return x + y }, "" )

Output:

.. code-block:: ring

	6
	ILoveRing

.. index:: 
	pair: Stdlib Functions; split() Function

Split() Function
================

Convert string words to list items

.. note:: This function remove all leading and trailing spaces from a string.

.. tip:: To avoid removing all leading and trailing spaces use the SplitMany() function.

Syntax:

.. code-block:: ring

	Split(cstring,delimiter) ---> List

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	Puts("Test Split()")
	See Split("one two three four five"," ")


.. index:: 
	pair: Stdlib Functions; splitmany() Function

SplitMany() Function
====================

Convert string words to list items. Allow many delimiters.

Syntax:

.. code-block:: ring

	SplitMany(cstring,delimiters as string or list) --> List

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	Puts("Test SplitMany()")
	See SplitMany("one,two,three,four and five"," ,")

.. index:: 
	pair: Stdlib Functions; capitalized() Function

Capitalized() Function
======================

Return a copy of a string with the first letter capitalized

Syntax:

.. code-block:: ring

	Capitalized(string) ---> string

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	Puts("Test Capitalized()")
	See capitalized("welcome to the Ring Programming Language")

.. index:: 
	pair: Stdlib Functions; isspecial() Function

IsSpecial() Function
====================

Check whether a character is special or not


Syntax:

.. code-block:: ring

	IsSpecial(char) ---> True/False

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	Puts("Test Isspecial()")
	See "Isspecial  = " + isSpecial("%") + nl

.. index:: 
	pair: Stdlib Functions; isvowel() Function

IsVowel() Function
==================

Check whether a character is vowel or not

Syntax:

.. code-block:: ring

	IsVowel(char) ---> True/False

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	Puts("Test Isvowel()")
	See "Isvowel = " + isVowel("c") + nl


.. index:: 
	pair: Stdlib Functions; linecount() Function

LineCount() Function
====================

Return the lines count in a text file.

Syntax:

.. code-block:: ring

	LineCount(cFileName) ---> Lines Count as number

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	Puts("Test Linecount()")
	See "the number of lines = " + lineCount("test.ring")

.. index:: 
	pair: Stdlib Functions; factorial() Function

Factorial() Function
====================

Return the factorial of a number

Syntax:

.. code-block:: ring

	Factorial(number) ---> number

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	Puts("Test Factorial()")
	see "6 factorial is : " + Factorial(6)

.. index:: 
	pair: Stdlib Functions; fibonacci() Function

Fibonacci() Function
====================

Return the fibonacci number

Syntax:

.. code-block:: ring

	Fibonacci(number) ---> number

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	Puts("Test Fibonacci()")
	see "6 Fibonacci is : " + Fibonacci(6)

.. index:: 
	pair: Stdlib Functions; isprime() Function

IsPrime() Function
==================

Check whether a number is prime or not

Syntax:

.. code-block:: ring

	isprime(number) ---> Number

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	Puts("Test Isprime()")
	if isPrime(16) see "16 is a prime number"
	else see "16 is not a prime number" ok

.. index:: 
	pair: Stdlib Functions; sign() Function

Sign() Function
===============

Returns an integer value indicating the sign of a number.

Syntax:

.. code-block:: ring

	Sign(number) ---> number ( -1 = negative , 0 , 1 (positive) )

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	Puts("Test Sign()")
	see "sign of 12 is = " + sign(12) + nl

.. index:: 
	pair: Stdlib Functions; list2file() Function

List2File() Function
====================

Write list items to text file (each item in new line).

Syntax:

.. code-block:: ring

	List2File(aList,cFileName)

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	# Test List2File
	Puts("Test List2File()")
	list2file(1:100,"myfile.txt")


.. index:: 
	pair: Stdlib Functions; file2list() Function

File2List() Function
====================

Read text file and convert lines to list items

Syntax:

.. code-block:: ring

	File2List(cFileName) ---> List

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	# Test File2List
	Puts("Test File2List()")
	see len(file2list("myfile.txt"))

.. index:: 
	pair: Stdlib Functions; startswith() Function

StartsWith() Function
=====================

Returns true if the given string starts with the specified substring.

Leading white spaces are ignored.

Syntax:

.. code-block:: ring

	StartsWith(string, substring) ---> True/False


Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	Puts("Test Startswith()")
	see Startswith("CalmoSoft", "Calmo") + nl

.. index:: 
	pair: Stdlib Functions; endswith() Function

EndsWith() Function
===================

Returns true if the given string ends with the specified substring.

Trailing white spaces are ignored.

Syntax:

.. code-block:: ring

	Endswith(string, substring) ---> True/False

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	Puts("Test Endswith()")
	see endsWith("CalmoSoft", "Soft") + nl

.. index:: 
	pair: Stdlib Functions; gcd() Function

GCD() Function
==============

Finding of the greatest common divisor of two integers.

Syntax:

.. code-block:: ring

	Gcd(number,number) ---> number

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	Puts("Test Gcd()")
	see gcd (24, 32) + nl


.. index:: 
	pair: Stdlib Functions; lcm() Function

LCM() Function
==============

Compute the least common multiple of two integers.

Syntax:

.. code-block:: ring

	lcm(number,number) ---> number

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	Puts("Test Lcm()")
	see Lcm(24,36) + nl

.. index:: 
	pair: Stdlib Functions; sumlist() Function

SumList() Function
==================

Compute the sum of a list of integers.

Syntax:

.. code-block:: ring

	sumlist(list) ---> number

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	Puts("Test Sumlist()")
	aList = [1,2,3,4,5]
	see Sumlist(aList) + nl

.. index:: 
	pair: Stdlib Functions; prodlist() Function

ProdList() Function
===================

Compute the product of a list of integers.

Syntax:

.. code-block:: ring

	prodlist(list) ---> number

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	Puts("Test Prodlist()")
	aList = [1,2,3,4,5]
	see Prodlist(aList) + nl

.. index:: 
	pair: Stdlib Functions; evenorodd() Function

EvenOrOdd() Function
====================

Test whether an integer is even or odd.

Result of test (1=odd 2=even).

Syntax:

.. code-block:: ring

	evenorodd(number) ---> 1 (odd) or 2 (even)

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	Puts("Test Evenorodd()")
	nr = 17
	see Evenorodd(nr) + nl

.. index:: 
	pair: Stdlib Functions; factors() Function

Factors() Function
==================

Compute the factors of a positive integer.

Syntax:

.. code-block:: ring

	factors(number) ---> list

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	Puts("Test Factors()")
	n = 45
	aList = factors(n)
	see "Factors of " + n + " = "
	for i = 1 to len(aList)
	    see "" + aList[i] + " "
	next


.. index:: 
	pair: Stdlib Functions; ispalindrome() Function

IsPalindrome() Function
=======================

Check if a sequence of characters is a palindrome or not. 

Syntax:

.. code-block:: ring

	IsPalindrome(String) ---> True/False

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	Puts("Test IsPalindrome()")
	cString = "radar"
	see IsPalindrome(cString)


.. index:: 
	pair: Stdlib Functions; isleapyear() Function

IsLeapYear() Function
=====================

Check whether a given year is a leap year in the Gregorian calendar. 

Syntax:

.. code-block:: ring

	Isleapyear(number) ---> True/False

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	Puts("Test Isleapyear()")
	year = 2016
	if Isleapyear(year) see "" + year + " is a leap year."
	else see "" + year + " is not a leap year." ok


.. index:: 
	pair: Stdlib Functions; binarydigits() Function

BinaryDigits() Function
=======================

Compute the sequence of binary digits for a given non-negative integer. 

Syntax:

.. code-block:: ring

	binarydigits(number) ---> string

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	Puts("Test Binarydigits()")
	b = 35
	see "Binary digits of " + b + " = " + Binarydigits(b)

.. index:: 
	pair: Stdlib Functions; matrixmulti() Function

MatrixMulti() Function
======================

Multiply two matrices together. 

Syntax:

.. code-block:: ring

	Matrixmulti(List,List) ---> List

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	# Multiply two matrices together.
	Puts("Test Matrixmulti()")
	A = [[1,2,3], [4,5,6], [7,8,9]]
	B = [[1,0,0], [0,1,0], [0,0,1]]
	see Matrixmulti(A, B)


.. index:: 
	pair: Stdlib Functions; matrixtrans() Function

MatrixTrans() Function
======================

Transpose an arbitrarily sized rectangular Matrix. 

Syntax:

.. code-block:: ring

	Matrixtrans(List) ---> List

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	# Transpose an arbitrarily sized rectangular Matrix.
	Puts("Test Matrixtrans()")
	matrix = [[78,19,30,12,36], [49,10,65,42,50], [30,93,24,78,10], [39,68,27,64,29]]
	see Matrixtrans(matrix)

.. index:: 
	pair: Stdlib Functions; dayofweek() Function

DayOfWeek() Function
====================

Return the day of the week of given date. (yyyy-mm-dd)

Syntax:

.. code-block:: ring

	dayofweek(string) ---> string

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	# Return the day of the week of given date.
	Puts("Test Dayofweek()")
	date = "2016-04-24"
	see "Data : " + date + " - Day : " + Dayofweek(date) + nl

.. index:: 
	pair: Stdlib Functions; permutation() Function

Permutation() Function
======================

Generates all permutations of n different numerals.

Syntax:

.. code-block:: ring

	permutation(list)

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	# Generates all permutations of n different numerals
	Puts("Test Permutation()")
	list = [1, 2, 3, 4]
	for perm = 1 to 24
		for i = 1 to len(list)
	        	see list[i] + " "
		next
     		see nl
		Permutation(list)
	next

.. index:: 
	pair: Stdlib Functions; readline() Function

ReadLine() Function
===================

Read line from file

Syntax:

.. code-block:: ring

	readline(fp) ---> string

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	# Read a file line by line.
	Puts("Test Readline()")
	fp = fopen("test.ring","r")
	while not feof(fp)
	See Readline(fp) end
	fclose(fp)

.. index:: 
	pair: Stdlib Functions; substring() Function

SubString() Function
====================

Return a position of a substring starting from a given position in a string.

Syntax:

.. code-block:: ring

	Substring(str,substr,npos) ---> string

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	# Return a position of a substring starting from a given position in a string.
	Puts("Test Substring()")
	a = "abcxyzqweabc"
	b = "abc"
	i = 4
	see substring(a,b,i)

.. index:: 
	pair: Stdlib Functions; changestring() Function

ChangeString() Function
=======================

Change substring from given position to a given position with another substring.

Syntax:

.. code-block:: ring

	Changestring(cString, nPos1, nPos2, cSubstr) ---> cString

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	# Change substring from given position for given position with a substring.
	Puts("Test Changestring()")
	see Changestring("Rmasdg",2,5,"in")	# Ring

.. index:: 
	pair: Stdlib Functions; sleep() Function

Sleep() Function
================

Sleep for the given amount of time.

Syntax:

.. code-block:: ring

	sleep(nSeconds) 

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	Puts("Test Sleep()")
	see "Wait 3 Seconds!"
	Sleep(3)
	see nl

.. index:: 
	pair: Stdlib Functions; ismainsourcefile() Function

IsMainSourceFile() Function
===========================

Check if the current file is the main source file

Syntax:

.. code-block:: ring

	IsMainSourceFile() ---> True/False

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	if ismainsourcefile()
		# code 
	ok

.. index:: 
	pair: Stdlib Functions; makedir() Function

MakeDir() Function
==================

Make Directory

Syntax:

.. code-block:: ring

	MakeDir(String)

Example:

.. code-block:: ring

	Load "stdlibcore.ring"

	# Create Directory
	puts("create Directory : myfolder")
	makedir("myfolder")


.. index:: 
	pair: Stdlib Functions; FSize() Function

Fsize() Function
================

The function return the file size in bytes.

Syntax:

.. code-block:: ring

	FSize(File Handle) ---> Number (File Size in Bytes)

.. index:: 
	pair: Stdlib Functions; TrimAll() Function

TrimAll() Function
==================

Remove all spaces and tabs characters from a string

Syntax:

.. code-block:: ring

	TrimAll(cString) ---> cString # Without Spaces and Tabs

.. index:: 
	pair: Stdlib Functions; TrimLeft() Function

TrimLeft() Function
===================

Remove all spaces and tabs characters from the left side of a string

Syntax:

.. code-block:: ring

	TrimLeft(cString) ---> cString # Without Spaces and Tabs from the left side

.. index:: 
	pair: Stdlib Functions; TrimRight() Function

TrimRight() Function
====================

Remove all spaces and tabs characters from the right side of a string

Syntax:

.. code-block:: ring

	TrimRight(cString) ---> cString # Without Spaces and Tabs from the right side

.. index:: 
	pair: Stdlib Functions; EpochTime() Function

EpochTime() Function
====================

Return the Epoch Time

Syntax:

.. code-block:: ring

	EpochTime(cDate,cTime) ---> nEpochTime

Example:

.. code-block:: ring

	see EpochTime( Date(), Time() )

.. index:: 
	pair: Stdlib Functions; SystemCmd() Function

SystemCmd() Function
====================

We can execute system commands using the SystemCmd() function that outputs to a variable

Syntax:

.. code-block:: ring

	SystemCmd(cCommand)

Example:

.. code-block:: ring

	cYou  = SystemCmd("whoami") 	   # User Name logged in is output a variable
	cThem = SystemCmd("dir c:\Users")  # Directory List is output to a variable


.. index:: 
	pair: Stdlib Functions; ListAllFiles() Function

ListAllFiles() Function
=======================

Using this function we can quickly do a process on a group of files in a folder and it's sub folders.

Syntax:

.. code-block:: ring

	ListAllFiles(cFolder,cExtension) ---> List of Files

Example:

.. code-block:: ring

	aList = ListAllFiles("c:/ring/ringlibs","ring") # *.ring only
	aList = sort(aList)
	see aList

Example:

.. code-block:: ring

	see listallfiles("b:/ring/libraries/weblib","") # All Files

.. index:: 
	pair: Stdlib Functions; SystemSilent() Function

SystemSilent() Function
=======================

We can execute system commands using the SystemSilent() function to avoid displaying the output!

Syntax:

.. code-block:: ring

	SystemSilent(cCommand)

.. index:: 
	pair: Stdlib Functions; OSCreateOpenFolder() Function

OSCreateOpenFolder() Function
=============================

Create folder then change the current folder to this new folder

Syntax:

.. code-block:: ring

	OSCreateOpenFolder(cCommand)


.. index:: 
	pair: Stdlib Functions; OSCopyFolder() Function

OSCopyFolder() Function
=======================

Copy folder to the current folder 

Parameters : The path to the parent folder and the folder name to copy

Syntax:

.. code-block:: ring

	OSCopyFolder(cParentFolder,cFolderName)

Example

To copy the folder b:\\ring\\ringlibs\\stdlib to the current folder

.. code-block:: ring

	OSCopyFolder("b:\ring\ringlibs\","stdlib")


.. index:: 
	pair: Stdlib Functions; OSDeleteFolder() Function

OSDeleteFolder() Function
=========================

Delete Folder in the current Directory

Syntax:

.. code-block:: ring

	OSDeleteFolder(cFolderName)

.. index:: 
	pair: Stdlib Functions; OSCopyFile() Function

OSCopyFile() Function
=====================

Copy File to the current directory

Syntax:

.. code-block:: ring

	OSCopyFile(cFileName)

.. index:: 
	pair: Stdlib Functions; OSDeleteFile() Function

OSDeleteFile() Function
=======================

Delete File

Syntax:

.. code-block:: ring

	OSDeleteFile(cFileName)

.. index:: 
	pair: Stdlib Functions; OSRenameFile() Function

OSRenameFile() Function
=======================

Rename File

Syntax:

.. code-block:: ring

	OSRenameFile(cOldFileName,cNewFileName)


.. index:: 
	pair: Stdlib Functions; List2Code() Function 

List2Code() Function 
====================

This function convert a Ring list during the runtime to Ring source code that we can save to source files.

The list may contains strings, numbers or sub lists.

Example:

.. code-block:: ring

	load "stdlibcore.ring"
	aList = 1:10
	? list2Code(aList)

Output:

.. code-block:: ring

	[
		1,2,3,4,5,6,7,8,9,10
	]


.. index:: 
	pair: Stdlib Functions; Str2ASCIIList() Function

Str2ASCIIList() Function
========================

Convert a string of bytes to a list of numbers where each item represent
the ASCII code of one byte in the string.

Syntax:

.. code-block:: ring

	Str2ASCIIList(String) ---> List of numbers

.. index:: 
	pair: Stdlib Functions; ASCIIList2Str() Function

ASCIIList2Str() Function
========================

Convert a list of numbers where each item represent
the ASCII code of one byte to a string of bytes.

Syntax:

.. code-block:: ring

	ASCIIList2Str(List of numbers) ---> String


Example:

.. code-block:: ring

	load "stdlibcore.ring"

	cStr = "MmMm"

	aList = Str2ASCIILIST(cStr)
	? aList 

	cStr2 = ASCIIList2Str(aList)
	? cStr2
	? len(cStr2)

Output:

.. code-block:: none

	77
	109
	77
	109

	MmMm
	4


.. index:: 
	pair: Stdlib Functions; IsListContainsItems() Function

IsListContainsItems() Function
==============================

Syntax:

.. code-block:: ring

	IsListContainsItems(aParent,aChild) ----> True/False

Example:

.. code-block:: ring

	load "stdlibcore.ring"
	aList1 = "a":"z"
	aList2 = [:h,:l,:p,:u]
	? IsListContainsItems(aList1,aList2)

.. index:: 
	pair: Stdlib Functions; IsBetween() Function

IsBetween() Function
====================

Syntax:

.. code-block:: ring

	IsBetween(nNumber,nMin,nMax) ----> True/False

Example:

.. code-block:: ring

	load "stdlibcore.ring"
	? isBetween(1,3,4)
	? isBetween(1,-3,4)
	? isBetween(4,1,6)
	? isBetween(4,3,4)

.. index:: 
	pair: Stdlib Functions; TimeInfo() Function

TimeInfo() Function
===================

Syntax:

.. code-block:: ring

	TimeInfo(cInformation) ----> String 

The cInformation value could be

.. code-block:: ring

	:hour_24
	:hour_12
	:minutes
	:seconds
	:time
	:day_short
	:day_long
	:month_short
	:month_long
	:date_time
	:day
	:day_year
	:month_year
	:am_pm
	:week_year
	:day_week
	:date
	:year_century
	:year
	:time_zone
	:percent_sign

Example:

.. code-block:: ring

	load "stdlibcore.ring"
	? timeInfo(:date)
	? timeInfo(:time)
	? timeInfo(:hour_12)

.. index:: 
	pair: Stdlib Functions; RandomList() Function

RandomList() Function
=====================

Syntax:

.. code-block:: none

	RandomList(aList) --> List contains the same items using Random order

Example:

.. code-block:: ring

	load "stdlibcore.ring"
	aList = 1:5
	? RandomList(aList)

.. index:: 
	pair: Stdlib Functions; RandomItem() Function

RandomItem() Function
=====================

Pick an item from a list (Random Choice)

Syntax:

.. code-block:: none

	RandomItem(aList) --> Item 

Example:

.. code-block:: ring

	load "stdlibcore.ring"
	aList = 1:5
	? RandomItem(aList)

.. index:: 
	pair: Stdlib Functions; CheckEquality() Function

CheckEquality() Function
========================

Check if two items are equal.
Deep comparison is performed if the two items are lists
Return 1 if both items are equal and 0 otherwise

Syntax:

.. code-block:: none

	CheckEquality(aItem1,aItem2) --> value = 1 if aItem1 = aItem2
				       value = 0 if aItem1 != aItem2

Example:

.. code-block:: ring

	load "stdlibcore.ring"
	aList1 = ["one", 2, [3]]
	aList2 = ["one", 2]
	aList2 + [3]
	? CheckEquality(aList1,aList2)


.. index:: 
	pair: Stdlib Functions; NumOrZero() Function

NumOrZero() Function
====================

This is a new function added to stdlibcore.ring

Using this function we get a number as output (No runtime errors)

Example:

.. code-block:: ring


	load "stdlibcore.ring"

	? numorzero(10)
	? numorzero("10")
	? numorzero("10.2")
	? numorzero("10.2 abc")
	? numorzero("What")
	? numorzero([10])
	? numorzero(new point)

	class point

Output:

.. code-block:: none

	10
	10
	10.20
	0
	0
	0
	0

