load "DatabaseManager.ring"

class DataCleaner
    db
    
    func init
        db = new DatabaseManager
        
    func cleanText text
        # إزالة الأحرف الخاصة
        text = substr(text, "\t", " ")
        text = substr(text, "\n", " ")
        # إزالة المسافات المتعددة
        while find(text, "  ")
            text = substr(text, "  ", " ")
        end
        return trim(text)
        
    func normalizeLanguageCode code
        code = lower(trim(code))
        switch code
            case "english"     code = "en"
            case "arabic"      code = "ar"
            case "french"      code = "fr"
            case "spanish"     code = "es"
        end
        return code
        
    func validateTranslationData sourceText, targetText, sourceLang, targetLang
        if len(sourceText) = 0 return false ok
        if len(targetText) = 0 return false ok
        if len(sourceLang) != 2 return false ok
        if len(targetLang) != 2 return false ok
        return true
        
    func cleanTranslationsTable
        query = "UPDATE translations 
                SET source_text = TRIM(source_text),
                    target_text = TRIM(target_text),
                    source_lang = LOWER(source_lang),
                    target_lang = LOWER(target_lang)
                WHERE source_text LIKE '% %' 
                   OR target_text LIKE '% %'
                   OR source_lang != LOWER(source_lang)
                   OR target_lang != LOWER(target_lang)"
        db.sqlite_execute(db.db, query)
        
    func removeDuplicateTranslations
        query = "DELETE FROM translations 
                WHERE id NOT IN (
                    SELECT MIN(id)
                    FROM translations
                    GROUP BY source_text, target_text, source_lang, target_lang
                )"
        db.sqlite_execute(db.db, query)
        
    func removeInvalidRecords
        query = "DELETE FROM translations 
                WHERE source_text IS NULL 
                   OR target_text IS NULL
                   OR source_lang NOT IN ('en', 'ar', 'fr', 'es')
                   OR target_lang NOT IN ('en', 'ar', 'fr', 'es')"
        db.sqlite_execute(db.db, query)
        
    func validatePerformanceMetrics
        query = "DELETE FROM performance_metrics 
                WHERE execution_time < 0 
                   OR memory_usage < 0
                   OR success_rate < 0 
                   OR success_rate > 1"
        db.sqlite_execute(db.db, query)
        
    func cleanAllTables
        cleanTranslationsTable()
        removeDuplicateTranslations()
        removeInvalidRecords()
        validatePerformanceMetrics()
