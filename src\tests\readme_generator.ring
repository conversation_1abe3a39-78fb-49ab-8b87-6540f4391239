load "stdlib.ring"



# تشغيل المثال إذا تم تشغيل الملف مباشرة
if isMainSourceFile() {
    generator = new ReadmeGenerator()
    
    # تعيين معلومات المشروع
    generator.setProjectInfo(
        "نظام اختبار المحول الذكي",
        "نظام شامل لاختبار وتقييم نموذج المحول للترجمة وتوليد الكود"
    )
    
    # إضافة قسم
    generator.addSection(
        "🏗️ هيكل الاختبارات",
        "يتضمن النظام مجموعة شاملة من الاختبارات الوحدية والتكاملية"
    )
    
    # إضافة مكون
    generator.addComponent(
        "اختبارات الترجمة",
        "اختبارات شاملة لوظائف الترجمة",
        [
            "اختبار دقة الترجمة",
            "اختبار معالجة الحالات الخاصة",
            "اختبار الأداء"
        ]
    )
    
    # إضافة مثال استخدام
    generator.addUsageExample(
        "تشغيل اختبارات الترجمة",
        "load 'translation_tests.ring'\nrunTests()",
        "يقوم بتشغيل جميع اختبارات الترجمة وعرض النتائج"
    )
    
    # إضافة مقياس
    generator.addMetric(
        "BLEU Score",
        "مقياس لتقييم جودة الترجمة",
        "يستخدم لمقارنة الترجمة المولدة مع الترجمة المرجعية"
    )
    
    # إضافة تحسين مستقبلي
    generator.addFutureImprovement(
        "اختبارات متوازية",
        "تنفيذ الاختبارات بشكل متوازٍ لتحسين الأداء"
    )
    
    # توليد وحفظ الملف
    generator.saveReadme("README1.md")
}


# منشئ ملفات README
Class ReadmeGenerator {
    # المتغيرات
    outputPath      # مسار الملف الناتج
    projectInfo     # معلومات المشروع
    
    func init {
        projectInfo = [
            "title"= "",
            "description"= "",
            "sections"= [],
            "components"= [],
            "features"= [],
            "usage"= [],
            "metrics"= [],
            "future"= []
        ]
    }
    
    # تعيين معلومات المشروع
    func setProjectInfo(title, description) {
        projectInfo["title"] = title
        projectInfo["description"] = description
    }
    
    # إضافة قسم
    func addSection(title, content) {
        add(projectInfo[:sections] , [:title = title, :content = content])
    }
    
    # إضافة مكون
    func addComponent(name, description, features) {
        add(projectInfo["components"] , [
            "name"= name,
            "description"= description,
            "features"= features
        ])
    }
    
    # إضافة ميزة
    func addFeature(name, description) {
        add(projectInfo["features"] , ["name"= name, "description"= description])
    }
    
    # إضافة مثال استخدام
    func addUsageExample(title, code, description) {
        add(projectInfo["usage"] , [
            "title"= title,
            "code"= code,
            "description"= description
        ])
    }
    
    # إضافة مقياس
    func addMetric(name, description, usage) {
        add(projectInfo["metrics"] , [
            "name"= name,
            "description"= description,
            "usage"= usage
        ])
    }
    
    # إضافة تحسين مستقبلي
    func addFutureImprovement(title, description) {
        add(projectInfo["future"] , [
            "title"= title,
            "description"= description
        ])
    }
    
    # توليد محتوى README
    func generateContent {
        content = "# 🤖 " + projectInfo["title"] + nl + nl
        content += "## 📝 نظرة عامة" + nl + projectInfo["description"] + nl + nl
        
        # إضافة الأقسام
        for section in projectInfo["sections"] {
            content += "## " + section["title"] + nl
            content += section["content"] + nl + nl
        }
        
        # إضافة المكونات
        content += "## 🚀 المكونات الرئيسية" + nl + nl
        for comp in projectInfo["components"] {
            content += "### " + comp["name"] + nl
            content += comp["description"] + nl
            for feature in comp["features"] {
                content += "- " + feature + nl
            }
            content += nl
        }
        
        # إضافة الميزات
        content += "## ✨ الميزات" + nl + nl
        for feature in projectInfo["features"] {
            content += "### " + feature["name"] + nl
            content += feature["description"] + nl + nl
        }
        
        # إضافة أمثلة الاستخدام
        content += "## 🛠️ كيفية الاستخدام" + nl + nl
        for example in projectInfo["usage"] {
            content += "### " + example["title"] + nl
            content += "```ring\n" + example["code"] + nl + "```" +nl
            content += example["description"] + nl + nl
        }
        
        # إضافة المقاييس
        content += "## 📊 المقاييس المستخدمة" + nl + nl
        for metric in projectInfo["metrics"] {
            content += "### " + metric["name"] + nl
            content += metric["description"] + nl
            content += "استخدام: " + metric["usage"] + nl + nl
        }
        
        # إضافة التحسينات المستقبلية
        content += "## 📈 التحسينات المستقبلية" + nl + nl
        for improvement in projectInfo["future"] {
            content += "### " + improvement["title"] + nl
            content += improvement["description"] + nl + nl
        }
        
        return content
    }
    
    # حفظ الملف
    func saveReadme(path) {
        content = generateContent()
        write(path, content)
        ? "تم إنشاء ملف README في: " + path
    }
}
