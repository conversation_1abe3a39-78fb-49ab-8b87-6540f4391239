﻿.. index:: 
     single: Performance Tips; Introduction

================
Performance Tips
================

In this chapter we will learn more about the Ring performance.

Tested using Victus Laptop [13th Gen Intel(R) Core(TM) i7-13700H, Windows 11, Ring 1.21]

.. index:: 
     pair: Performance Tips; Introduction

Introduction
============

Ring is designed to be a simple, small and flexible language in the first place, but also it is fast enough for many applications.

Ring can do each of the following tasks in around one second. 

(1) Compiling 200,000 lines of code
(2) Executing an empty loop that count from 1 to 100,000,000
(3) Creating list contains 7,000,000 items then summing all of the list items
(4) Printing numbers from 1 to 40,000 using command prompt
(5) Printing numbers from 1 to 500,000 using output redirection and Ring Notepad
(6) Adding 60,000 nodes to the TreeWidget in GUI applications
(7) Adding 60,000 items to the ListWidget in GUI applications
(8) Executing 3000 search operations using linear search in a list contains 100,000 items, trying to find the last item (The worst case)

Also when we need more speed we can use C/C++ extensions! 

Example:

.. code-block:: ring

	t1=clock()
	for t=1 to 100_000_000 next
	? (clock()-t1)/clockspersecond()

Output:

.. code-block:: none

	1.06


Example:

.. code-block:: ring

	? "Create list contains 100,000 items"
	nMax  = 100_000
	aList = list(nMax)
	for t=1 to nMax aList[t] = t next
	
	? "Do 3000 search operations - Find the last item (Worst Case!)"
	c = clock()
	
	for t=1 to 3000
        	find(alist,nMax)
	next
	
	? "Time: " + ( clock() - c ) / clockspersecond() + " seconds"

Output:

.. code-block:: none

	Create list contains 100,000 items
	Do 3000 search operations - Find the last item (Worst Case!)
	Time: 0.81 seconds

Example:

.. code-block:: ring

	load "guilib.ring"

	C_NODESCOUNT = 60000

	func main
	new QApp {
		win = new QWidget() { 
			move(100,100) resize(500,500)
			setWindowTitle("Many Tree Items - Testing Performance")
			tree = new QTreeWidget(win) { 
				blocksignals(True) setUpdatesEnabled(False)
				root = new qTreeWidgetItem()
				root.setText(0,"The Root Node")
				t1 = clock()
				for t = 1 to C_NODESCOUNT
					oItem = new qTreeWidgetItem()
					oItem.settext(0,"Item " + t)
					root.addchild(oItem)
				next
				cTime = (clock()-t1)/clockspersecond()
				setHeaderLabel("Creating "+C_NODESCOUNT+" nodes in " + cTime + " seconds.")
				addTopLevelItem(root)
				expanditem(root)
				blocksignals(False) setUpdatesEnabled(True)
			}
			oLayout = new QVBoxLayout() {
				addWidget(tree)
			}
			setLayout(oLayout)
			show() 
		}
		exec()
	}
		

Output:

.. image:: manytreeitems.png
	:alt: Many Tree Items


.. index:: 
     pair: Performance Tips; Creating Lists

Creating Lists
==============

Example:

.. code-block:: ring

	decimals(3)
	C_COUNT = 100_000

	? "Create the list using the Range operator"
	t1 = clock()
	aList = 1:C_COUNT
	? "Time : " + ((clock()-t1)/clockspersecond()) + " seconds"
	
	? "Create the list using the For loop"
	t1 = clock()
	aList = []
	for x = 1 to C_COUNT
		aList + x
	next
	? "Time : " + ((clock()-t1)/clockspersecond()) + " seconds"
	
	? "Create the list using the list() function and the For loop"
	t1 = clock()
	aList = list(C_COUNT)
	for x = 1 to C_COUNT
		aList[x] = x
	next
	? "Time : " + ((clock()-t1)/clockspersecond()) + " seconds"

Output:

.. code-block:: none

	Create the list using the Range operator
	Time : 0.001 seconds
	Create the list using the For loop
	Time : 0.009 seconds
	Create the list using the list() function and the For loop
	Time : 0.012 seconds


.. note:: Creating lists using the Range operator is faster than using the For loop or the list() function


.. tip:: For large lists we always recommend using the List() function

.. index:: 
     pair: Performance Tips; Arithmetic Operations

Arithmetic Operations
=====================

Example:

.. code-block:: ring

	C_COUNT = 1_000_000

	? "Using * operator"
	t1 = clock()
	out = 10
	for x = 1 to C_COUNT
		out = out * 2
	next
	? "Time : " + ((clock()-t1)/clockspersecond()) + " seconds"

	? "Using *= operator"
	t1 = clock()
	for x = 1 to C_COUNT
		out *= 2
	next
	? "Time : " + ((clock()-t1)/clockspersecond()) + " seconds"

Output:

.. code-block:: none

	Using * operator
	Time : 0.08 seconds
	Using *= operator
	Time : 0.07 seconds


.. note:: Using the *= operator is faster that using the * operator


.. index:: 
     pair: Performance Tips; Using len() and For Loops

Using len() and For Loops
=========================

Example:

.. code-block:: ring

	aList = 1:1000000

	? "Using len() in the For loop"
	t1 = clock()
	for x = 1 to len(aList)
	next
	? "Time : " + ((clock()-t1)/clockspersecond()) + " seconds"

	? "Using len() before the For loop"
	t1 = clock()
	nMax = len(aList)
	for x = 1 to nMax
	next
	? "Time : " + ((clock()-t1)/clockspersecond()) + " seconds"

Output:

.. code-block:: none

	Using len() in the For loop
	Time : 0.06 seconds
	Using len() before the For loop
	Time : 0.03 seconds

.. note:: Using len() before the For loop is faster than using len() in the For loop.


.. index:: 
     pair: Performance Tips; Calling Functions and Methods

Calling Functions and Methods
=============================

Example:

.. code-block:: ring

	? "calling 100000 functions"
	t1 = clock()
	for x = 1 to 100000
		test()
	next
	? "Time : " + ((clock()-t1)/clockspersecond()) + " seconds"

	o1 = new test

	? "calling 100000 methods using the dot operator"
	t1 = clock()
	for x = 1 to 100000
		o1.test()
	next
	? "Time : " + ((clock()-t1)/clockspersecond()) + " seconds"

	? "calling 100000 methods using braces "
	t1 = clock()
	for x = 1 to 100000
		o1 { test() }
	next
	? "Time : " + ((clock()-t1)/clockspersecond()) + " seconds"

	? "calling 100000 methods using braces (outside the loop) "
	t1 = clock()
	o1 {
	for x = 1 to 100000
		test() 
	next
	}
	? "Time : " + ((clock()-t1)/clockspersecond()) + " seconds"

	func test

	class test
		func test


Output:

.. code-block:: none

	calling 100000 functions
	Time : 0.01 seconds
	calling 100000 methods using the dot operator
	Time : 0.04 seconds
	calling 100000 methods using braces 
	Time : 0.09 seconds
	calling 100000 methods using braces (outside the loop)
	Time : 0.03 seconds

.. note:: Calling functions is faster than calling methods.

.. note:: Using the dot operator to call a method is faster than using braces.

.. tip:: Using braces before the for loop will be faster



