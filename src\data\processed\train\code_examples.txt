* hex2str()
.. code-block:: ring

	Number(string) ---> Number
	0 + string ---> Number
Example:
	see number("5") + 5 + nl 	# print 10
	see 0 + "10" + 2		# print 12	
.. index:: 
	pair: Data Type; String()
String() Function
=================
We can convert numbers to strings using the String() function or the + operator.
Syntax:
	String(number) ---> String
	"" + number ---> String
Example:
	see string(5) + 5 + nl 		# print 55
	see "" + 10 + 2			# print 102	
.. index:: 
	pair: Data Type; Ascii()
Ascii() Function
================
We can get the ASCII code for a letter using the Ascii() function
Syntax:
	Ascii(character) ---> ASCII Code
Example:
	See ascii("m") + nl + 	# print 109
	    ascii("M") 		# print 77
.. index:: 
	pair: Data Type; Char()
Char() Function
===============
We can convert the ASCII code to character using the Char() function.
Syntax:
	Char(ASCII Code) ---> character
Example:
	See char(109) + nl + 	# print m
	    char(77) 		# print M
.. index:: 
	pair: Data Type; Hex()
Hex() Function
==============
We can convert decimal to hexadecimal using the Hex() function.
Syntax:
	Hex(decimal) ---> hexadecimal
Example:
	See hex(10) + nl + 	# print a
	    hex(200)		# print c8
.. index:: 
	pair: Data Type; Dec()
Dec() Function
==============
We can convert hexadecimal to decimal using the Dec() function
Syntax:
	Dec(hexadecimal) ---> decimal
Example:
	See dec("a") + nl + 	# print 10
	    dec("c8")		# print 200
.. index:: 
	pair: Data Type; Str2Hex()
Str2hex() Function
==================
We can convert string characters to hexadecimal characters using the Str2hex() function.
Syntax:
	Str2hex(string) ---> hexadecimal string
Example:
	See str2hex("hello")  	# print 68656c6c6f
.. index:: 
	pair: Data Type; Hex2str()
Hex2str() Function
==================
We can convert hexadecimal characters to string using the Hex2str() function
Syntax:
	Hex2Str(Hexadecimal string) ---> string
Example:
	See hex2str("68656c6c6f")  	# print hello


* Automatic loading for (ringsyntax.ring) file that exist in the current folder
.. code-block:: ring

	aList = [ 10,20,30, ref(aList) ]	# Circular Reference
	? aList[4][1] 				# Print 10
	? aList[4][4][4][4][4][2]		# Print 20
	? refcount(aList)			# Print 2
We added this feature to use Ring in teaching Data Structures & Design Patterns.
Check the chapter (Using References) to learn more about using this feature!
.. index:: 
	pair: What is new in Ring  1.18; Ring for MS-DOS
Ring for MS-DOS
===============
Starting from Ring 1.18, We can build Ring Compiler/VM on MS-DOS
Tested using 


* Recursion
.. code-block:: ring

	def <function_name> [parameters]
		Block of statements
	[end]
.. note:: the keyword 'end' is optional.
Example:
	def hello
		put "Hello from function" + nl
	end
.. index:: 
	pair: Functions - Second Style; Call Functions
Call Functions
==============
To call function without parameters, we type the function name then ()
.. tip:: We can call the function before the function definition and the function code.
Example:
	hello()
	def hello
		put "Hello from function" + nl
	end
Example:
	first()  second()
	def first   put "message from the first function" + nl
	def second  put "message from the second function" + nl
.. index:: 
	pair: Functions - Second Style; Declare parameters
Declare parameters
==================
To declare the function parameters, after the function name type the list of parameters as a group
of identifiers separated by comma.
Example:
	def sum x,y
		put x+y+nl
	end
.. index:: 
	pair: Functions - Second Style; Send Parameters
Send Parameters
===============
To send parameters to function, type the parameters inside () after the function name
Syntax:
	funcname(parameters)
Example:
	/* output
	** 8
	** 3000
	*/
	sum(3,5) sum(1000,2000)
	def sum x,y put x+y+nl
.. index:: 
	pair: Functions - Second Style; Main Function
Main Function
=============
Using the Ring programming language, the Main Function is optional, 
when it's defined, it will be executed after the end of other statements.
if no other statements comes alone, the main function will be the first `entry point <http://en.wikipedia.org/wiki/Entry_point>`_ 
Example:
	# this program will print the hello world message first then execute the main function
	put "Hello World!" + nl
	def main
		put "Message from the main function" + nl
	end
.. index:: 
	pair: Functions - Second Style; Variables Scope
Variables Scope
===============
The Ring programming language uses `lexical scoping <http://en.wikipedia.org/wiki/Scope_%28computer_science%29#Lexical_scope_vs._dynamic_scope>`_ to
determine the scope of a variable.
 
Variables defined inside functions (including function parameters) are local variables.
Variables defined outside functions (before any function) are global variables.
Inside any function we can access the variables defined inside this function beside the global variables.
Example:
	# the program will print numbers from 10 to 1
	x = 10 				# x is a global variable.
	def main
		for t = 1 to 10		# t is a local variable
			mycounter()	# call function
		end
	end
	def mycounter
		put x + nl		# print the global variable value
		x--			# decrement
	end
.. note:: Using the main function before the for loop declare the t variable as a local variable,
	  It's recommended to use the main functions instead of typing the instructions directly to set the scope
	  of the new variables to local.
.. index:: 
	pair: Functions - Second Style; Return Value
Return Value
============
The function can return a value using the Return command.
Syntax:
	Return [Expression]
.. tip:: the Expression after the return command is optional and we can use the return command
	 to end the function execution without returning any value.
	 
.. note:: if the function doesn't return explicit value, it will return NULL (empty string = "" ).
Example:
	if novalue() = NULL	
		put "the function doesn't return a value" + nl
	end
	def novalue
.. index:: 
	pair: Functions - Second Style; Recursion
Recursion
=========
The Ring programming language support `Recursion <http://en.wikipedia.org/wiki/Recursion_%28computer_science%29>`_
and the function can call itself using different parameters.
Example:
	put fact(5)  	# output = 120
	def fact x if x = 0 return 1 else return x * fact(x-1) end


* Switch Statement
.. code-block:: ring

	switch Expression {
	case Expression
		Block of statements
	else
		Block of statements
	}	
Example:
	print(" 
		Main Menu
		---------
		(1) Say Hello
		(2) About
		(3) Exit
 	      ")
	nOption = GetString()
	switch nOption {
	case 1 
		print("Enter your name : ")
		name = getstring()
		print("Hello #{name}\n")
	case 2 
		print("Sample : using switch statement\n")
	case 3 
		Bye
	else 
		print("bad option...\n")
	}
.. index:: 
	pair: Control Structures; Looping
Looping
=======
.. index:: 
	pair: Control Structures - Third Style; While Loop


* These improvements increased the performance of Ring Code Generator for Extensions (4x Faster)
.. code-block:: ring

	x = 0x10
	? x		# 16
	x = 0xff
	? x		# 255
	x = 0x0A
	? x		# 10
	? 0xFFFF	# 65535
	? 0x0A + 1	# 10+1 = 11
(2) Printing large double value
Example:
	c = 999999999999999
	for i = 1 to 13
		c *= 999999999999999
	next
	? "c = " + c
Output:
.. code-block:: none
	c = 9.999999999999862032924046117813879019544782068185773946275755888189234614925384380788550958e+209
(3) Using String() and Number() with large double values
Example:
	c1 = 999999999999999
	for i = 1 to 13 
		c1 *= 999999999999999
	next
	
	s = string(c1)	? "c1 = " + s
	c2 = number(s)	? "c2 = " + c2
	
	? "c2 - c1 = " + (c2 - c1)
	
	str1	= "-2222044646462"
	c	= Number(str1)
	str2	= String(c)
	
	if str1 = str2 
		? "Strings Identical"
	else
		? "Strings Mismatch!"
	ok
Output:
.. code-block:: none
	c1 = 9.999999999999862032924046117813879019544782068185773946275755888189234614925384380788550958e+209
	c2 = 9.999999999999862032924046117813879019544782068185773946275755888189234614925384380788550958e+209
	c2 - c1 = 0
	Strings Identical
.. index:: 
	pair: What is new in Ring 1.14?; Using CLOC (Count Lines of Code)
Using CLOC (Count Lines of Code)
================================
Usage:
.. code-block:: none
	ringcloc <application_folder_path>
Example(1):
.. code-block:: none
	ringcloc c:\ring\tools\ringnotepad
Output:
.. code-block:: none
	      47 text files.
	      47 unique files.
	      11 files ignored.
	
	github.com/AlDanial/cloc v 1.88  T=1.18 s (39.0 files/s, 3212.2 lines/s)
	-----------------------------------------------------------------------------------
	Language                         files          blank        comment           code
	-----------------------------------------------------------------------------------
	Ring                                33            273            139           3186
	Bourne Shell                         4             22              9             88
	DOS Batch                            4              3              7             20
	INI                                  1              0              0             15
	C                                    1              0              1             10
	Markdown                             2              4              0              8
	Windows Resource File                1              0              0              1
	-----------------------------------------------------------------------------------
	SUM:                                46            302            156           3328
	-----------------------------------------------------------------------------------
Example(2):
.. code-block:: none
	ringcloc c:\ring\tools\formdesigner
Output:
.. code-block:: none
	      54 text files.
	      54 unique files.
	      13 files ignored.
	
	github.com/AlDanial/cloc v 1.88  T=1.54 s (34.5 files/s, 7122.4 lines/s)
	-------------------------------------------------------------------------------
	Language                     files          blank        comment           code
	-------------------------------------------------------------------------------
	Ring                            52           1306            567           9071
	Markdown                         1              5              0              7
	-------------------------------------------------------------------------------
	SUM:                            53           1311            567           9078
	-------------------------------------------------------------------------------
.. index:: 
	pair: What is new in Ring 1.14?; More Improvements
More Improvements
=================


* Added: samples/General/Performance/search.ring
.. code-block:: ring

	t1= clock()
	for t=1 to 100_000_000 next
	t2 = clock()
	? (t2-t1)/clockspersecond()
Time using Ring 1.18 (32bit) : 3.78 seconds
Time using Ring 1.19 (32bit) : 1.31 seconds
Time using Ring 1.19 (64bit) : 1.12 seconds
.. index:: 
	pair: What is new in Ring  1.19; For Loop - Better Performance
Calling Functions - Better Performance
======================================
Calling functions written in Ring code in Ring 1.19 is three times (3x) faster than Ring 1.18.
While calling functions written in C code in Ring 1.19 is four times (4x) faster than Ring 1.18.
Example:
	t1=clock()
	for t=1 to 1_000_000
		result = max(t,t*2)
	next
	? result
	t2 = clock()
	? (t2-t1)/clockspersecond() 
Time using Ring 1.18 (32bit) :  1.45 seconds
Time using Ring 1.19 (32bit) :  0.32 seconds
Time using Ring 1.19 (64bit) :  0.25 seconds
These improvements let the Stars animation sample works at 2350 FPS in Ring 1.19 instead of 500FPS in Ring 1.18
.. image:: stars3d.png
	:alt: stars3d
.. index:: 
	pair: What is new in Ring  1.19; Using Objects During Definition
Using Objects During Definition
===============================
This release provides better support for using objects during definition
where we can mix between this feature and other features like operator overloading without missing the output
Example:
.. image:: usingobjdurdef.png
	:alt: usingobjdurdef


* In RingQt for Android, The Ring Object File (ringo) will be executed directly from resources.
.. code-block:: ring

	? "Start the test!" 
	pState = ring_state_init()
	ring_state_runcode(pState," ? 'Let us try having an error' ? x")
	ring_state_delete(pState)
	? ""
	? "End of test!"
Output:
.. code-block:: none
	Start the test!
	Let us try having an error
	Line 1 Error (R24) : Using uninitialized variable : x
	in file Ring_EmbeddedCode
	End of test!
	
(5) The compiler will ignore new lines after keywords that expect tokens after it
Example:
	see 
	"
		Hello, World!
	"
	test()
	func 
	#======================#
		Test
	#======================#
		?
		"
	
		Hello from the Test function
		"
Output:
.. code-block:: none
	        Hello, World!
	        Hello from the Test function
(6) Better code (faster) for the main loop, special loop for eval() function.
(7) Better code (faster) for tracking C pointers to avoid using NULL pointers.
(8) Better code (faster) for getting the self object using braces.
.. index:: 
	pair: What is new in Ring 1.8?; Notes to extensions creators
Notes to extensions creators
============================
If you have created new extensions for Ring in the C/C++ languages.
You have to rebuild your extension (Generate the DLL file again using Ring 1.8 header files) before usage with Ring 1.8
Because we changed the internal structure of the VM, but no changes to the code are required. just rebuild.


* More Improvements
.. code-block:: ring

	func setLang nLanguage
		if C_ENV_DEFAULT_LANG = nLanguage
			return
		ok
		C_ENV_DEFAULT_LANG = nLanguage
		# Change the language
			switch nLanguage 
				on C_TRANSLATION_ENGLISH
					load again "translation/english.ring"
				on C_TRANSLATION_ARABIC 
					load again "translation/arabic.ring"
			off
.. index:: 
	pair: What is new in Ring 1.12?; ring_state_filetokens() function
ring_state_filetokens() function
================================
Using the ring_state_filetokens() function we can get all the tokens in the ring source code file.
	C_FILENAME 	= "test_tokens.ring"
	C_WIDTH		= 12
	# write the file
		write(C_FILENAME,'
				see "Hello, World!"
				? 3*2+3
				Name = "Ring"
				? Name
	')
	# Token Type
		C_KEYWORD 	= 0
		C_OPERATOR 	= 1
		C_LITERAL 	= 2
		C_NUMBER 	= 3
		C_IDENTIFIER 	= 4
		C_ENDLINE 	= 5
	# Keywords List
	aKEYWORDS = ["IF","TO","OR","AND","NOT","FOR","NEW","FUNC", 
	"FROM","NEXT","LOAD","ELSE","SEE","WHILE","OK","CLASS","RETURN","BUT", 
	"END","GIVE","BYE","EXIT","TRY","CATCH","DONE","SWITCH","ON","OTHER","OFF", 
	"IN","LOOP","PACKAGE","IMPORT","PRIVATE","STEP","DO","AGAIN","CALL","ELSEIF", 
	"PUT","GET","CASE","DEF","ENDFUNC","ENDCLASS","ENDPACKAGE", 
	"CHANGERINGKEYWORD","CHANGERINGOPERATOR","LOADSYNTAX"]
	pState = ring_state_new()
	aList = ring_state_filetokens(pState,C_FILENAME)
	PrintTokens(aList)
	ring_state_delete(pState)
	func PrintTokens aList
		for aToken in aList
			switch aToken[1]
			on C_KEYWORD 
				? Width("Keyword",C_WIDTH) + ": "  + aKeywords[0+aToken[2]]
			on C_OPERATOR 
				? Width("Operator",C_WIDTH)  + ": " + aToken[2]
			on C_LITERAL 
				? Width("Literal",C_WIDTH)  + ": " + aToken[2]
			on C_NUMBER 
				? Width("Number",C_WIDTH)  + ": " + aToken[2]
			on C_IDENTIFIER 
				? Width("Identifier",C_WIDTH)  + ": " + aToken[2]
			on C_ENDLINE 
				? "EndLine"	
			off
		next
	func Width cText,nWidth
		return cText+copy(" ",nWidth-len(cText))
Output:
.. code-block:: none
	EndLine
	Keyword     : SEE
	Literal     : Hello, World!
	EndLine
	Operator    : ?
	Number      : 3
	Operator    : *
	Number      : 2
	Operator    : +
	Number      : 3
	EndLine
	Identifier  : name
	Operator    : =
	Literal     : Ring
	EndLine
	Operator    : ?
	Identifier  : name
	EndLine
.. index:: 
	pair: What is new in Ring 1.12?; Generate Embedded Object File
Generate Embedded Ring Object File
==================================
We can generate embedded object file (C source code) from the source code file (*.ring) 
using -geo option
Command:
	ring test.ring -geo
This command will generate at least three files
	test.c
	ringappcode.c
	ringappcode.h
More files could be generated based on the project size
The generated files will pass the byte code to Ring VM to be executed
.. index:: 
	pair: What is new in Ring 1.12?; Better RingRayLib
Better RingRayLib
=================
More Samples are added to RingRayLib
	* Sound Loading Playing 
	* Texture Source 
	* Music Playing Streaming 
	* Rectangle scaling 
	* Colors Palette
	* Following Eyes
	* Collision Area
	* Bezier Lines
	* Images Generation
	* Fifteen Puzzle Game
	* Cubic Map
Screen Shot:
	
.. image:: raylib_cubicmap.png
	:alt: RayLib Example	
.. index:: 
	pair: What is new in Ring 1.12?; More Improvements
More Improvements
=================


* Run the deploy.bat file which will copy the ringpico.uf2 file to the device
.. code-block:: ring

	DELAY   = 100
	LED_PIN	= PICO_DEFAULT_LED_PIN 
	
	gpio_init(LED_PIN)
	gpio_set_dir(LED_PIN, GPIO_OUT)  
	
	while True 
		gpio_put(LED_PIN, True)
		sleep_ms(DELAY)
		gpio_put(LED_PIN, False)
		sleep_ms(DELAY)
	end
.. index:: 
	pair: Using Pico; LEDs sample
LEDs sample
===========
	DELAY   = 100
	LED_PIN	= PICO_DEFAULT_LED_PIN 
	LED1 	= 14  	
	LED2	= 15 
	
	aPins = [LED_PIN, LED1, LED2] 
	
	for nPin in aPins 
		gpio_init(nPin)
		gpio_set_dir(nPin, GPIO_OUT)  
	next 
	
	while True 
		gpio_put(LED_PIN, True)					sleep_ms(DELAY)
		gpio_put(LED1	, True)		gpio_put(LED2, False)	sleep_ms(DELAY)
		gpio_put(LED1	, False)	gpio_put(LED2, True)	sleep_ms(DELAY)
		gpio_put(LED_PIN, False)				sleep_ms(DELAY)
	end
.. index:: 
	pair: Using Pico; LED and Switch sample
LED and Switch sample
=====================
File: main.ring
	load "mylib.ring"
	SWITCH_PIN = 14
	LED_PIN    = 15
	func main 
		oSwitch	= new LEDSwitch {
			setPin(SWITCH_PIN)
			LED { setPin(LED_PIN) }
		}
	
		while True 
			 oSwitch.process()	
		end
File: mylib.ring
	class LED 
	
		Pin 
	
		func setPin nPin 
			Pin = nPin 
			gpio_init(Pin)
 			gpio_set_dir(Pin,GPIO_OUT) 
		func Enable 
			gpio_put(Pin,True)
	
		func Disable 
			gpio_put(Pin,False)
	class LEDSwitch 
	
		Pin 
		Status = False  
		LED = new LED 
		func setPin nPin 
			Pin = nPin 
			gpio_init(Pin)
 			gpio_pull_up(Pin) 
	
		func getStatus  
			return gpio_get(Pin)  
	
		func Process
			if ! getStatus()
				Status = ! Status 
				if Status 
					LED.enable()
				else
					LED.disable() 
				ok
				sleep_ms(30)
				while ! getStatus() end  
				sleep_ms(30) 
			ok
.. index:: 
	pair: Using Pico; Declarative Programming sample
Declarative Programming sample
==============================
File: main.ring
	load "circuit.ring"
	
	SWITCH_PIN = 14
	LED_PIN    = 15
	
	func main 
		Circuit {
			LED {
				Pin	= PICO_DEFAULT_LED_PIN
				Blink	= True
				Delay	= 0.1
			}
			LEDSwitch {
				Pin = SWITCH_PIN
				LED { 
					Pin	= LED_PIN
					Blink	= True
					Delay	= 3  
				}
			}
		}
File: Circuit.ring
	circuit = new Circuit
	class Circuit
		LED
		LEDSwitch
		aObjects = []
		lCallBraceEnd = True 
		func getLED
			aObjects + new LED 
			return aObjects[len(aObjects)] 	
		func getLEDSwitch 
			aObjects + new LEDSwitch 
			return aObjects[len(aObjects)] 
		func braceEnd 
			if ! lCallBraceEnd return ok
			lCallBraceEnd = False 
			while True 
				for oObj in aObjects 
					oObj.process()
				next 
			end 
	class CircuitComponent
		func process
	class LED from CircuitComponent
		Pin 
		Delay = 1
		Blink = False
		t1    = clock()
		lStatus		= False 
		lCallBraceEnd	= True 
		lEnableProcess	= True 
		func setPin nPin 
			Pin = nPin 
			gpio_init(Pin)
			gpio_set_dir(Pin,GPIO_OUT) 
		func Enable 
			lStatus = True
			gpio_put(Pin,True)
		func Disable 
			lStatus = False
			gpio_put(Pin,False)
		func braceEnd 
			if ! lCallBraceEnd return ok
			enable()
		func process
			if ! lEnableProcess return ok
			if Blink and ( ((clock()-t1)/clockspersecond()) > Delay )
				lStatus = ! lStatus 
				gpio_put(Pin,lStatus)
				t1 = clock()
			ok
	class LEDSwitch from CircuitComponent
		Pin 
		Status	= False  
		LED	= new LED { lCallBraceEnd = False lEnableProcess = False }
		func setPin nPin 
			Pin = nPin 
			gpio_init(Pin)
			gpio_pull_up(Pin) 
		func getStatus  
			return gpio_get(Pin)  
		func Process
			LED.process()
			if ! getStatus()
				Status = ! Status 
				if Status 
					LED.enable() 
				else
					LED.disable() 
				ok
				LED.t1 = clock()
				LED.lEnableProcess = Status
				sleep_ms(30)
				while ! getStatus() end  
				sleep_ms(30) 
			ok
Screen Shot:
.. image:: pico.jpg
	:alt: Raspberry Pi Pico
.. index:: 
	pair: Using Pico; Using Wokwi Simulator
Using Wokwi Simulator
=====================
Using this simulator, We can test RingPico programs without the need to use real hardware.
Steps:


* After compressing the files (To ZIP file) - Total : 504 KB
.. code-block:: ring

	load "libui.ring"
	oWindow = uiNewWindow( "Say Hello", 500, 80, True)
	uiWindowOnClosing(oWindow,"closeApp()")
	lbl1 = uiNewLabel("Name: ")
	text1 = uiNewEntry()
	btn1 = uiNewButton("SayHello")
	uiButtonOnClicked(btn1,"sayHello()")
	btn2 = uiNewButton("Close")
	uiButtonOnClicked(btn2,"closeApp()")
	lbl2 = uiNewLabel("")
	g = uiNewGrid() uiGridSetPadded(g, 1) uiWindowSetChild(oWindow, g)
	uiGridAppend(g, lbl1, 0, 0, 2, 1, 1, uiAlignCenter, 0, uiAlignCenter)
	uiGridAppend(g, text1, 1, 0, 2, 1, 1, uiAlignFill, 0, uiAlignFill)
	uiGridAppend(g, btn1, 0, 1, 1, 2, 1, uiAlignFill, 0, uiAlignFill)
	uiGridAppend(g, btn2, 2, 1, 1, 1, 1, uiAlignFill, 0, uiAlignFill)
	uiGridAppend(g, lbl2, 0, 3, 2, 1, 1, uiAlignCenter, 0, uiAlignCenter)
	uiControlShow( oWindow )
	uiMain()
	func sayHello 
		uiLabelSetText(lbl2,"Hello " + uiEntryText(text1))
	func closeApp
		uiQuit()
Screen Shots:
.. image:: libui_6.png
	:alt: RingLibui Screen Shot
.. image:: libui_14.png
	:alt: RingLibui Screen Shot
.. image:: libui_16.png
	:alt: RingLibui Screen Shot
.. image:: libui_26.png
	:alt: RingLibui Screen Shot
.. index:: 
	pair: What is new in Ring 1.14?; RingSockets Extension
RingSockets Extension
=====================
In Ring, We have sockets using different extensions like RingQt, RingLibuv and RingLibSDL
In this release we provide a special extension for sockets
This will be useful if your application doesn't use the previous libraries
Example (Server Code)
	# TCP SERVER
	load "sockets.ring" 
	sock = socket(AF_INET,SOCK_STREAM,0) 
	bind(sock,"127.0.0.1",5050)
	listen(sock,5)
	ns = accept(sock)
	send(ns,"Hello Client")
	msg = recv(ns,1024)
	? "Client Say >> " + msg
	close(sock)
	? "Socket connection closed"
Example (Client Code)
	# TCP Client
	load "sockets.ring" 
	sock = socket(AF_INET,SOCK_STREAM)
	connect(sock,"127.0.0.1",5050)
	send(sock,"Hello Server")
	msg = recv(sock,1024)
	? "Server Say >> " + msg
	close(sock)
	? "Socket connection closed"
.. index:: 
	pair: What is new in Ring 1.14?; RingThreads Extension
RingThreads Extension
=====================
In Ring, We have threads using different extensions like RingQt, RingLibuv and RingAllegro
In this release we provide a special extension for threads
This will be useful if your application doesn't use the previous libraries
Example:
	load "threads.ring"
	func main
		nThreads = 2
		aList = list(nThreads)
		for x=1 to nThreads
			aList[x] = new_thrd_t()
			thrd_create(aList[x],"Hello("+x+")")
		next
		for x=1 to nThreads
			res= 0
			thrd_join(aList[x],:res)
		next
		? :Done
		shutdown()
	func Hello x
		for r=1 to 100
			? "Message from the Hello("+x+") function"
		next
.. index:: 
	pair: What is new in Ring 1.14?; Better RingOpenSSL
Better RingOpenSSL
==================
The next functions are added to the RingOpenSSL extension
These functions compute the hash of large files/data without the need to load all of the content in a single string.
.. code-block:: none
	md5init() -> MD5_CTX
	md5update (MD5_CTX, string) -> 1 for success or 0 for failure
	md5final (MD5_CTX) -> string
	
	sha1init() -> SHA_CTX
	sha1update (SHA_CTX, string) -> 1 for success or 0 for failure
	sha1final (SHA_CTX) -> string
	
	sha224init() -> SHA224_CTX
	sha224update (SHA224_CTX, string) -> 1 for success or 0 for failure
	sha224final (SHA224_CTX) -> string
	
	sha256init() -> SHA256_CTX
	sha256update (SHA256_CTX, string) -> 1 for success or 0 for failure
	sha256final (SHA256_CTX) -> string
	
	sha384init() -> SHA384_CTX
	sha384update (SHA384_CTX, string) -> 1 for success or 0 for failure
	sha384final (SHA384_CTX) -> string
	
	sha512init() -> SHA512_CTX
	sha512update (SHA512_CTX, string) -> 1 for success or 0 for failure
	sha512final (SHA512_CTX) -> string
.. index:: 
	pair: What is new in Ring 1.14?; More Functions
More Functions
==============


* Binary Data 
.. code-block:: ring

	cStr1 = "a"			# One character
	cStr2 = "Hello, World!" 	# A string of many characters
	cStr3 = "Hello
	Welcome to the Ring language!
	"				# Multi-line string
	cStr4 = read(EXEFileName())	# Read executable file (Binary Data)
The Number type is used to represent


* Private Flag (if the variable is an attribute in a Class)
.. code-block:: ring

	RingVM_MemoryList() ---> List
Example:
	x = 10
	test()
	func test
		y = 20
		see ringvm_memorylist()
Output:
	true
	2
	1
	0
	0
	false
	2
	0
	0
	0
	nl
	1
	0
	0
	null
	1
	0
	0
	ring_gettemp_var
	4
	00000000
	0
	0
	ccatcherror
	1
	NULL
	0
	0
	ring_settemp_var
	4
	00000000
	0
	0
	ring_tempflag_var
	2
	0
	0
	0
	stdin
	3
	50512DB8
	file
	0
	0
	0
	stdout
	3
	50512DD8
	file
	0
	0
	0
	stderr
	3
	50512DF8
	file
	0
	0
	0
	this
	4
	00000000
	0
	0
	sysargv
	3
	B:\ring\bin/ring
	B:/ring/tests/scripts/memorylist.ring
	0
	0
	x
	2
	10
	0
	0
	y
	2
	20
	0
	0
.. index:: 
	pair: Low Level Functions; RingVM_CallList()
ringvm_calllist() function
==========================
The Function return a list of the functions call list.
Each List Member is a list contains the next items


* Open_WindowAndLink()
.. code-block:: ring

	class firstwindowController from windowsControllerParent
	    oView = new firstwindowView
	    func OpenSecondWindow
        	Open_WindowAndLink(:SecondWindowController,self)
	    func SendMessage
        	if IsSecondWindow() 
	            SecondWindow().setMessage("Message from the first window")
        	ok
	    func setMessage cMessage
        	oView.Label1.setText(cMessage)
(3) The next classes are added to RingQt


* DisableHashComments
.. code-block:: ring

	DisableHashComments
	#define = 10
	EnableHashComments
	# Just a comment
	DisableHashComments
	? #define
	EnableHashComments
	# End of program
.. index:: 
	pair: What is new in Ring  1.20; Better Files for Loading the StdLib
Better Files for Loading the StdLib
===================================
Using stdlib.ring will load stdlib functions, classes and some extensions too like RingLibCurl, RingOpenSSL, etc.
Using stdlibcore.ring we can only load stdlib functions.
In this release we provide stdlibclasses.ring that can only load stdlib classes without loading stdlib functions or extensions. 
Example:
	load "stdlibclasses.ring"
	oStack = new Stack {
		push("A")
		push("B")
		push("C")
		push("D")
		push("E")
		pop()
		pop()
		print()
	}
Output:
.. code-block:: none
	C
	B
	A
.. note:: Also, we improved the files that load the library to load it in separate global scope.
.. tip:: To load a library in separate global scope use the Load Package command.
.. index:: 
	pair: What is new in Ring  1.20; Better Performance when using Braces
Better Performance when using Braces
====================================
Using braces to access objects is faster in Ring 1.20 than Ring 1.19
The speed up factor could be from (2.2 to 2.6 times)
Tested using Victus Laptop [13th Gen Intel(R) Core(TM) i7-13700H, Windows 11]
Using normal functions or using the dot operator still faster than using braces, but this update is a step forward towards reducing the gap.
Example:
	oPoint = new Point
	t1 = clock()
	for t=1 to 100000
		oPoint {
			# Access object attributes|methods
		}
	next	
	? (clock()-t1)/clocksPerSecond()
	class point x y z


* While Loop
.. code-block:: ring

	while Expression {
		Block of statements
	}
Example:
	While True {
		print(" 
			Main Menu
			---------
			(1) Say Hello
			(2) About
			(3) Exit
			  ")
		nOption = GetString()
		switch nOption {
		case 1 
			print("Enter your name : ")
			name = getstring()
			print("Hello #{name}\n")
		case 2 
			print("Sample : using switch statement\n")
		case 3 
			Bye
		else 
			print("bad option...\n")
		}
	}
.. index:: 
	pair: Control Structures - Third Style; For Loop


* For in Loop
.. code-block:: ring

	for|foreach identifier in List/String  [step expression]
		Block of statements
	next
Example:
	aList = 1:10	# create list contains numbers from 1 to 10
	for x in aList  see x + nl  next  # print numbers from 1 to 10
.. note:: We can use the ForEach keyword instead of the For keyword 
.. index:: 
	pair: Control Structures - First Style; Step Option
Using The Step option with For in
=================================
We can use the Step option with For in to skip number of items in each iteration
Example:
	aList = 1:10	# create list contains numbers from 1 to 10
	# print odd items inside the list
	for x in aList step 2
		see x + nl  
	next  
.. index:: 
	pair: Control Structures - First Style; for in to modify lists
Using For in to modify lists
=============================
When we use (For in) we get items by reference.
This means that we can read/edit items inside the loop.
	
Example:
	aList = 1:5	# create list contains numbers from 1 to 5
	# replace list numbers with strings
	for x in aList  
		switch x
		on 1  x = "one"
		on 2  x = "two"
		on 3  x = "three"
		on 4  x = "four"
		on 5  x = "five"
		off
	next
	see aList	# print the list items
.. index:: 
	pair: Control Structures - First Style; Do Again Loop
Do Again Loop
=============
Syntax:
	
	do
		Block of statements
	again expression
Example:
	x = 1 
	do 
		see x + nl 
		x++ 
	again x <= 10
.. index:: 
	pair: Control Structures - First Style; Exit
Exit Command
============
Used to go outside one or more of loops.
Syntax:
	exit [expression]	# inside loop
Example:
	for x = 1 to 10
		see x + nl
		if x = 5 exit ok
	next
.. index:: 
	pair: Control Structures - First Style; Exit from two loops
Exit from two loops
===================
The next example presents how to use the exit command to exit from two loops in one jump.
Example:
	for x = 1 to 10
		for y = 1 to 10
			see "x=" + x + " y=" + y + nl
			if x = 3 and y = 5
				exit 2	   # exit from 2 loops 
			ok
		next
	next			
.. index:: 
	pair: Control Structures - First Style; Loop Command
Loop Command
============
Used to jump to the next iteration in the loop.
Syntax:
	loop [expression]	# inside loop
Example:
	for x = 1 to 10
		if x = 3
			see "Number Three" + nl
			loop
		ok
		see x + nl
	next
.. index:: 
	pair: Control Structures - First Style; Short-circuit evaluation
Short-circuit evaluation
========================
The logical operators and/or follow the `short-circuit evaluation <http://en.wikipedia.org/wiki/Short-circuit_evaluation>`_.
If the first argument of the AND operator is zero, then there is no need to evaluate the second
argument and the result will be zero.
If the first argument of the OR operator is one, then there is no need to evaluate the second
argument and the result will be one.
Example:
	/* output
	** nice 
	** nice 
	** great	
	*/
	x = 0 y = 10
	if (x = 0 and nice()) and (y = 10 and nice())
		see "great" + nl
	ok
	func nice  see "nice" + nl   return 1
Example:
	# No output
	x = 0 y = 10
	if (x = 1 and nice()) and (y = 10 and nice())
		see "great" + nl
	ok
	func nice  see "nice" + nl   return 1
Example:
	/* output 
	** nice
	** great
	*/
 
	x = 0 y = 10
	if (x = 0 and nice()) or (y = 10 and nice())
		see "great" + nl
	ok
	func nice  see "nice" + nl  return 1			
.. index:: 
	pair: Control Structures - First Style; Comments about evaluation
Comments about evaluation
=========================


* For Loop
.. code-block:: ring

	for identifier=expression to expression [step expression]
		Block of statements
	end
Example:
	# print numbers from 1 to 10
	for x = 1 to 10	 put x + nl  end
Example:
	# Dynamic loop
	Put "Start : " get nStart  nStart= 0+nStart       
	Put "End   : " get nEnd    nEnd  = 0+nEnd
	Put "Step  : " get nStep   nStep = 0+nStep
	For x = nStart to nEnd Step nStep
		Put x + nl
	End
Example:
	# print even numbers from 0 to 10
	for x = 0 to 10 step 2
		Put x + nl
	end
Example:
	# print even numbers from 10 to 0
	for x = 10 to 0 step -2
		put x + nl
	end
.. index:: 
	pair: Control Structures - Second Style; For In Loop


* Time using Ring 1.18: 0.38 second
.. code-block:: ring

	oPoint = new Point
	t1 = clock()
	oPoint {
		for t=1 to 100000
			# Access object attributes|methods
		next	
	}
	? (clock()-t1)/clocksPerSecond()
	class point x y z
.. index:: 
	pair: What is new in Ring  1.20; Better Support for Threads
Better Support for Threads
==========================


* TimeInfo(cInformation)
.. code-block:: ring

	load "stdlibcore.ring"
	? "Using the IsListContainsItems() function" 
	aList1 = "a":"z"
	aList2 = [:h,:l,:p,:u]
	? IsListContainsItems(aList1,aList2)
	? "Using the IsBetween() function"
	? isBetween(1,3,4)
	? isBetween(4,1,6)
	? "Using the TimeInfo() function"
	? timeInfo(:date)
	? timeInfo(:year)
	? timeInfo(:time)
	? timeInfo(:hour_12)
Output:
	Using the IsListContainsItems() function
	1
	Using the IsBetween() function
	0
	1
	Using the TimeInfo() function
	05/24/19
	2019
	15:30:33
	03
For more information about these functions, see the StdLib functions chapter.
.. index:: 
	pair: What is new in Ring 1.11?; Better RingQt 
Better RingQt
=============


* Logical AND have higher precedence than Logical OR.
.. code-block:: ring

	aList = ["one",
		 "two",
		 "three",
		]
	nNum1 = +10
	nNum2 = -10
	lRes  = True OR False AND False
	lRes2 = True || False &&  False
	? aList
	? nNum1
	? nNum2
	? lRes
	? lRes2
Output:
.. code-block:: none
	one
	two
	three
	10
	-10
	1 		# True	
	1 		# True
.. index:: 
	pair: What is new in Ring  1.22; More Improvements
More Improvements
=================


* Using ? to print expression then new line
.. code-block:: ring

	ring2exe cards.ring -dist -mobileqt
Example (2)
	ring2exe formdesigner.ring -dist -mobileqt
.. image:: formdesignerandroid.png
	:alt: Form Designer - Android
.. index:: 
	pair: What is new in Ring 1.6?; New Tool: Folder2qrc
New Tool: Folder2qrc
====================
When we have large RingQt project that contains a lot of images and files, We need to add these files 
to the resource file ( *.qrc ) when distributing applications for Mobile devices. 
Instead of adding these files one by one, Ring 1.6 comes with a simple tool that save our time, It's
called Folder2qrc.
Example:
.. code-block:: none
	folder2qrc formdesigner.ring
We determine the main source file while we are in the application folder, and Folder2qrc will check all
of the files in the current folder and sub folders, Then add them to the resource file after the
mainfile.ringo (In our example this will be formdesigner.ringo)
The output file will be : project.qrc
You can open it and remove the files that you don't need in the resources!
.. index:: 
	pair: What is new in Ring 1.6?; Better Scripts for building Ring
Better Scripts for building Ring
================================
Ring 1.6 comes with better scripts for building Ring from source code.
The updates are tested on 32bit and 64bit systems on Windows, Linux (Ubuntu,Fedora) and macOS.
The scripts for Windows are updated to find the Visual C/C++ compiler based on your Visual Studio version.
.. index:: 
	pair: What is new in Ring 1.6?; RingConsoleColors Extension
RingConsoleColors Extension
===========================
Using the RingConsoleColors extension we can easily change the colors used in our console applications
.. image:: consolecolors.png
	:alt: RingConsoleColors
For more information check the RingConsoleColors chapter in the documentation.
.. index:: 
	pair: What is new in Ring 1.6?; RingMurmurHash Extension
RingMurmurHash Extension
========================
Ring 1.6 comes with the RingMurmurHash extension!
Developer: Hassan Ahmed
Example:
	load "murmurhashlib.ring"
	key = "Ring Language"
	see murmurhash3_x86_32(key, 0, 0) + nl // Output: 1894444853
	see murmurhash3_x86_32(key, 0, 1) + nl // Output: 70eaef35
For more information check the RingMurmurHash chapter in the documentation.
.. index:: 
	pair: What is new in Ring 1.6?; Better Ring Notepad
Better Ring Notepad
===================
Ring Notepad comes with the next updates
(1) Automatic setting for the Main File when we Run the application (using the Main file buttons).
(2) Main File - Automatic save before running.
(3) When we run GUI application - don't change the focus to the text box used for the input in the Output Window.
(4) A button and option to run web applications
.. image:: runwebapp1.png
	:alt: Ring Notepad - Run Web Application - shot 1
For Windows users, Ring 1.6 comes with Apache Web server!
We can run any web application from any folder directly without doing any configuration.
.. image:: runwebapp2.png
	:alt: Ring Notepad - Run Web Application - shot 2
(5) Tools - Operating System - Terminal (Command Prompt) & Files Manager (Explorer).
.. image:: rnotetoolsterminal.png
	:alt: Ring Notepad - Tools - Terminal - shot 1
So we can quickly open the Command Prompt or the Explorer at the application folder.
.. image:: rnotetoolsterminal2.png
	:alt: Ring Notepad - Tools - Terminal - shot 2
(6) Support *.sh & *.bat extensions.
(7) New Menu: Distribute 
.. image:: rnotedistribute.png
	:alt: Ring Notepad - Distribute
.. index:: 
	pair: What is new in Ring 1.6?; Better RingQt
Better RingQt
=============
RingQt comes with the next updates
(1) QAllEvents - getkeytext() Method
(2) QSQLQuery - exec_2() Method
(3) QDockWidget Events
(4) AppFile() Function
(5) IsMobile() Function
(6) QRegion Class
(7) QCoreApplication class
.. index:: 
	pair: What is new in Ring 1.6?; Better StdLib
Better StdLib
=============
StdLib comes with the next updates
(1) Factors() function is updated (Return the output instead of printing it)
(2) Palindrome() function is updated (Return the output instead of printing it)
(3) Using stdlibcore.ring we can use the StdLib functions (Without Classes)
    Also this is useful when developing standalone console applications
    Because using stdlib.ring (functions & classes) will load libraries like RingLibCurl, RingOpenSSL, etc.
(4) New Functions


* Try-Catch
.. code-block:: ring

	see "if statement.." + nl
	x = 1
	if x = 1 {
		see "one" + nl
	elseif x=2 
		see "two" + nl
	elseif x=3 
		see "three" + nl
	}
	see "for loop.." + nl
	for t = 1 to 10 {
		see t 
	}
	see nl
	see "switch..." + nl
	x = 1
	switch x {
		on 1 see "one" + nl
		on 2 see "two" + nl
	}
	see "try catch..." + nl
	try {
		x = 1 / 0
	catch
		see "catching error" + nl
	}
Output:
	if statement..
	one
	for loop..
	12345678910
	switch...
	one
	try catch...
	catching error
.. index:: 
	pair: Syntax Flexibility; Using 'put' and 'get' as 'see' and 'give'
Using 'put' and 'get' as 'see' and 'give'
=========================================
We can replace the 'see' keyword with the 'put' keyword.
Also we can replace the 'give' keyword with the 'get' keyword.
Example:
	put "Hello World" + nl
	put "Enter Your Name ? " Get Name
	Put "Hello " + Name
.. index:: 
	pair: Syntax Flexibility; Using 'case' as 'on' in switch statements
Using 'case' as 'on' in switch statements
=========================================
We can replace the 'on' keyword with 'case' keyword in the switch statement.
Example (1) :
	for x=1 to 10 
		switch x 
		case 1 put "one" + nl
		case 2 put "two" + nl
		case 3 put "three" + nl
		else put "else" + nl
		end
	end
Example (2) :
	for x=1 to 10 {
		switch x {
		case 1 put "one" + nl
		case 2 put "two" + nl
		case 3 put "three" + nl
		else put "else" + nl
		}
	}
.. index:: 
	pair: Syntax Flexibility; Using 'def' as 'func' in functions/methods definition
Using 'def' as 'func' in functions/methods definition
=====================================================
We can use the 'def' keyword as the 'func' keyword to define functions and methods.
Example:
	one() two()
	def one put "one" + nl
	def two put "two" + nl
.. index:: 
	pair: Syntax Flexibility; Using braces { } in Packages/Classes/Functions
Using braces { } in Packages/Classes/Functions
==============================================
Example:
	load "stdlib.ring"
	import mypackage
	new myclass {
		myfunc() 
	}
	package mypackage 
	{
		class myclass 
		{
			func myfunc 
			{
				print("Hello, World!\n")
			}
		}
	}
.. index:: 
	pair: Syntax Flexibility; Using 'break'/'continue' keywords
Using 'break'/'continue' keywords
=================================
Instead of using Exit/Loop commands we can use Break/Continue
Example:
	for t=1 to 10 {
		if t=3 {
			continue
		elseif t=5
			break
		}
		? t
	}
Output:
.. code-block:: none
	1
	2
	4
.. index:: 
	pair: Syntax Flexibility; Using 'end' keyword after Packages/Classes/Functions
Using 'end' keyword after Packages/Classes/Functions
====================================================
Example:
	import mypackage
	new myclass {
		myfunc() 
	}
	package mypackage 
		class myclass 
			def myfunc 
				put "Hello, World!"
			end
		end
	end
.. index:: 
	pair: Syntax Flexibility; Using 'function'/'endfunction' keywords
Using 'function'/'endfunction' keywords
=======================================
Example:
	one() two() three()
	function one 
		? :one 
	endfunction
	function two
		? :two
	endfunction
	function three
		? :three
	endfunction
.. index:: 
	pair: Syntax Flexibility; Using 'endif'/'endfor'/'endwhile'/'endswitch'/'endtry' keywords
Using 'endif'/'endfor'/'endwhile'/'endswitch'/'endtry' keywords
===============================================================
Example:
	for t=1 to 10 
		if t=3 
			? :three
		endif
	endfor
.. index:: 
	pair: Syntax Flexibility; Using 'endpackage'/'endclass'/'endfunc' keywords after Packages/Classes/Functions
Using 'endpackage'/'endclass'/'endfunc' keywords after Packages/Classes/Functions
=================================================================================
Example:
	import mypackage
	new myclass { myfunc() }
	package mypackage
		class myclass
			func myfunc			
				see "welcome"  + nl
			endfunc
		endclass
	endpackage
.. index:: 
	pair: Syntax Flexibility; Ignore new lines after keywords
Ignore new lines after keywords
===============================
Starting from Ring 1.8 the compiler will ignore new lines after keywords that expect tokens after it
Example:
	see 
	"
		Hello, World!
	"
	test()
	func 
	#======================#
		Test
	#======================#
		?
		"
	
		Hello from the Test function
		"
Output:
.. code-block:: none
	        Hello, World!
	        Hello from the Test function
.. index:: 
	pair: Syntax Flexibility; Automatic loading for syntax files
Automatic loading for syntax files
==================================
Starting from Ring 1.18 we have better syntax flexibility


* You can build your application using Qt Creator
.. code-block:: ring

	if isWebAssembly()
 		mypic = new QPixmap(":/cards.jpg")
	else
    		mypic = new QPixmap("cards.jpg")
	ok
.. index:: 
	pair: Building RingQt Applications for WebAssembly; Comments about developing for WebAssembly using RingQt
Comments about developing for WebAssembly using RingQt
======================================================
(1) The main project file is main.cpp 
	This file load Ring Compiler/Virtual Machine and RingQt 
	Then get the Ring Object File during the runtime from the resources
	Then run the Ring Object File (ringapp.ringo) using the Ring VM 
	Through main.cpp you can extract more files from the resources to temp. folder once you
	add them (create projects with many files).
(2) use if isWebAssembly() when you want to modify the code just for WebAssembly
Example:
	if isWebAssembly()
		// WebAssembly code
	else
  		// other platforms
	ok
(3) When you deal with Qt Classes you can determine the images from resources (you don't need to copy them using main.cpp)
Example: 
	if isWebAssembly()
	    mypic = new QPixmap(":/cards.jpg")
	else
	    mypic = new QPixmap("cards.jpg")
	ok
Now RingQt comes with the AppFile() function to determine the file name 
Example:
	mypic = new QPixmap(AppFile("cards.jpg"))  # Desktop, Android or WebAssembly
(4) When you update your project code, You don't have to use Ring2EXE to generate the Qt project again
Just use the Distribute Menu in Ring Notepad and select (Generate Ring Object File)
Then copy the YourAppName.ringo file to target/webassembly/qtproject folder and accept replacing files.
(5) If your application folder contains a Qt resource file (project.qrc)
Then when you use Ring2EXE or Ring Notepad (Distribute - Prepare Qt project for WebAssembly) the 
resource file will be used 
See ring/applications/cards game as an example.
(6) Use stdlibcore.ring instead of stdlib.ring when using StdLib functions
(7) Use ClocksPerSecond() function instead of typing the value (1000)
(8) Nested events loops are not supported, use events for dialogs instead of calling the exec() method
(9) Using Sleep() or ProcessEvents() doesn't provide the expected results, use Qt Timers.
(10) We don't have a direct access to the File System because the applications are executed in a secure environment
.. tip:: We can use special functions for Uploading/Downloading files (See FileContent sample) 
.. index:: 
	pair: Building RingQt Applications for WebAssembly; Dialogs
Dialogs
=======
See the folder: ring/samples/UsingQtWASM
Folders: 


* Clear Error Message if the Event Code is larger than the allowed size
.. code-block:: ring

	load "guilib.ring"
	
	oApp = new qapp {
		w = new qWidget() {
			setWindowTitle("Using Clipboard - Press CTRL+V")
			resize(400,100)
			new qlineedit(w) {
				move(10,10)
				resize(200,30)
			}
			show()
		}
		oApp.clipboard().setText("I Love Ring Programming!",0)
		
		exec()
	}
Screen Shot:
.. image:: usingclipboard.png
	:alt: usingclipboard
Example (Using Strings):
	load "guilib.ring"
	
	o1 = new QString2()
	o1.append("Ring")
	
	oChar = new QChar(61)
	? o1.leftJustified(20,oChar,False)
	? o1.rightJustified(20,oChar,False)
Output:
.. code-block:: none
	Ring================
	================Ring
.. index:: 
	pair: What is new in Ring 1.15?; Better RingLibCurl
Better RingLibCurl
==================


* Main Menu
.. code-block:: ring

	while true 
		see nl + "code:> "
		give cCode 
		try
			eval(cCode) 
		catch
			see cCatchError
		done
	end
Output:
	code:> see "hello world"
	hello world
	code:> for x = 1 to 10 see x + nl next
	1
	2
	3
	4
	5
	6
	7
	8
	9
	10
	code:> func test see "Hello from test" + nl
	code:> test()
	Hello from test
	code:> bye
.. index:: 
	pair: Demo Programs; Main Menu
Main Menu
=========
Example:
	# Demo Program
	while true
		see "
		Main Menu
		===========
		[1] Say Hello
		[2] Sum two numbers
		[3] Stars
		[4] Fact
		[5] Exit
		" give nMenu see nl
		# we can use Switch-ON-Other-OFF instead of IF-BUT-ELSE-OK
		Switch nMenu
		On 1 sayhello() 
		On 2 Sum()
		On 3 Stars()
		On 4 
			see "Enter Number : " give x
			see "Output : " 
			Try	
				see Fact(number(x))
			Catch
				see "Error in parameters!" + nl
			Done
		On "5" return 
		Other see "bad option" + nl
		Off
	end
	func sayhello
		see "Enter your name ? " give fname
		see "Hello " + fname + nl
	func sum
		see "number 1 : " give num1 see "number 2 : " give num2
		see "Sum : " see 0 + num1 + num2
	func stars
		for x = 1 to 10
			see space(8)
			for y = 1 to x see "*" next see nl
		next
	func fact x if x = 0 return 1 else return x * fact(x-1) ok
	func space x y = "" for t=1 to x y += " " next return y
Output:
	        Main Menu
        	===========
	        [1] Say Hello
        	[2] Sum two numbers
	        [3] Stars
        	[4] Fact
	        [5] Exit
	        1
	Enter your name ? Mahmoud Fayed
	Hello Mahmoud Fayed
	        Main Menu
        	===========
	        [1] Say Hello
        	[2] Sum two numbers
	        [3] Stars
        	[4] Fact
	        [5] Exit
	        2
	number 1 : 3
	number 2 : 4
	Sum : 7
	        Main Menu
        	===========
	        [1] Say Hello
        	[2] Sum two numbers
	        [3] Stars
        	[4] Fact
	        [5] Exit
	        3
	        *
        	**
	        ***
        	****
	        *****
        	******
	        *******
	        ********
        	*********
	        **********
	        Main Menu
        	===========
	        [1] Say Hello
	        [2] Sum two numbers
        	[3] Stars
	        [4] Fact
	        [5] Exit
	        4
	Enter Number : 5
	Output : 120
	        Main Menu
        	===========
	        [1] Say Hello
        	[2] Sum two numbers
	        [3] Stars
        	[4] Fact
	        [5] Exit
	        5


* The Input() Function
.. code-block:: ring

	Give VariableName
Example:
	See "Enter the first number : " Give nNum1
	See "Enter the second number : " Give nNum2
	See "Sum : " + ( 0 + nNum1 + nNum2 )
 
Output:
	Enter the first number : 3
	Enter the second number : 4
	Sum : 7
.. index:: 
	pair: Getting Input; GetChar()
GetChar() Function
==================
We can get one character from the standard input using the GetChar() function
Syntax:
	GetChar() ---> Character
Example:
	While True
		See "
			Main Menu
			(1) Say Hello
			(2) Exit
		    " 
		Option = GetChar()
		GetChar() GetChar()  # End of line
		# the previous two lines can be replaced with the next line
		# Give Option
		if Option = 1
			see "Enter your name : " give cName 
			see "Hello " + cName
		else
			bye
		ok
	End
.. index:: 
	pair: Getting Input; Input()
Input() Function
================
We can get input from the keyboard using the Input() function
Syntax:
	Input([nCount]) ---> string
The function will wait until nCount characters (at least) are read
.. tip:: If the nCount is not passed as parameter, the function will read a line.
Example:
	See "Enter message (30 characters) : " cMsg = input(30)
	See "Message : " + cMsg


* socketsCleanup()
.. code-block:: ring

	cClassName = "myclass2"
	myobj = new from cClassName
	cClassName = "myclass"
	myobj = new from cClassName
	class myclass
		? :hello
	class myclass2
		? :wow
Output:
.. code-block::
	wow
	hello
.. index:: 
	pair: What is new in Ring  1.18; ImportPackage() Function
ImportPackage() Function
========================
Instead of using the import command we can use the importpackage() function
This function get the package name through a string or variable
This is useful if the package name will be known only during the runtime
Syntax:
	importpackage(cPackageName)
Example:
	importpackage(:mypackage)
	new myclass { myfunction() }
	package mypackage
		class myclass
			function myfunction
				? "Hello, World!"
.. index:: 
	pair: What is new in Ring  1.18; More Low Level Functions
More Low Level Functions
========================
The next functions are added to the Low Level functions


* Compiler : Support using f after numbers
.. code-block:: ring

	x = 19.99f
	see type(x) + nl
Output:
	NUMBER


* Using 'end' keyword after Packages/Classes/Functions
.. code-block:: ring

	import mypackage
	new myclass {
		myfunc() 
	}
	package mypackage 
		class myclass 
			def myfunc 
				put "Hello, World!"
			end
		end
	end


* continue
.. code-block:: ring

	for t=1 to 10
		? t
		if t=3 
			? :three
		endif
	endfor
.. index:: 
	pair: What is new in Ring  1.17; Better I/O Functions
Better I/O Functions
====================
The next functions can be used without the need to load stdlib.ring


* Bytes2Double()
.. code-block:: ring

	see "Test Int2Bytes() and Bytes2Int() - Value : 77" + nl
	r = Int2Bytes(77)
	see "Int Size : " + len(r) + nl
	see r + nl
	see Bytes2Int(r) + nl
	see "Test Float2Bytes() and Bytes2Float() - Value 77.12" + nl
	r = Float2Bytes(77.12)
	see "Float Size : " + len(r) + nl
	see r + nl
	see Bytes2Float(r) + nl
	see "Test Double2Bytes() and Bytes2Double() - Value 9999977.12345" + nl
	r = Double2Bytes(9999977.12345)
	see "Double Size : " + len(r) + nl
	see r + nl
	decimals(5)
	see Bytes2Double(r) + nl
.. index:: 
	pair: What is new in Ring 1.4?; Better StdLib
Better StdLib
=============
The StdLib is updated to include the next functions


* curl_easy_setopt() support passing (CURLLIST *) as the third parameter
.. code-block:: ring

	# Header
	mylist = curl_slist_append(null,'accept-encoding: identity')
	mylist = curl_slist_append(mylist,'authorization: Bearer ' + my_api_key )
	mylist = curl_slist_append(mylist,'connection: close')
	mylist = curl_slist_append(mylist,'content-length: ' + len(my_body) )
	mylist = curl_slist_append(mylist,'content-type: application/json')
	curl_easy_setopt(curl, CURLOPT_HTTPHEADER, mylist)
.. index:: 
	pair: What is new in Ring 1.15?; Heroku (Better support)
Heroku (Better support)
=======================
From Ring 1.8 We can run Ring Web Applications in the Cloud using Heroku
In Ring 1.15 We updated the packages to use a modern Ring version
Also to avoid all of the reported problems during installation
URL: https://github.com/ring-lang/RingWebAppOnHeroku
.. index:: 
	pair: What is new in Ring 1.15?; Using ':' then Numbers
Using ':' then Numbers
======================
Ring 1.15 support using ':' then Numbers to define literals
Example:
	? Type( :1234 )
	aList = [ :1 = "One", 
		  :2 = "Two"]
	? aList[:1]
	? aList[:2]
Output:
.. code-block:: none
	STRING
	One
	Two
.. index:: 
	pair: What is new in Ring 1.15?; More Samples
More Samples
============
The next samples are added to the samples folder
	*  samples/Language/AnonFunctions/anonfunc1.ring
	*  samples/Language/AnonFunctions/anonfunc2.ring
	*  samples/Language/AnonFunctions/anonfunc3.ring
	*  samples/Language/AnonFunctions/anonfunc4.ring
	*  samples/Language/AnonFunctions/anonfunclib.ring
	*  samples/Language/Numbers/powfunc.ring
	*  samples/Language/Strings/trim.ring
	*  samples/Language/Strings/trim2.ring
	*  samples/Language/Loops/loopcommand.ring
	*  samples/General/NumberTripletsGame/NumberTripletsGame.ring
	*  samples/General/Elevator/elevator.ring
	*  samples/General/SmallExamples/ParametersOrder/parametersorder.ring
	*  samples/UsingArabic/WriteArabicFile/using_QFile_write.ring
	*  samples/UsingArabic/WriteArabicFile/using_QFile_read.ring
	*  samples/UsingRayLib/more/ex6_jump_player2D.ring
	*  samples/UsingRayLib/more/ex7_jump_player3D.ring
	*  samples/UsingRayLib/more/ex8_Aiming_at_3D_moving_targets.ring
	*  samples/UsingRayLib/more/ex9_jump_player3D_collision_detection.ring
	*  samples/UsingRayLib/more/ex10_jump_player3D_long_way.ring
	*  samples/UsingQt/String/QCharUnicodeValue.ring
	*  samples/UsingQt/String/QCharUnicodeValue2.ring
	*  samples/UsingQt/String/QStringJustified.ring
	*  samples/UsingQt/Clipboard/QTextEditClipboard.ring
	*  samples/UsingQt/Clipboard/UsingQClipboard.ring
	*  samples/UsingQt/Time/timemillisecond.ring
	*  samples/UsingQt/ByteArray/Base64.ring
	*  samples/UsingWebLib/PathInfo/pathinfo.ring
	*  samples/Drawing/ColorWheel/ColorWheel-Animate.ring
	*  samples/Drawing/ColorWheel/ColorWheel-FastDraw.ring
Screen Shot:
.. image:: jump3d.png
	:alt: Jump3D
.. index:: 
	pair: What is new in Ring 1.15?; More Improvements
More Improvements
=================


* Assert() function
.. code-block:: ring

	Try
		Statements...
	Catch
		Statements...
	Done
The statements in the Try block will be executed, if any error happens then
the statements in the catch block will be executed.
Inside the catch block we can use the variable cCatchError to get the error message
Example:
	Try
		see 5/0
	Catch
		see "Catch!" + nl + cCatchError
	Done
Output:
	Catch!
	Error (R1) : Can't divide by zero !
.. index:: 
	pair: Eval() and Debugging; Eval()
Eval() Function
===============
We can execute code during the runtime from string using the Eval() function
Syntax:
	Eval(cCode)
Example:
	Eval("nOutput = 5+2*5 " )
	See "5+2*5 = " + nOutput + nl			 
	Eval("for x = 1 to 10 see x + nl next")		 
	Eval("func test see 'message from test!' ")	 
	test()
Output:
	5+2*5 = 15
	1
	2
	3
	4
	5
	6
	7
	8
	9
	10
	message from test!
We can use the Return command to return a value
Example:
	see Eval("return 5*5")
Output:
	25
.. index:: 
	pair: Eval() and Debugging; Raise()
Raise() Function
================
We can raise an exception using the Raise() function
Syntax:
	Raise(cErrorMessage)
The function will display the error message then end the execution of the program.
We can use Try/Catch/Done to avoid exceptions generated by raise() function.
Example:
	nMode = 10
	if nMode < 0 or nMode > 5
		raise("Error : nMode not in the range 1:4")
	ok
Output:
	Line 4 Error : nMode not in the range 1:4
	In raise in file tests\raise.ring
Example:
	try 
		testmode(6)
	catch
		see "avoid raise!"
	done
	testmode(-1)
	func testmode nMode
		if nMode < 0 or nMode > 5
			raise("Error : nMode not in the range 1:4")
		ok
Output:
	avoid raise!
	Line 12 Error : nMode not in the range 1:4
	In raise In function testmode() in file tests\raise2.ring
	called from line 7  in file tests\raise2.ring
.. index:: 
	pair: Eval() and Debugging; Assert()
Assert() Function
=================
We can use the Assert() function to test conditions before executing the code
If the test fail the program will be terminated with an error message contains the assert condition.
Syntax:
	Assert( condition )
Example:
	x = 10
	assert( x = 10)
	assert( x = 100 ) 
Output:
	Line 3 Assertion Failed!
	In assert in file tests\assert.ring


* More Improvements
.. code-block:: ring

	load "pdfgen.ring"
	
	cPDFFileName = "output.pdf"
	
	pdf = pdf_create(PDF_A4_WIDTH, PDF_A4_HEIGHT, [
		:creator  = "My software",
		:producer = "My software",
		:title    = "My document",
		:author   = "My name",
		:subject  = "My subject",
		:date     = "Today"
	] )
	
	pdf_set_font(pdf, "Times-Roman")
	pdf_append_page(pdf)
	
	pdf_add_text(pdf, NULL, "This is text", 12, 50, 20, PDF_BLACK)
	pdf_add_line(pdf, NULL, 50, 24, 150, 24, 0, 0)
	pdf_add_text(pdf, NULL, "This is text", 24, 250, 20, PDF_BLUE)
	
	for t=1 to 30
		pdf_add_text(pdf, NULL, "Number: " + t, 14, 250, 50+(20*t), PDF_RED)
	next
	
	pdf_add_text(pdf, NULL, "I LOVE PROGRAMMING!", 48, 30, 700,PDF_BLUE)
	
	pdf_save(pdf, cPDFFileName)
	pdf_destroy(pdf)
	
	system(cPDFFileName)
Output:
.. image:: PDFGen.png
	:alt: PDFGen
.. index:: 
	pair: What is new in Ring  1.21; Better MatrixLib
Better MatrixLib
================
The next functions are added to the library
.. code-block:: none
	MatrixScalarProjection(U,V)  		// Scalar Projection A onto B = (A.B) / |B|
	MatrixCopyCol(U-Array, Start, End)	// Create new smaller V-array from U-array 
Example
	Load "stdlibcore.ring"
	Load "matrixlib.ring"
	Func Main()
 
		A =  [[  1],[  6],[ 18]]       
		B =  [[ 42],[-69],[ 98]]       
                        
		See "A " MatrixPrint(A)
		See "B " MatrixPrint(B)
		SP = MatrixScalarProjection(B,A)
		See "Scalar projection of B onto A = 73.26  => "+ SP +nl
		
		SP = MatrixScalarProjection(A,B)
		See "Scalar projection of A onto B = 10.96  => "+ SP +nl
		
		A =  [[ 3],[ 4]]       
		B =  [[ 1],[ 2]] 
		
		See "A " MatrixPrint(A)
		See "B " MatrixPrint(B) 
		
		SP = MatrixScalarProjection(A,B)
		See "Scalar projection of A onto B = 4.9193 => "+ SP +nl
Output:
.. code-block:: none
	A       MatrixPrint: 3x1
	|  1  |
	|  6  |
	|  18 |
	
	B       MatrixPrint: 3x1
	|  42 |
	| -69 |
	|  98 |
	
	Scalar projection of B onto A = 73.26  => 73.26
	Scalar projection of A onto B = 10.96  => 10.96
	A       MatrixPrint: 2x1
	|  3 |
	|  4 |
	
	B       MatrixPrint: 2x1
	|  1 |
	|  2 |
	
	Scalar projection of A onto B = 4.9193 => 4.92
.. index:: 
	pair: What is new in Ring  1.21; Better RingQt
Better RingQt
=============
(1) Qt library is updated from Qt 5.15.16 to 5.15.17
(2) QDateTimeEdit - Added Class Events
(3) Using (load "qtcore.ring") we can load the QtCore library
(4) The next classes are added to RingQt


* Practical language designed for creating the next version of the Programming Without Coding Technology software.
.. code-block:: ring

	f = func {
	    a = 42
	    return func { return a }
	}
 
	innerF = call f()
	call innerF()
Output:
.. code-block:: none
	Using uninitialized variable : a In function _ring_anonymous_func_16601()
The Answer:


* In Functions and methods the current scope is the local scope.
.. code-block:: ring

	name = "test"
	o1 = new person
	see o1
	class person
		name
		address 
		phone
In the previous example we have a global variable called 'name'
inside the class person.
when we use the variable 'name', Ring will start the search operation and 
will try to find it.
if found ---> Use it
if not found ---> Define new attribute
But the variable name is a global variable, so it will be found and used!
We will not have the attribute name! added to the object. 
Solution (1) - Use the Main Function 
	func main
		name = "test"
		o1 = new person
		see o1
	class person
		name
		address 
		phone
Solution (2) - Use special mark for global variable names like $
	$name = "test"
	o1 = new person
	see o1
	class person
		name
		address 
		phone
Solution (3) - Use the AddAttribute() Method
	name = "test"
	o1 = new person
	see o1
	class person
		AddAttribute(self,"name")
		address 
		phone
Solution (4) - Use self before the attribute name
	name = "test"
	o1 = new person
	see o1
	class person
		self.name
		address 
		phone
So what is the best solution to this conflict?
1 - Use the $ Mark for global variables 
2 - Optional : Try to avoid global variables and use the Main function 
In practice i do both of them.
The other solution 


* Private Flag (For Private Methods in Classes)
.. code-block:: ring

	RingVM_FunctionsList() ---> List
Example:
	test()
	func test
		see ringvm_functionslist()
Output:
	test
	8
	B:/ring/tests/scripts/functionslist.ring
	0
.. index:: 
	pair: Low Level Functions; RingVM_ClassesList()
ringvm_classeslist() function
=============================
The Function return a list of Classes.
Each List Member is a list contains the next items


* We will push declarative and natural paradigms many steps forward. Also in next versions 
.. code-block:: ring

	mystring = read("myfile.exe")
(2) Remember this, when you think about variables


* More Improvements
.. code-block:: ring

	load "lightguilib.ring"
Instead of
	load "guilib.ring"
Also Ring Notepad, Distribute menu comes with a new option : Distribute light RingQt application
Using this option we can distribute lightguilib applications
For example, Distributing (Game of Life) using this option provides : target/windows folder 
.. image:: lightguilib.png
	:alt: lightguilib


* QAxBase : Different versions for the dynamicCall() and querySubObject() methods.
.. code-block:: ring

	load "guilib.ring"
	new qApp {
		win1 = new qwidget() {
			setwindowtitle("Printer Preview Dialog")
			setgeometry(100,100,800,880)
			printer1 = new qPrinter(0)
			show()
			oPreview = new qPrintPreviewDialog(printer1) {
				setParent(win1)
				move(10,10) 
				setPaintrequestedevent("printPreview()")
				exec()
			}
		}
		exec()
	}
	func printPreview
		printer1  {
			painter = new qpainter() {
				begin(printer1)
				myfont = new qfont("Times",50,-1,0)
				setfont(myfont)
				drawtext(100,100,"Test - Page (1)")
				printer1.newpage()
				drawtext(100,100,"Test - Page (2)")
				printer1.newpage()
				myfont2 = new qfont("Times",14,-1,0)
				setfont(myfont2)
				for x = 1 to 30
					drawtext(100,100+(20*x),"Number : " + x)
				next 
				endpaint()
			}
		}
Screen Shot:
.. image:: printpreviewdialog.png
	:alt: Print Preview Dialog
.. index:: 
	pair: What is new in Ring 1.9?; Better Memory Management
Better Memory Management
========================
The Ring API is updated to include RING_API_RETMANAGEDCPOINTER()
Using RING_API_RETMANAGEDCPOINTER() the Ring extensions written in C/C++ languages can return a managed
pointer to Ring. This pointer can be controlled by the Ring VM using reference counting.
This is important to avoid the need to write code that free the unmanaged resources like QPixMap objects in RingQt.
Also the Code Generator for extensions is updated to automatically use RING_API_RETMANAGEDCPOINTER() based on need.
Syntax:
	RING_API_RETMANAGEDCPOINTER(void *pValue,const char *cPointerType,
					void (* pFreeFunc)(void *,void *))
For more information about RING_API_RETMANAGEDCPOINTER()
See the "Extension using the C/C++ languages" Chapter in the documentation 
.. index:: 
	pair: What is new in Ring 1.9?; Better Code Generator for Extensions
Better Code Generator for Extensions
====================================
(1) The code generator for extensions is updated to support the <loadfile> command
	<loadfile> filename.cf
This is useful to separate the extension configuration file to many files
Example:
The file : qt_module_network.cf in the RingQt Extension
	<comment>
					Module (network)
	</comment>
	<loadfile> qabstractsocket.cf
	<loadfile> qnetworkproxy.cf
	<loadfile> qtcpsocket.cf
	<loadfile> qtcpserver.cf
	<loadfile> qhostaddress.cf
	<loadfile> qhostinfo.cf
	<loadfile> qnetworkrequest.cf
	<loadfile> qnetworkaccessmanager.cf
	<loadfile> qnetworkreply.cf
(2) The code generator support the <managed> option when defining classes.
Using this option, the generator will use RING_API_RETMANAGEDCPOINTER() to return the C pointer.
So the Garbage Collector will manage these C pointers.
Example
	<class>
	name: QFont
	para: QString, int, int, bool
	managed
	</class>
.. index:: 
	pair: What is new in Ring 1.9?; More Improvements
More Improvements
=================
(1)  Ring Compiler - The Rule (Factor -> '-' Expr) changed to (Factor -> '-' Factor).
(2)  Ring VM - Better Error Message.
(3)  Better code for IsNULL() function - updated to check pointers too.
(4)  Better code for ringvm_evalinscope() function - used by the Trace Library.
(5)  Better code for Space() function.
(6)  Better code for Hex() and Dec() functions.
(7)  Better code for Download() function.
(8)  Better code for SubStr() function.
(9)  Better code for the Unsigned() function.
(10) Better code for Chdir() function.
(11) Better code for Tempname() function.
(12) Better code for HashTable - New Key (using ring_strdup() instead of strdup() function).
(13) New Function : SRandom() - Initialize random number generator. 
(14) New Function : IsPointer().
(15) IsList() will not return True for C Pointers, we have now the IsPointer() function. 
(16) The ? Operator is updated to respect the ringvm_see() function.
(17) Scripts to run Ring tests on Linux and macOS (Not only Windows).
(18) RingAllegro is Updated from Allegro 5.1 to Allegro 5.2.
(19) Shader Functions are added to RingAllegro.
(20) Joystick Functions are added to RingAllegro.
(21) Network functions are added to RingLibSDL.
(22) Game Engine for 2D Games - Text Class - Check the font object before usage.
(23) Game Engine for 2D Games - Automatic support for Joystick.
(24) RingLibCurl is updated to automatically use CURLOPT_COPYPOSTFIELDS when needed.
(25) Ring Notepad - Find Previous Feature.
(26) Ring Notepad - Default Style. 
(27) Ring Notepad - Support using Non-English language (Like Arabic) in file names.
(28) Form Designer - Nice Alignment for Toolbox Icons.
(29) Form Designer - QAllEvents Class - Mouse Double Click Event.
(30) Find in Files - Replace and Replace All options.
(31) Qt Class Converter is updated for better conversion results.
(32) More samples are added to ring/samples/other folder.
(33) Code Refactoring for Ring Notepad, RingQt, Game Engine for 2D Games.
(34) Better Documentation - Many images are updated to reflect the current state of Ring Environment.
(35) Better Documentation - More chapters are added to the documentation.


* For Loop
.. code-block:: ring

	for identifier=expression to expression [step expression] {
		Block of statements
	}
Example:
	# print numbers from 1 to 10
	for x = 1 to 10	 { 
		print("#{x}\n") 
	}
Example:
	# Dynamic loop
	print("Start : ") nStart = getnumber()
	print("End   : ") nEnd = getnumber()
	print("Step  : ") nStep = getnumber()
	for x = nStart to nEnd step nStep {
		print("#{x}\n") 
	}
Example:
	# print even numbers from 0 to 10
	for x = 0 to 10 step 2 {
		print("#{x}\n") 
	}
Example:
	# print even numbers from 10 to 0
	for x = 10 to 0 step -2 {
		print("#{x}\n") 
	}
.. index:: 
	pair: Control Structures - Third Style; For In Loop


* Static-Analysis
.. code-block:: ring

	load "typehints.ring"
	see sum(3,4) + nl ;
	see sayHello("Mahmoud");
	int func sum(int x,int y) {
		return x+y ;
	}
	string func sayHello(string name) {
		return "Hello " + name ;
	}
.. index:: 
	pair: The Type Hints Library; User Types
User Types
==========
The Type Hints library is very powerful and will support user types (Classes) automatically
Example:
	load "typehints.ring"
	import mypackage 
	test()  { main([:one,:two,:three]) }
	myclass func test() {
		see "Testing User Types!" + nl
		return new myclass
	}
	package mypackage {
		public class myclass {
			public static void func main(list args) {
				see "welcome" + nl
				see args
			}
		}
	}
.. index:: 
	pair: The Type Hints Library; Using Types inside Code
Using Types inside Code
=======================
Also you can use the types inside the code (not only the function prototype)
Example:
	load "typehints.ring"
	int 	sum = sum(3,4)
	string 	msg = sayHello("Mahmoud")
	see "Sum = " + sum + nl + msg + nl
	int func sum(int x,int y) {
		return x+y ;
	}
	string func sayHello(string name) {
		return "Hello " + name ;
	}
.. index:: 
	pair: The Type Hints Library; Using Override
Using Override
==============
We can use override or @override 
Example:
	load "typehints.ring"
	o = new MyNewLib {
		? isGreaterThanTwo(10)
		? isGreaterThanTwo(1)
	}
	class MyLib {
		boolean func isGreaterThanTwo(int x) {
			if x > 2
				return true
			else
				return false
			ok
		}
	}
	class MyNewLib < MyLib {
		@override
		boolean func isGreaterThanTwo(int x) {
			? "Using override"
			return x > 2
		}
	}
Output:
.. code-block:: none
	Using override
	1
	Using override
	0
.. index:: 
	pair: The Type Hints Library; Rules
Rules
=====


* RingQt : New methods added to QMenu and QCursor Classes
.. code-block:: ring

	load "guilib.ring"
	new qApp {
		win = new qwidget() {
			setwindowtitle("Context Menu")
			resize(400,400)
			myfilter = new qAllEvents(win) {
				setContextmenuEvent("mymenu()")
			}
			installeventfilter(myfilter)
			show()
		}
		exec()
	}
	func mymenu 
		new qMenu(win) {
			oAction = new qAction(win) {
				settext("new")
				setClickEvent("See :New")
			}
			addaction(oAction)
			oAction = new qAction(win) {
				settext("open")
				setClickEvent("See :Open")
			}
			addaction(oAction)
			oAction = new qAction(win) {
				settext("save")
				setClickEvent("See :Save")
			}
			addaction(oAction)
			oAction = new qAction(win) {
				settext("close")
				setClickEvent("See :Close")
			}
			addaction(oAction)
			oCursor  = new qCursor()
			exec(oCursor.pos())
		}
	


* For in Loop
.. code-block:: ring

	for identifier in List/String  [step expression] {
		Block of statements
	}
Example:
	aList = 1:10	# create list contains numbers from 1 to 10
	for x in aList { print("#{x}\n") }  # print numbers from 1 to 10
Example:
	aList = 1:10	# create list contains numbers from 1 to 10
	# print odd items inside the list
	for x in aList step 2 {
		print("#{x}\n") 
	}
When we use (For in) we get items by reference.
This means that we can read/edit items inside the loop.
	
Example:
	aList = 1:5	# create list contains numbers from 1 to 5
	# replace list numbers with strings
	for x in aList {
		switch x {
		case 1  x = "one"
		case 2  x = "two"
		case 3  x = "three"
		case 4  x = "four"
		case 5  x = "five"
		}
	}
	print(aList)	# print the list items
.. index:: 
	pair: Control Structures - Third Style; Exceptions
Exceptions
==========
	try {
		Block of statements
	catch
		Block of statements
	}


* To determine a prefix in all of the functions names type it between <funcstart> and </funcstart>
.. code-block:: ring

	<funcstart>
	al
	</funcstart>
.. index:: 
	pair: Code Generator; Wrap structures
Generate function to wrap structures
====================================


* Pawn promotion to Queen
.. code-block:: ring

	load "stdlib.ring"
	aList = 1:5
	? RandomList(aList)
(2) RandomItem() function
Pick an item from a list (Random Choice)
Syntax:
.. code-block:: none
	RandomItem(aList) --> Item 
Example:
	load "stdlib.ring"
	aList = 1:5
	? RandomItem(aList)
(3) List2Code() function


* Calculator Sample
.. code-block:: ring

	load "tilengine.ring"
	TLN_Init(400, 240, 1, 0, 0)
	TLN_SetLoadPath("assets\sonic")
	foreground = TLN_LoadTilemap ("Sonic_md_fg1.tmx", NULL)
	TLN_SetLayerTilemap(0, foreground)
	TLN_CreateWindow(NULL, 0)
	while TLN_ProcessWindow()
		TLN_DrawFrame(0)
	end
	TLN_DeleteTilemap(foreground)
	TLN_Deinit()
Screen Shots:
.. image:: tilengine_shot3.png
	:alt: tilengine
.. image:: tilengine_shot5.png
	:alt: tilengine
.. image:: tilengine_shot8.png
	:alt: tilengine
.. image:: tilengine_shot9.png
	:alt: tilengine
.. index:: 
	pair: What is new in Ring 1.14?; RingLibui Extension
RingLibui Extension
===================
This extension provides complete support for Libui 
Using this extension we can develop and distribute lightweight GUI Applications using Ring (Less than 1 MB)
Runtime files and their size


* Better Template() function - can accept NULL instead of object as the second parameter.
.. code-block:: ring

	html(template("main.rhtml",NULL))


* getnumber() -> nNumber
.. code-block:: ring

	puts("Hello, World!")
	puts(2022)
	puts("one\ntwo\nthree")
	puts("one\n\ttwo\n\t\tthree")
	age = 6
	puts("Ring is #{age} years old!") 
	puts("I know that 2+2=#{2+2} and 3+3=#{3+3}")
	happy()
	func happy
		new myclass {x=10 y=20 z=30 test()}
		? :done
	class myclass 
		name = "Ring"
		x y z 
		func test
			puts("Language Name = #{name}")
			puts("x=#{x}\ny=#{y}\nz=#{z}")
Output:
.. code-block:: none
	Hello, World!
	2022
	one
	two
	three
	one
			two
					three
	Ring is 6 years old!
	I know that 2+2=4 and 3+3=6
	Language Name = Ring
	x=10
	y=20
	z=30
	done
.. index:: 
	pair: What is new in Ring  1.17; Better Ring API
Better Ring API
===============
The next functions are added to Ring API


* Compiler : Support using _ in numbers
.. code-block:: ring

	x = 1_000_000
	see type(x)+nl
	see x+1+nl
Output:
	NUMBER
	100000001


* Low Level Functions
.. code-block:: ring

	Load "stdlib.ring"
	Puts("Test Times()")
	Times ( 3 , func { see "Hello, World!" + nl } )
Example:
	Load "stdlib.ring"
	Puts("Test Map()")
	See Map( 1:10, func x { return x*x } )
Example:
	Load "stdlib.ring"
	Puts("Test Filter()")
	See Filter( 1:10 , func x { if x <= 5 return true else return false ok } )
Example:
	Load "stdlib.ring"
	See "Testing the String Class" + nl
	oString = new string("Hello, World!")
	oString.println()
	oString.upper().println()
	oString.lower().println()
	oString.left(5).println()
	oString.right(6).println()
Example:
	Load "stdlib.ring"
	oList = new list ( [1,2,3] )
	oList.Add(4)
	oList.print()
Example:
	Load "stdlib.ring"
	oStack = new Stack
	oStack.push(1)
	oStack.push(2)
	oStack.push(3)
	see oStack.pop() + nl
Example:
	Load "stdlib.ring"
	oQueue = new Queue
	oQueue.add(1)
	oQueue.add(2)
	oQueue.add(3)
	see oQueue.remove() + nl
Example:
	Load "stdlib.ring"
	ohashtable = new hashtable
	See "Test the hashtable Class Methods" + nl
	ohashtable { 
		Add("Egypt","Cairo")
		Add("KSA","Riyadh")
		see self["Egypt"] + nl
		see self["KSA"] + nl
		see contains("Egypt") + nl
		see contains("USA") + nl
		see index("KSA")  + NL
		print()
		delete(index("KSA"))
		see copy("*",60) + nl
		print()
	}
Example:
	Load "stdlib.ring"
	otree = new tree
	See "Test the tree Class Methods" + nl
	otree {
		set("The first step")	# set the root node value
		see value() + nl
		Add("one")
		Add("two")
		Add("three") {
			Add("3.1")
			Add("3.2")
			Add("3.3")
			see children
		}
		see children
		oTree.children[2] {
			Add("2.1") Add("2.2") Add("2.3") {
				Add("2.3.1") Add("2.3.2") Add("test")
			}
		}
		oTree.children[2].children[3].children[3].set("2.3.3")
	}
	see copy("*",60) + nl
	oTree.print()
Check the next chapters:


* Method or Function (Bool : True=Method, False=Function/File)
.. code-block:: ring

	RingVM_TraceData() ---> aDataList
.. index:: 
	pair: Low Level Functions; ringvm_traceevent()
ringvm_traceevent()
===================
Inside the function that we will use for tracing events
We can use ringvm_traceevent() to know the event type


* The operator() method output will be stored in myVar
.. code-block:: ring

	optionalFunc(:reply)
	? "I love Programming, What about you?"
	reply()
	? "Ok, Thanks!"
Output:
.. code-block:: none
	I love Programming, What about you?
	Ok, Thanks!
File: Answer.ring
	load "Question.ring"
	func reply
		? "Me too!"
Output:
.. code-block:: none
	I love Programming, What about you?
	Me too!
	Ok, Thanks!
.. index:: 
	pair: What is new in Ring  1.19; ParentClassName()
ParentClassName() Function
==========================
		
We can know the parent class name of an object using the parentclassname() function
Syntax:
.. code-block:: none
	parentclassname(object) --> Returns the parent class name of the object class 
Example:
	new Child  { test() }
	class Parent
	class Child from Parent
		func test
			? "Parent: " + parentClassName(self)
Output:
.. code-block:: none
	Parent: parent
.. index:: 
	pair: What is new in Ring  1.19; FastPro Extension
FastPro Extension
=================
This new extension comes with the next functions


* Swap() function
.. code-block:: ring

	aList = [:one,:two,:four,:three]
	see aList
	see copy("*",50) + nl
	swap(aList,3,4)
	see aList
Output
	one
	two
	four
	three
	**************************************************
	one
	two
	three
	four
.. index:: 
	pair: What is new in Ring 1.3?; Return Self by Reference
Return Self by Reference
========================
In this release, using Return Self in class methods will return the object by reference.
Example:
	mylist = [new mytest() {
		see self
		x = 20
		see self
	}]
	see mylist 
	class mytest
		x = 15
		func init 
			return self	# Return by reference
Output
	x: 15.000000
	x: 20.000000
	x: 20.000000
.. index:: 
	pair: What is new in Ring 1.3?; Using '<' and ':' operators as 'from' keyword
Using '<' and ':' operators as 'from' keyword
=============================================
In this release of the Ring language we can use the '<' and ':' operators as the 'from' keyword
Syntax (1):
	
	class Cat from Animal
Syntax (2):
	
	
	class Cat < Animal
Syntax (3):
	
	class Cat : Animal
.. index:: 
	pair: What is new in Ring 1.3?; Embedding Ring in Ring without sharing the State
Embedding Ring in Ring without sharing the State
================================================
From Ring 1.0 we already have functions for embedding Ring in the C language. Also we
can execute Ring code inside Ring programs using the eval() function. In this release
we provide functions for embedding Ring in Ring programs without sharing the state.
Advantages:
(1) Quick integration for Ring programs and applications together without conflicts.
(2) Execute and run Ring code in safe environments that we can trace.
Example:
	pState = ring_state_init()
	ring_state_runcode(pState,"See 'Hello, World!'+nl")
	ring_state_runcode(pState,"x = 10")
	pState2 = ring_state_init()
	ring_state_runcode(pState2,"See 'Hello, World!'+nl")
	ring_state_runcode(pState2,"x = 20")
	ring_state_runcode(pState,"see x +nl")
	ring_state_runcode(pState2,"see x +nl")
	v1 = ring_state_findvar(pState,"x")
	v2 = ring_state_findvar(pState2,"x")
	see v1[3] + nl
	see V2[3] + nl
	ring_state_delete(pState)
	ring_state_delete(pState2)
Output:
	Hello, World!
	Hello, World!
	10
	20
	10
	20
.. index:: 
	pair: What is new in Ring 1.3?; RingZip Library
RingZip Library
===============
Ring 1.3 comes with the RingZip library for creating, modifying and extracting *.zip files.
Example (1):  Create myfile.zip contains 4 files 
	load "ziplib.ring"
	oZip = zip_openfile("myfile.zip",'w')
	zip_addfile(oZip,"test.c")
	zip_addfile(oZip,"zip.c")
	zip_addfile(oZip,"zip.h")
	zip_addfile(oZip,"miniz.h")
	zip_close(oZip)
Example (2): Extract myfile.zip to myfolder folder.
	load "ziplib.ring"
	zip_extract_allfiles("myfile.zip","myfolder")
Example (3): Print file names in the myfile.zip
	load "ziplib.ring"
	oZip = zip_openfile("myfile.zip",'r')
	for x=1 to zip_filescount(oZip)
	       see zip_getfilenamebyindex(oZip,x) + nl
	next
	zip_close(oZip)
Example (4) : Using Classes instead of Functions
	load "ziplib.ring"
	new Zip {
		SetFileName("myfile.zip")
		Open("w")
		AddFile("test.c")
		AddFile("zip.c")
		AddFile("zip.h")
		AddFile("miniz.h")
		Close()
	}
.. index:: 
	pair: What is new in Ring 1.3?; Form Designer
Form Designer
=============
Ring 1.3 comes with the Form Designer to quickly design your GUI application windows/forms
and generate the Ring source code.
It's written in Ring (Around 8000 Lines of code) using Object-Oriented Programming and
Meta-Programming.
We can run the From Designer from Ring Notepad
.. image:: rnotefd.png
	:alt: Form Designer - Inside Ring Notepad
Also we can run the Form Designer in another window.
.. image:: formdesigner.png
	:alt: Form Designer


* Size : 9 MB (exe) compressed using 7zip
.. code-block:: ring

	myobj = new Start
	myobj.one()
	     .two()
	     .three()
	     .go()
	class start
		func one
			? "One"
			return new one
	class one 
		func two
			? "Two"
			return new two
	class two
		func three
			? "Three"
			return new three
	class three
		func go
			? "Go!"
Output:
	One
	Two
	Three
	Go!
.. index:: 
	pair: What is new in Ring 1.16?; Code Runner Extension support Ring
Code Runner Extension support Ring
==================================
If you are using Microsoft Visual Studio Code, We have good news for you!
The Code Runner Extension added support for the Ring programming language
.. image:: coderunner.png
	:alt: coderunner
After installing Code Runner
It's recommended to modify this file : 
C:/Users/<USER>/.vscode/extensions/formulahendry.code-runner-0.11.6/package.json
Set the property (code-runner.fileDirectoryAsCwd) to (True)
So Code Runner can move to the file directory when we run it using (Ctrl+Alt+N)
.. code-block:: none
	"code-runner.fileDirectoryAsCwd": {
		"type": "boolean",
		"default": true,
		"description": "Whether to use the directory of the file to be executed as the working directory.",
		"scope": "resource"
	},
.. tip:: Check ring/tools/editors/vscode folder to support Ring in VSCode
.. image:: tetrisvscode.png
	:alt: tetrisvscode
.. index:: 
	pair: What is new in Ring 1.16?; Zero and Strings
Zero and Strings
================
From Ring 1.0, the language do implicit conversion between numbers and strings
This is useful when we mix them in some situations like printing something on the screen
	x = 10		# Number
	? "x = " + x 	# x converted from Number to String 
Also we can do arithmetic operations
	x = "10"	# String
	? 5 + x		# x converted from String to Number
The question is What happens if x content is not a number?
The answer : The result of the conversion will be (Zero)
	x = "Test"	# String - The content is not a number
	? 5 + x		# print (5) - x converted from String to Number (Zero)
The other operators like "=" and "!=" do the conversion too
Starting from Ring 1.16, They will be careful when we compare things to Zero
Example: 
	x = "Test"
	? 0 = x		# The result will be FALSE
	? 0 != x	# The result will be TRUE
This is useful when we compare between values inside Empty Lists and Strings 
	aList = list(10)	# 10 items - Each item is Zero
	? aList[1] + 5		# print (5)
	? aList[1] = "Test"	# False
	? aList[1] = 0		# True
The other values (Not Zero) will follow the normal conversion rules
	x = "5"
	? 5  = x	# True
	? 6 != x	# True
.. index:: 
	pair: What is new in Ring 1.16?; Better Installation Scripts
Better Installation Scripts
===========================
Ring 1.16 comes with better installation scripts on Linux and macOS


* Before defining any variable, Ring try to find the variable and use it if it's found.
.. code-block:: ring

	new point  
	class point 
		x=10 y=20 z=30
		print()
		func print
			new UI {
				display(this.x,this.y,this.z)
			}
	Class UI
		func display x,y,z
			see x + nl + y + nl + z + nl
.. index:: 
	pair: Object Oriented Programming; Using This in the class region as Self
Using This in the class region as Self
======================================
The class region is the region that comes after the class name and before any method.
We can use This in the class region as Self.
Example:
	func main
		o1 = new program {
			test()
		}
		? o1
	class program 
		this.name = "My Application"
		this.version = "1.0"
		? name ? version	
		func test
			? "Name    = " + name 
			? "Version = " + version
Output
.. code-block:: none
	My Application
	1.0
	Name    = My Application
	Version = 1.0
	name: My Application
	version: 1.0
.. note:: When we use braces to change the current active object, Using This we can still point to the class.
.. tip:: The difference between This and Self is that Self point to the current active object that we can change using braces.
Remember that in most cases we don't need to use This or Self in the class region
We can write
	class program name version
Or 
	class program name="My Application" version="1.0"
.. note:: We use This or Self in the class region just to avoid conflict with global variables that are defined with the same name.
.. index:: 
	pair: Object Oriented Programming; Default value for object attributes
Default value for object attributes
===================================
The default value for object attributes is NULL
In Ring, the NULL value is just an empty string or a string that contains "NULL"
We can check for NULL values using the isNULL() function
Example:
	oProgram = new Program
	? oProgram.name
	? oProgram.version
	? isNULL(oProgram.name)
	? isNULL(oProgram.version)
	oProgram { name="My Application" version="1.0" }
	? isNULL(oProgram.name)
	? isNULL(oProgram.version)
	? oProgram
	class program
		name 
		version
Output:
.. code-block:: none
	NULL
	NULL
	1
	1
	0
	0
	name: My Application
	version: 1.0
.. index:: 
	pair: Object Oriented Programming; New From
Command: New From
=================
Using (new) we can create a new object from a specific class
Using (new from) we provide a variable which contains the class name
Example:
	cClassName = "myclass2"
	myobj = new from cClassName
	cClassName = "myclass"
	myobj = new from cClassName
	class myclass
		? :hello
	class myclass2
		? :wow
Output:
.. code-block::
	wow
	hello
.. index:: 
	pair: Object Oriented Programming; Using Objects During Definition
Using Objects During Definition
===============================
Starting from Ring 1.19, The language provides better support for using objects during definition
where we can mix between this feature and other features like operator overloading without missing the output
Example:
.. image:: usingobjdurdef.png
	:alt: usingobjdurdef


* The programmer have full control on when to delete the variable from the memory using the Assignment statement.
.. code-block:: ring

	aList = [1,2,3,4,5]
	aList = "nice"
			
After the second line directly, The list [1,2,3,4,5] will be deleted from the memory and we will have a string "nice" 


* ring/samples/UsingQt/Painter/test3.ring
.. code-block:: ring

	decimals(3)
	t1=clock()
	for t=1 to 1_000_000 r=max(t,t*2) next
	t2 = clock()
	? r
	? (t2-t1)/clockspersecond()
Output:
	2000000
	0.073
Results:
.. code-block:: none
	Time using Ring 1.21	--> 73 ms
	Time using Python 3.11	--> 83 ms
	Time using VFP 9.0 SP2	--> 94 ms
	Time using Python 2.7	--> 108 ms
	Time using Harbour 3.2	--> 110 ms
	Time using Ring 1.20	--> 244 ms
.. index:: 
	pair: What is new in Ring  1.21; Faster Compiler
Faster Compiler
===============
This release provides better performance when compiling large projects
These projects could have huge number of classes and methods
For example, PWCT2 compile time is reduced from 1100 ms to 790 ms 
Also, SoftanzaLib compile time is reduced from five seconds to one second
.. index:: 
	pair: What is new in Ring  1.21; Reducing Memory Usage
Reducing Memory Usage
=====================
We did the next updates to reduce the memory usage
(1) The ByteCode registers count is reduced from four to two
(2) Setter/Getter/OperatorOverloading - Don't use ring_vm_eval()
(3) Using GC functions when using strings in the VM stack
(4) Using the VM Stack when passing arguments to C functions
(5) Reducing memory required by RING_API_REGISTER to support C functions
(6) The pre-allocated memory pool items count is reduced from 1M to 100K
.. index:: 
	pair: What is new in Ring  1.21; ForEach Keyword
ForEach Keyword
===============
This release support using the ForEach keyword in For-in loops
Example:
	aList = 1:10
	ForEach x in aList
		? x
	Next
.. index:: 
	pair: What is new in Ring  1.21; NumOrZero() Function
NumOrZero() Function
====================
This is a new function added to stdlibcore.ring
Using this function we get a number as output (No runtime errors)
Example:
	load "stdlibcore.ring"
	? numorzero(10)
	? numorzero("10")
	? numorzero("10.2")
	? numorzero("10.2 abc")
	? numorzero("What")
	? numorzero([10])
	? numorzero(new point)
	class point
Output:
.. code-block:: none
	10
	10
	10.20
	0
	0
	0
	0
.. index:: 
	pair: What is new in Ring  1.21; Better Operator Overloading
Better Operator Overloading
===========================
We support operator overloading from the first release of the Ring language
When using an object inside an expression and this object define the operator method, this
method will be called if the object comes first.
.. code-block:: none
	object operator value
	myobj + 10
	myobj + "test"
	myobj + [1,2,3]
In this release we support that the value could come first before the object and the operator() 
method will be called but the letter 'r' will comes before the operator (i.e. r+ instead of +)
Example:
	? f(2)
	mylist = new List([1,2,3])
	f(mylist).print()
	func f x
		return 2+x*x	# Here 2 comes before x and x could be an object 
	class List
	aList = []
	func init vValue
		aList = vValue
	func operator cOperator,vValue
		if cOperator = "r+"  
			cOperator = "+"
		ok
		switch cOperator 
			on "+"
				if isNumber(vValue) { 
					for t in aList
						t += vValue
					next
				but isObject(vValue)
					for t = 1 to len(aList)
						aList[t] += vValue[t]
					next
				ok
			on "*"
				if isNumber(vValue) { 
					for t in aList 
						t *= vValue
					next
				but isObject(vValue)
					for t = 1 to len(aList)
						aList[t] *= vValue[t]
					next
				ok
			on "[]"
				return aList[vValue]
			on "len"
				return len(aList)
		off
		return self
	func print
		? aList
Output:
.. code-block:: none
	6
	3
	6
	11
.. note:: the numbers(3,6,11) are the result of applying the function f to the list items [1,2,3]
.. index:: 
	pair: What is new in Ring  1.21; Syntax Highlighting for Vim/nano
Syntax Highlighting for Vim/nano
================================
Check the folders: ring/tools/editors/vim and ring/tools/editors/nano
.. image:: nano.png
	:alt: nano
.. index:: 
	pair: What is new in Ring  1.21; New VM Instructions
New VM Instructions
===================
The next instructions are added to the Ring Virtual Machine


* Using the Load command, We can determine which translation file to use. 
.. code-block:: ring

	class selobjectsController from windowsControllerParent
		oView = new selobjectsView  {
			ListObjects.setselectionmode(QAbstractItemView_MultiSelection)
			win.setwindowmodality(2)
			# Translation
				win.setWindowTitle(T_FORMDESIGNER_SELOBJECTS_TITLE)
				win.setLayoutDirection(T_LAYOUTDIRECTION)
				labelobjects.setText(T_FORMDESIGNER_SELOBJECTS_OBJECTS)
				btnSelect.setText(T_FORMDESIGNER_SELOBJECTS_SELECT)
				btnClose.setText(T_FORMDESIGNER_SELOBJECTS_CLOSE)
		}


* Recursion
.. code-block:: ring

	func <function_name> [parameters]
		Block of statements
.. note:: No keyword is required to end the function definition.
Example:
	func hello
		see "Hello from function" + nl
.. index:: 
	pair: Functions - First Style; Call Functions
Call Functions
==============
To call function without parameters, we type the function name then ()
.. tip:: We can call the function before the function definition and the function code.
Example:
	hello()
	func hello
		see "Hello from function" + nl
Example:
	first()  second()
	func first   see "message from the first function" + nl
	func second  see "message from the second function" + nl
.. index:: 
	pair: Functions - First Style; Declare parameters
Declare parameters
==================
To declare the function parameters, after the function name type the list of parameters as a group
of identifiers separated by comma.
Example:
	func sum x,y
		see x+y+nl
.. index:: 
	pair: Functions - First Style; Send Parameters
Send Parameters
===============
To send parameters to function, type the parameters inside () after the function name
Syntax:
	funcname(parameters)
Example:
	/* output
	** 8
	** 3000
	*/
	sum(3,5) sum(1000,2000)
	func sum x,y see x+y+nl
.. index:: 
	pair: Functions - First Style; Main Function
Main Function
=============
Using the Ring programming language, the Main Function is optional, 
when it's defined, it will be executed after the end of other statements.
if no other statements comes alone, the main function will be the first `entry point <http://en.wikipedia.org/wiki/Entry_point>`_ 
Example:
	# this program will print the hello world message first then execute the main function
	See "Hello World!" + nl
	func main
		see "Message from the main function" + nl
.. index:: 
	pair: Functions - First Style; Variables Scope
Variables Scope
===============
The Ring programming language uses `lexical scoping <http://en.wikipedia.org/wiki/Scope_%28computer_science%29#Lexical_scope_vs._dynamic_scope>`_ to
determine the scope of a variable.
 
Variables defined inside functions (including function parameters) are local variables.
Variables defined outside functions (before any function) are global variables.
Inside any function we can access the variables defined inside this function beside the global variables.
Example:
	# the program will print numbers from 10 to 1
	x = 10 				# x is a global variable.
	func main
		for t = 1 to 10		# t is a local variable
			mycounter()	# call function
		next
	func mycounter
		see x + nl		# print the global variable value
		x--			# decrement
.. note:: Using the main function before the for loop declare the t variable as a local variable,
	  It's recommended to use the main functions instead of typing the instructions directly to set the scope
	  of the new variables to local.
.. index:: 
	pair: Functions - First Style; Return Value
Return Value
============
The function can return a value using the Return command.
Syntax:
	Return [Expression]
.. tip:: the Expression after the return command is optional and we can use the return command
	 to end the function execution without returning any value.
	 
.. note:: if the function doesn't return explicit value, it will return NULL (empty string = "" ).
Example:
	if novalue() = NULL	
		See "the function doesn't return a value" + nl
	ok
	func novalue
.. index:: 
	pair: Functions - First Style; Recursion
Recursion
=========
The Ring programming language support `Recursion <http://en.wikipedia.org/wiki/Recursion_%28computer_science%29>`_
and the function can call itself using different parameters.
Example:
	see fact(5)  	# output = 120
	func fact x if x = 0 return 1 else return x * fact(x-1) ok


* Recursion
.. code-block:: ring

	func <function_name> [parameters] ['{']
		Block of statements
	['}']
Example:
	func hello {
		print("Hello from function \n")
	}
.. index:: 
	pair: Functions - Third Style; Call Functions
Call Functions
==============
To call function without parameters, we type the function name then ()
.. tip:: We can call the function before the function definition and the function code.
Example:
	hello()
	func hello {
		print("Hello from function \n")
	}
Example:
	first()  second()
	func first {  print("message from the first function \n")  }
	func second { print("message from the second function \n") }
.. index:: 
	pair: Functions - Third Style; Declare parameters
Declare parameters
==================
To declare the function parameters, after the function name type the list of parameters as a group
of identifiers separated by comma.
Example:
	func sum(x,y) {
		print(x+y)
	}
.. index:: 
	pair: Functions - Third Style; Send Parameters
Send Parameters
===============
To send parameters to function, type the parameters inside () after the function name
Syntax:
	funcname(parameters)
Example:
	/* output
	** 8
	** 3000
	*/
	sum(3,5) sum(1000,2000)
	func sum(x,y) { print(x+y) } 
.. index:: 
	pair: Functions - Third Style; Main Function
Main Function
=============
Using the Ring programming language, the Main Function is optional, 
when it's defined, it will be executed after the end of other statements.
if no other statements comes alone, the main function will be the first `entry point <http://en.wikipedia.org/wiki/Entry_point>`_ 
Example:
	# this program will print the hello world message first then execute the main function
	print("Hello, World! \n")
	func main {
		print("Message from the main function \n")
	}
.. index:: 
	pair: Functions - Third Style; Variables Scope
Variables Scope
===============
The Ring programming language uses `lexical scoping <http://en.wikipedia.org/wiki/Scope_%28computer_science%29#Lexical_scope_vs._dynamic_scope>`_ to
determine the scope of a variable.
 
Variables defined inside functions (including function parameters) are local variables.
Variables defined outside functions (before any function) are global variables.
Inside any function we can access the variables defined inside this function beside the global variables.
Example:
	# the program will print numbers from 10 to 1
	
	x = 10 				# x is a global variable.
	func main {
		for t = 1 to 10	{	# t is a local variable
			mycounter()	# call function
		}
	}
	func mycounter {
		print("#{x}\n")		# print the global variable value
		x--			# decrement
	}
.. note:: Using the main function before the for loop declare the t variable as a local variable,
	  It's recommended to use the main functions instead of typing the instructions directly to set the scope
	  of the new variables to local.
.. index:: 
	pair: Functions - Third Style; Return Value
Return Value
============
The function can return a value using the Return command.
Syntax:
	Return [Expression]
.. tip:: the Expression after the return command is optional and we can use the return command
	 to end the function execution without returning any value.
	 
.. note:: if the function doesn't return explicit value, it will return NULL (empty string = "" ).
Example:
	if novalue() = NULL {
		print("the function doesn't return a value\n")
	}
	func novalue { }
.. index:: 
	pair: Functions - Third Style; Recursion
Recursion
=========
The Ring programming language support `Recursion <http://en.wikipedia.org/wiki/Recursion_%28computer_science%29>`_
and the function can call itself using different parameters.
Example:
	print( fact(5) )  	# output = 120
	func fact(x) { if x = 0 { return 1 else return x * fact(x-1) } }


* isxdigit()
.. code-block:: ring

	IsAlNum(value) ---> 1 if the value is digit/letter or 0 if not	
Example:
	see isalnum("Hello") + nl +	# print 1
	    isalnum("123456") + nl +	# print 1
	    isalnum("ABCabc123") + nl +	# print 1
	    isalnum("How are you")  	# print 0 because of spaces
.. index:: 
	pair: Data Type; IsAlpha()
IsAlpha() Function
==================
We can test a character or a string using the IsAlpha() Function
Syntax:
	IsAlpha(value) ---> 1 if the value is a letter or 0 if not	
Example:
	see isalpha("Hello") + nl +	# print 1
	    isalpha("123456") + nl +	# print 0
	    isalpha("ABCabc123") + nl +	# print 0
	    isalpha("How are you")  	# print 0
.. index:: 
	pair: Data Type; IsCntrl()
IsCntrl() Function
==================
We can test a character or a string using the IsCntrl() Function
Syntax:
	IsCntrl(value) ---> 1 if the value is a control character (no printing position) 
			    or 0 if not	
Example:
	See iscntrl("hello") + nl +	# print 0 
	    iscntrl(nl)			# print 1
.. index:: 
	pair: Data Type; IsDigit()
IsDigit() Function
==================
We can test a character or a string using the IsDigit() Function
Syntax:
	IsDigit(value) ---> 1 if the value is a digit or 0 if not	
Example:
	see isdigit("0123456789") + nl +	# print 1
	    isdigit("0123a") 			# print 0
.. index:: 
	pair: Data Type; IsGraph()
IsGraph() Function
==================
We can test a character or a string using the IsGraph() Function
Syntax:
	IsGraph(value) ---> 1 if the value can be printed (Except space) or 0 if not	
Example:
	see isgraph("abcdef") + nl +	# print 1
	    isgraph("abc def") 		# print 0
.. index:: 
	pair: Data Type; IsLower()
IsLower() Function 
==================
We can test a character or a string using the IsLower() Function
Syntax:
	IsLower(value) ---> 1 if the value is lowercase letter or 0 if not	
Example:
	see islower("abcDEF") + nl + 	# print 0
	    islower("ghi")		# print 1
.. index:: 
	pair: Data Type; IsPrint()
IsPrint() Function
==================
We can test a character or a string using the IsPrint() Function
Syntax:
	IsPrint(value) ---> 1 if the value occupies a printing position or 0 if not	
Example:
	see isprint("Hello") + nl + 		# print 1
	    isprint("Nice to see you") + nl +   # print 1
	    isprint(nl)				# print 0
.. index:: 
	pair: Data Type; IsPunct()
IsPunct() Function
==================
We can test a character or a string using the IsPunct() Function
Syntax:
	IsPunct(value) ---> 1 if the value is a punctuation character or 0 if not	
Example:
	see ispunct("hello") + nl +	# print 0
	    ispunct(",") 		# print 1
.. index:: 
	pair: Data Type; IsSpace()
IsSpace() Function
==================
We can test a character or a string using the IsSpace() Function
Syntax:
	IsSpace(value) ---> 1 if the value is a white-space or 0 if not	
Example:
	see isspace("   ") + nl +	# print 1
	    isspace("test") 		# print 0
.. index:: 
	pair: Data Type; IsUpper()
IsUpper() Function
==================
We can test a character or a string using the IsUpper() Function
Syntax:
	IsUpper(value) ---> 1 if the value is an uppercase alphabetic letter or 0 if not	
Example:
	see isupper("welcome") + nl +	 # print 0 
	    isupper("WELCOME") 		 # print 1
.. index:: 
	pair: Data Type; IsXdigit()
IsXdigit() Function
===================
We can test a character or a string using the IsXdigit() Function
Syntax:
	IsXdigit(value) ---> 1 if the value is a hexadecimal digit character or 0 if not	
Example:
	see isxdigit("0123456789abcdef") + nl +  # print 1
	    isxdigit("123z") 			 # print 0
.. index:: 
	pair: Data Type; Conversion
Conversion
==========
The next functions can be used for conversion


* While Loop
.. code-block:: ring

	while Expression
		Block of statements
	end
Example:
	While True
		See " 
			Main Menu
			---------
			(1) Say Hello
			(2) About
			(3) Exit
		    " Give nOption
		Switch nOption
		On 1 
			See "Enter your name : " 
			Give name 
			See "Hello " + name + nl
		On 2 
			See "Sample : using while loop" + nl
		On 3 
			Bye
		Other 
			See "bad option..." + nl
		Off
	End
.. index:: 
	pair: Control Structures - First Style; For Loop


* TreeWidget
.. code-block:: ring

	load "guilib.ring"
	import System.GUI
This doesn't have any effect on our previous code, It's just another choice for better code that is consistent with Ring rules.
Also the form designer is updated to provide us the choice between using classes where (index start from 0) or (index start from 1)
Example  (Uses the Form Designer)
(1) https://github.com/ring-lang/ring/blob/master/samples/UsingFormDesigner/indexstart/indexstartView.ring
(2) https://github.com/ring-lang/ring/blob/master/samples/UsingFormDesigner/indexstart/indexstartController.ring 
.. index:: 
	pair: What is new in Ring 1.3?; Better Ring Notepad
Better Ring Notepad
===================
(1) Using QPlainTextEdit instead of QTextEdit
(2) Displaying the line number for each line in the source code file.
Screen Shot:
.. image:: rnotelinenumber.png
	:alt: Ring Notepad - Line Number
(3) Auto-Complete for Ring functions names, classes and words in the opened file.
.. image:: autocomplete.png
	:alt: Ring Notepad - Auto Complete
(4) Functions and Methods List
.. image:: functionslist.png
	:alt: Ring Notepad - Functions List
(5) Output Window
.. image:: outputwin.png
	:alt: Ring Notepad - Output Window
(6) Classes List
.. image:: classeslist.png
	:alt: Ring Notepad - Classes List
(7) Change the Current Style
.. image:: rnotestylemenu.png
	:alt: Ring Notepad - Style Menu
.. index:: 
	pair: What is new in Ring 1.3?; Ring mode for Emacs Editor
Ring mode for Emacs Editor
==========================
Ring 1.3 comes with Ring mode for Emacs Editor
Screen Shot:
.. image:: ringemacs.png
	:alt: Ring mode for Emacs Editor
.. index:: 
	pair: What is new in Ring 1.3?; Better StdLib
Better StdLib
=============
The StdLib is updated to include the next functions


* The load command is executed in the parsing phase (after the scanner phase).
.. code-block:: ring

	LoadSyntax	"syntaxfile.ring"
Example:
File : StyleBasicOn.ring
	ChangeRingKeyword 	see 	print
	ChangeRingKeyword 	ok 	endif
	ChangeRingKeyword 	next 	endfor
	ChangeRingKeyword 	end 	endwhile
File : StyleBasicOff.ring
	ChangeRingKeyword  print 	see
	ChangeRingKeyword  endif 	ok
	ChangeRingKeyword  endfor 	next
	ChangeRingKeyword  endwhile 	end
File : UseStyleBasic.ring
	LoadSyntax "stylebasicon.ring"
	x = 10
	while x > 0
		print "x = " + x + nl
		for t = 1 to 10
			if t = 3
				print "number three" + nl
			endif
		endfor	
		x--
	endwhile
	LoadSyntax "stylebasicoff.ring"
	see "done" + nl
.. note:: files called by the LoadSyntax command must contains ChangeRingKeyword and ChangeRingOperator commands only. 
.. tip:: files called by the LoadSyntax command doesn't support functions, packages and classes. just imperative commands only.
.. note:: Using this feature you can create many styles that you can use in the same project and you can support Ring translation to other languages like Arabic, French and so on.
.. tip:: The effect of LoadSyntax command is related to the current source code file only.
.. tip:: Using LoadSyntax command is optional, See the (Automatic loading for syntax files) section.
	
Using "()" around the function parameters
=========================================
We can use () around the function parameters (optional).
Example:
	hello()
	sum(3,4)
	func hello()
		see "Hello" + nl
	func sum(x,y)
		see x+y+nl
	
Output:
	Hello
	7
Example:
	myfunc = func x,y { see x + y + nl }
	call myfunc (3,4)
	myfunc2 = func (x,y) { see x+y+nl }
	call myfunc(3,4)
Output:
	7
	7
.. index:: 
	pair: Syntax Flexibility; Using Semi-colon after and between statements
Using Semi-colon after and between statements
=============================================
In Ring we can use semi-colon after and between statements (optional).
Example:
	# Using semi-colon is optional
	see "Hello" + nl ; see "How are you?" + nl  ; see "Welcome to Ring" + nl ;
	one() ; two() ; three() ;
	func one ; see "one" + nl ;
	func two ; see "two" + nl ;
	func three ; see "three" + nl ;
Output:
	Hello
	How are you?
	Welcome to Ring
	one
	two
	three
.. index:: 
	pair: Syntax Flexibility; Using $ and @ in the start of the variable name
Using $ and @ in the start of the variable name
===============================================
You can use any unicode character in the variable name also we can use $ and @ in the name.
This feature may help, for example we can start global variables with $ and the object attributes
with @. 
In other languages like Ruby this is the rule, In the Ring language this is just an option without
any force from the Compiler.
example:
	$global_variable = 5
	new test { hello() }	
	class test
		@instance_variable = 10
		func hello
			local_variable = 15
			see "Global   : " + $global_variable + nl + 
			    "Instance : " + @instance_variable + nl +
			    "Local    : " + local_variable + nl
Output:
	Global   : 5
	Instance : 10
	Local    : 15
.. index:: 
	pair: Syntax Flexibility; Using the 'elseif' keyword as 'but' in if statement
Using the 'elseif' keyword as 'but' in if statement
===================================================
if you don't like the 'but' keyword in if statement
Then you can use the 'elseif' keyword.
Example:
	give x
	if x = 1 see "one"
	elseif x=2 see "two"
	elseif x=3 see "three"
	elseif x=4 see "four"
	else see "other"
	ok
	see nl
.. index:: 
	pair: Syntax Flexibility; Using the 'else' keyword as 'other' in switch statement
Using the 'else' keyword as 'other' in switch statement
=======================================================
if you don't like the 'other' keyword in switch statement
Then you can use the 'else' keyword.
Also you can replace 'else' with 'other' in if statement.
i.e. 'other' keyword is the same as 'else' keyword.
Example:
	x = 1
	switch x
		on 10 
			see "10" + nl
		else 
			see "not 10" + nl
	end
Output:
	not 10
.. index:: 
	pair: Syntax Flexibility; Using the 'end' keyword in different control structures
Using the 'end' keyword in different control structures
=======================================================
We can use the 'end' keyword to close different control structures


* ringvm_calllist() function - Added the parameters count and the line number to the output.
.. code-block:: ring

	new SumRows {
		10 20 30		# 60
		10			# 10
		400 100			# 500
		30 40			# 70
	}
	class SumRows
		nSum     = 0
		nLastRow = 0
		func braceExprEval  value 
			aCallList = ringvm_calllist()
			nLine     = aCallList[len(aCallList)-1][7]
			if nLastRow  = 0      nLastRow = nLine            nSum = value return ok
			if nLastRow != nLine  nLastRow = nLine   ? nSum   nSum = value return ok
			nSum += value 
		func braceEnd 
		
			? nSum 
.. index:: 
	pair: What is new in Ring 1.22?; Better Ring FastPro Extension
Better RingFastPro Extension
============================


* Parameters Count
.. code-block:: ring

	RingVM_CallList() ---> List
Example:
	hello()
	func hello
		test()
	func test
		mylist = ringvm_calllist()
		for t in mylist see t[2] + nl next
Output:
	hello
	test
	ringvm_calllist
.. index:: 
	pair: Low Level Functions; RingVM_FilesList()
ringvm_fileslist() function
===========================
Function return a list of the Ring Files.
Syntax:
	RingVM_FilesList() ---> List
Example:
	load "stdlib.ring"
	see ringvm_fileslist()
Output:
	B:/ring/tests/scripts/fileslist.ring
	B:\ring\bin\stdlib.ring
	eval
	stdlib.ring
	stdlib.rh
	stdclasses.ring
	stdfunctions.ring
	stdbase.ring
	stdstring.ring
	stdlist.ring
	stdstack.ring
	stdqueue.ring
	stdmath.ring
	stddatetime.ring
	stdfile.ring
	stdsystem.ring
	stddebug.ring
	stddatatype.ring
	stdconversion.ring
	stdodbc.ring
	stdmysql.ring
	stdsecurity.ring
	stdinternet.ring
	stdhashtable.ring
	stdtree.ring
.. index:: 
	pair: Low Level Functions; ringvm_settrace()
ringvm_settrace()
=================
The function ringvm_settrace() determine the Trace function name
The trace function is a Ring 
function that will be called for each event
Syntax:
	RingVM_SetTrace(cCode)
.. index:: 
	pair: Low Level Functions; ringvm_tracedata()
ringvm_tracedata()
==================
Inside the function that we will use for tracing events
We can use the ringvm_tracedata() function to get the event
data.
The event data is a list contains the next items


* sendemail(cSMTPServer,cEmail,cPassword,cSender,cReceiver,cCC,cTitle,cContent)
.. code-block:: ring

	Load "stdlib.ring"
	ointernet = new internet
	See "Test the internet Class Methods" + nl
	ointernet { 
		see download("www.ring-lang.sf.net")
	}


* While Loop
.. code-block:: ring

	while Expression
		Block of statements
	end
Example:
	While True
		Put " 
			Main Menu
			---------
			(1) Say Hello
			(2) About
			(3) Exit
		    " Get nOption
		Switch nOption
		Case 1 
			Put "Enter your name : " 
			Get name 
			Put "Hello " + name + nl
		Case 2 
			Put "Sample : using while loop" + nl
		Case 3 
			Bye
		Else 
			Put "bad option..." + nl
		End
	End
.. index:: 
	pair: Control Structures - Second Style; For Loop


* RunString(cString)
.. code-block:: ring

	load "stdlib.ring"
	load "naturallib.ring"
	New NaturalLanguage {
		SetLanguageName(:MyLanguage)
		SetCommandsPath(CurrentDir()+"/../command")
		SetPackageName("MyLanguage.Natural")
		UseCommand(:Hello)
		UseCommand(:Count)
		RunFile("program.txt")
	}
We defined a language called MyLanguage, We have folder for the language commands.
Each command will define a class that belong to the MyLanguage.Natural package.
We will define two commands, Hello and Count.
So we must have two files for defining the commands in the CurrentDir()+"/../command" folder
File: hello.ring
	DefineNaturalCommand.SyntaxIsKeyword([
		:Package = "MyLanguage.Natural",
		:Keyword = :hello, 
		:Function = func {
			See  "Hello, Sir!" + nl + nl
		}
	])
File: count.ring
	DefineNaturalCommand.SyntaxIsKeywordNumberNumber([
		:Package = "MyLanguage.Natural",
		:Keyword = :count, 
		:Function = func {
			if not isattribute(self,:count_times) {
				AddAttribute(self,:count_times)
				Count_Times = 0
			}
			if Expr(1) > Expr(2) { 
				nStep = -1 
			else 
				nStep = 1
			}
			if Count_Times = 0 { 
				see nl+"The Numbers!" + nl 
				Count_Times++
			else 
				see nl + "I will count Again!" +nl 
			}
			for x = Expr(1) to Expr(2) step nStep {
				see nl+x+nl 
			}
			CommandReturn(fabs(Expr(1)-Expr(2))+1)				
		}
	])
.. index:: 
	pair: Using the Natural Library; Defining Commands
Defining Commands
=================
To define new command we can use the DefineNaturalCommand object
This object provides the next methods :-


* To generate functions that wrap structures (create/delete/get structure members)
.. code-block:: ring

	<struct>
	ALLEGRO_COLOR
	ALLEGRO_EVENT { type , keyboard.keycode , mouse.x , mouse.y }
	</struct>
from the previous example we will generate two function to create/delete the structure ALLEGRO_COLOR
Also we will generate two functions to create/delete the structure ALLEGRO_EVENT and four functions
to get the structure ALLEGRO_EVENT members (type, keyboard.keycode, mouse.x, mouse.y).
.. index:: 
	pair: Code Generator; Determine Structure Members Types
Determine Structure Members Types
=================================
You can determine the pointer name before the structure member name.
Example:
.. code-block:: none
	SDL_Surface {flags,SDL_PixelFormat *format,w,h,pitch,void *pixels}
.. index:: 
	pair: Code Generator; Defining Constants
Defining Constants
==================
You can define constants using <constant> and </constant>
The generator will generate the required functions to get the constant values
And will define the constants to be used with the same name in Ring code using *.rh file that
will be generated too.
rh = Ring Header
Example:
	<constant>
	MIX_DEFAULT_FORMAT
	SDL_QUIT
	SDL_BUTTON_LEFT
	SDL_BUTTON_MIDDLE
	SDL_BUTTON_RIGHT
	</constant>
.. note:: You will need to pass the *.rh file name to parsec.ring after the generated source file name.
Example:
	ring ..\codegen\parsec.ring libsdl.cf ring_libsdl.c ring_libsdl.rh
.. index:: 
	pair: Code Generator; Register New Functions
Register New Functions
======================
We can register functions by typing the function prototype between <register> and </register>
We need this feature only when we don't provide the function prototype as input directly where
we need to write the code of this function.
Example:
	<register>
	void al_exit(void)
	</register>
	<code>
	RING_FUNC(ring_al_exit)
	{
		if ( RING_API_PARACOUNT != 0 ) {
			RING_API_ERROR(RING_API_BADPARACOUNT);
			return ;
	}
	exit(0);
	}
	</code>
In the previous example we register the al_exit() function. This function is not part of the Allegro
Library, it's just an extra function that we need to add. Then the code if this function is written
inside <code> and </code>. This function call the exit() function from the C language library.
.. index:: 
	pair: Code Generator; Comments in configuration file
Writing comments in the configuration file
==========================================


* Classes List
.. code-block:: ring

	RingVM_PackagesList() ---> List
Example:
	see ringvm_packageslist()
	package package1
		class class1
	package package2
		class class1
	package package3
		class class1
Output:
	package1
	class1
	11
	0
	00FEF838
	package2
	class1
	17
	0
	00FEF978
	package3
	class1
	23
	0
	00FEFF68
.. index:: 
	pair: Low Level Functions; RingVM_MemoryList()
ringvm_memorylist() function
============================
The Function return a list of Memory Scopes and Variables.
Each List Member is a list contains variables in a different scope.
Each Item in the scope list is a list contains the next items


* FSize()
.. code-block:: ring

	load "stdlib.ring" 
	func main 
		print("Enter your name : ")  	;
		Name = getString() 		;
		print( "Hello : #{Name} ") 	;
		return 				;
.. index:: 
	pair: What is new in Ring 1.4?; Better WebLib
Better WebLib
=============
The web library is updated 


* NofProcessors() Function
.. code-block:: ring

	? NofProcessors()
.. index:: 
	pair: What is new in Ring 1.14?; Better Functions
Better Functions
================


* Declarative Programming on the top of Object-Oriented
.. code-block:: ring

	alist = [new point, new point, new point]	# create list contains three objects 
	alist + [1,2,3]					# add another item to the list
	see "Item 4 is a list contains 3 items" + nl
	see alist[4] 
	add(alist , new point)
	alist + new point
	alist[5] { x = 100 y = 200 z = 300 }
	alist[6] { x = 50 y = 150 z = 250 }
	see "Object inside item 5" + nl
	see alist[5]
	see "Object inside item 6" + nl
	see alist[6]
	class point x y z
Output:
	Item 4 is a list contains 3 items
	1
	2
	3
	Object inside item 5
	x: 100.000000
	y: 200.000000
	z: 300.000000
	Object inside item 6
	x: 50.000000
	y: 150.000000
	z: 250.000000
.. index:: 
	pair: Declarative Programming; Return object by reference
Composition and Returning Objects and Lists by Reference
========================================================
When we use composition and have object as one of the class attributes, when we return that object it will be returned by reference.
if the caller used the assignment operator, another copy of the object will be created.
The caller can avoid using the assignment operator and use the returned reference directly to access the object.
The same is done also if the attribute is a list (not object).
.. note:: Objects and Lists are treated using the same rules. When you pass them to function they are passed by reference, 
	when you return them from functions they are returned by value except if it's an object attribute where a return by reference 
	will be done.
Example:
	o1 = new Container
	myobj = o1.addobj()	# the assignment will create another copy
	myobj.x = 100
	myobj.y = 200
	myobj.z = 300
	see o1.aobjs[1]		# print the object inside the container	
	see myobj		# print the copy
	Class Container
		aObjs = []
		func addobj
			aobjs + new point
			return aobjs[len(aobjs)]	# return object by reference
	Class point 
		x  = 10
		y  = 20
		z  = 30
Output:
	x: 10.000000
	y: 20.000000
	z: 30.000000
	x: 100.000000
	y: 200.000000
	z: 300.000000
Example(2):
	func main
		o1 = new screen  {
			content[point()] { 
				x = 100 
				y = 200
				z = 300		
			}
			content[point()] { 
				x = 50 
				y = 150
				z = 250		
			}
		}
		see o1.content[1]
		see o1.content[2]
	Class Screen
		content = []
		func point
			content + new point
			return len(content)
	Class point 
		x  = 10
		y  = 20
		z  = 30
Output:
	x: 100.000000
	y: 200.000000
	z: 300.000000
	x: 50.000000
	y: 150.000000
	z: 250.000000
Example(3):
	func main
		o1 = New Screen  {
			point() { 		# access the object using reference 
				x = 100 
				y = 200
				z = 300		
			}
			point() { 		# access the object using reference 
				x = 50 
				y = 150
				z = 250		
			}
		}
		see o1.content[1]		
		see o1.content[2]
	Class Screen
		content = []
		func point
			content + new point
			return content[len(content)]	# return the object by reference
	Class point x=10 y=20 z=30
Output:
	x: 100.000000
	y: 200.000000
	z: 300.000000
	x: 50.000000
	y: 150.000000
	z: 250.000000
.. index:: 
	pair: Declarative Programming; executing code after the end of object access
Executing code after the end of object access 
=============================================
We can access an object using { } to use object attributes and methods.
if the object contains a method called BraceEnd(), it will be executed before the end of the object access.
Example: 
	New Point { See "How are you?" + nl }
	Class Point x y z
		func braceend
			see "I'm fine, Thank you!" + nl
Output:
	How are you?
	I'm fine, Thank you!
.. index:: 
	pair: Declarative Programming; Declarative programming on the top of Object-Oriented
Declarative Programming on the top of Object-Oriented
=====================================================
The next features enable us to build and use declarative programming environment using nested structures on the top of object oriented 


* Float/Double 
.. code-block:: ring

	nNum1 = True		# Boolean Value (1) 
	nNum2 = False		# Boolean Value (0)
	nNum3 = 10		# Integer
	nNum4 = -10		# Signed Integer
	nNum5 = 1250.11		# Float/Double
The List type is used instead of


* DOSBox 0.74
.. code-block:: ring

	new points {
		first  { x=10   y=20   z=30   }
		second { x=100  y=200  z=300  }
		third  { x=1000 y=2000 z=3000 }
		print()
	}
	class points
	
		aPoints = []
	
		func braceerror
			aPoints + new point
			return aPoints[len(aPoints)]
	
		func print
			? aPoints
	
	class point x y z
Output:
.. code-block:: none
	x: 10
	y: 20
	z: 30
	x: 100
	y: 200
	z: 300
	x: 1000
	y: 2000
	z: 3000
.. index:: 
	pair: What is new in Ring  1.18; Better RingRayLib
Better RingRayLib
=================
The next functions are added to the extension


* All other values are True
.. code-block:: ring

	? 1 and 1			# 1 (True)
	? "test" and "test"		# 1 (True)
	? [1,2,3] and "test"		# 1 (True)
	? 1 and "test" and [1,2,3]	# 1 (True)
	? 1 and new point		# 1 (True)
	? 1 and 0			# 0 (False)
	? 1 and ""			# 0 (False)
	? 1 and []			# 0 (False)
	? 1 and NULLPointer()		# 0 (False)
	class point 
.. index:: 
	pair: Operators; Mixing Bitwise Operators and Types
Mixing Bitwise Operators and Types
==================================
These operators support numbers. Also, it will automatically convert strings to numbers if this is possible or produce a runtime error if the string can't be converted.
Using these operators with lists or objects produce a runtime error with an exception to this rule.
The exception is using objects that support operator overloading where the object comes first before the operator.
Example (7):
	? 1 & 1			# 1
	? "1" & 1		# 1
	? 1 & "3"		# 1
	? "3" & "3"		# 3
	? "123" & "123"		# 123
.. index:: 
	pair: Operators; Mixing Assignment Operators and Types
Mixing Assignment Operators and Types
=====================================
Using assignment we can assign any value to any variable.
Using += support Strings & Numbers and will produce a runtime error if used with other types
Using other assignment operators like -=, *=, /=, %=, <<=, >>=, etc. support only numbers and will produce a runtime error if used with other types.
Example (8):
	cStr = "one"
	cStr += " two"
	? cStr		# one two
	nNum = 100
	nNum += 200
	? nNum		# 300
.. index:: 
	pair: Operators; Unary Positive and Unary Negative
Unary Positive and Unary Negative
=================================
Rules:


* StdLib Classes
.. code-block:: ring

	Load "libsdl.ring"
	SDL_Init(SDL_INIT_EVERYTHING)
	win = SDL_CreateWindow("Hello World!", 100, 100, 640, 480, SDL_WINDOW_SHOWN)
	SDL_Delay(2000)
	SDL_DestroyWindow(win)
	SDL_Quit()
See the RingLibSDL Chapter.
.. index:: 
	pair: What is new in Ring 1.1?; Game Engine for 2D Games
Demo Project - Game Engine for 2D Games
=======================================
In practice we would create a game engine in a language like C/C++ to get the best performance 
then provide Ring classes to use the engine.
But many 2D Games are simple and creating a game engine in Ring will be fast enough in many cases
Also this would be a good demo project to learn about the language concepts where we build things
using Object Oriented Programming (OOP) then access the power that we have using declarative programming
using nested structures or using natural programming.
In this project we selected the first way (declarative programming using nested structures)
Example:
	Load "gameengine.ring"	# Give Control to the Game Engine
	func main		# Called by the Game Engine
		oGame = New Game	# Create the Game Object
		{
			title = "My First Game"
			text {
				x = 10	y=50
				animate = false
				size = 20
				file = "fonts/pirulen.ttf"
				text = "game development using ring is very fun!"
				color = rgb(0,0,0)	# Color = black	
			}
			text {
				x = 10	y=150
				# Animation Part ======================================
				animate = true				# Use Animation
				direction = GE_DIRECTION_INCVERTICAL	# Increase y
				point = 400	 	# Continue until y=400
				nStep = 3		# Each time y+= 3
				#======================================================
				size = 20
				file = "fonts/pirulen.ttf"
				text = "welcome to the real world!"
				color = rgb(0,0,255)	# Color = Blue 	
			}
			Sound {					# Play Sound
				file = "sound/music1.wav"	# Sound File Name
			}		
		}					# Start the Events Loop	
See the "Demo Project - Game Engine for 2D Games" chapter.
.. index:: 
	pair: What is new in Ring 1.1?; RingSQLite
RingSQLite
==========
Ring 1.0 provided support for ODBC to use any database and provided native support for MySQL.
Now Ring 1.1 provide native support for SQLite database too.
Example:
	oSQLite = sqlite_init()
	sqlite_open(oSQLite,"mytest.db") 
	sql = "CREATE TABLE COMPANY("  +
	         "ID INT PRIMARY KEY     NOT NULL," +
        	 "NAME           TEXT    NOT NULL," +
	         "AGE            INT     NOT NULL," +
        	 "ADDRESS        CHAR(50)," +
	         "SALARY         REAL );"
	sqlite_execute(oSQLite,sql) 
	sql = "INSERT INTO COMPANY (ID,NAME,AGE,ADDRESS,SALARY) "  +
        	 "VALUES (1, 'Mahmoud', 29, 'Jeddah', 20000.00 ); " +
	         "INSERT INTO COMPANY (ID,NAME,AGE,ADDRESS,SALARY) "  +
        	 "VALUES (2, 'Ahmed', 27, 'Jeddah', 15000.00 ); "     +
	         "INSERT INTO COMPANY (ID,NAME,AGE,ADDRESS,SALARY)" +
        	 "VALUES (3, 'Mohammed', 31, 'Egypt', 20000.00 );" +
	         "INSERT INTO COMPANY (ID,NAME,AGE,ADDRESS,SALARY)" +
        	 "VALUES (4, 'Ibrahim', 24, 'Egypt ', 65000.00 );"
	sqlite_execute(oSQLite,sql)
	aResult =  sqlite_execute(oSQLite,"select * from COMPANY") 
	for x in aResult
		for t in x
			see t[2] + nl
		next
	next
	see copy("*",50)  + nl
	for x in aResult
		see x["name"] + nl
	next
	sqlite_close(oSQLite) 
.. index:: 
	pair: What is new in Ring 1.1?; Better Code Generator for Extensions 
Better Code Generator for Extensions 
====================================
We are using the code generator (written in Ring) every day to add new libraries to Ring.
The generator is used to create RingQt and RingAllegro 
Also in Ring 1.1 it's used to create RingLibSDL.
more features are added like 


* SystemCmd()
.. code-block:: ring

	load "stdlib.ring"
	world = "World!"
	mystring = print2str("Hello, #{world} \nIn Year \n#{2000+17} \n")
	see mystring + nl
Output:
	Hello, World!
	In Year
	2017
(2) The ListAllFiles() is a new function added to the StdLib 
Using this function we can quickly do a process on a group of files in a folder and it's sub folders.
Example:
            load "stdlib.ring"
            aList = ListAllFiles("c:/ring/ringlibs","ring") # *.ring only
            aList = sort(aList)
            see aList
Example:
            load "stdlib.ring"
            see listallfiles("b:/ring/ringlibs/weblib","") # All Files
(3) The SystemCmd() is a new function added to the StdLib 
The function will execute a system command like the System() function but 
will return the output in a string.
Example:
	cYou = SystemCmd("whoami")
	See "SystemCmd: whoami ====="+ nl + cYou +nl
Output:
	SystemCmd: whoami =====
	desktop-umberto\umberto
.. index:: 
	pair: What is new in Ring 1.5?; Better WebLib
Better WebLib
=============
The WebLib is updated to include the HTMLPage class
Using this class we can create HTML documents without printing the output to the standard output
So instead of using the WebLib in Web Applications only
We can use it in Console/GUI/Mobile Applications too
Example:
	load "stdlib.ring"
	load "weblib.ring"
	import System.Web
	func main
		mypage = new HtmlPage {
			h1 { text("Customers Report") }
			Table
		        {
        		          style = stylewidth("100%") + stylegradient(4)
                		  TR
	                	  {
        	                	TD { WIDTH="10%" text("Customers Count : " )  }
	                	        TD { text (100) }
		                  }
        		}
	
			Table
        		{
	                	  style = stylewidth("100%") + stylegradient(26)
		                  TR
        		          {
					style = stylewidth("100%") + stylegradient(24)
        	                	TD { text("Name " )  }
	        	                TD { text("Age" ) }
					TD { text("Country" ) }
					TD { text("Job" ) }	
					TD { text("Company" ) }
        		          }
				  for x =  1 to 100
	        	          	TR
                		  	{
                    			    	TD { text("Test" )  }
                    				TD { text("30" ) }
						TD { text("Egypt" ) }
						TD { text("Sales" ) }	
						TD { text("Future" ) }
        	          		}
				  next
		        }
		}
		write("report.html",mypage.output())
Using this feature we can create reports quickly using WebLib & GUILib together
Example:
	load "stdlib.ring"
	load "weblib.ring"
	load "guilib.ring"
	import System.Web
	import System.GUI
	new qApp {
		open_window(:CustomersReportController)
		exec()
	}
	class CustomersReportController
		oView = new CustomersReportView
		func Start
			CreateReport()
		func CreateReport
			mypage = new HtmlPage {
				h1 { text("Customers Report") }
				Table
				{
					style = stylewidth("100%") + stylegradient(4)
					TR
					{
						TD { WIDTH="10%" 
							text("Customers Count : " )  }
						TD { text (100) }
					}
				}
				Table
				{
					style = stylewidth("100%") + stylegradient(26)
					TR
					{
						style = stylewidth("100%") +
							stylegradient(24)
						TD { text("Name " )  }
						TD { text("Age" ) }
						TD { text("Country" ) }
						TD { text("Job" ) }	
						TD { text("Company" ) }
					}
					for x =  1 to 100
						TR
						{
							TD { text("Test" )  }
							TD { text("30" ) }
							TD { text("Egypt" ) }
							TD { text("Sales" ) }	
							TD { text("Future" ) }
						}
					next
				}
			}
			write("report.html",mypage.output())
		func PrintEvent
			printer1 = new qPrinter(0) {
				setoutputformat(1)
				setoutputfilename("report.pdf")
			}
			oView {
				web.print(printer1)
				web.show()
			}
			system ("report.pdf")
	class CustomersReportView
			win = new window() {
					setwindowtitle("Report Window")
					setgeometry(100,100,500,500)
					web = new webview(win) {
						setgeometry(100,100,1000,500)
						loadpage(new qurl("file:///"+
						currentdir()+"/report.html"))
					}
					new pushbutton(win) {
							setGeometry(100,20,100,30)
							settext("Print")
							setclickevent(Method(:PrintEvent))
					}
					showMaximized()
				}
Screen Shot:
.. image:: ring15reportshot.png
	:alt: Customers Report
.. index:: 
	pair: What is new in Ring 1.5?; Better RingQt
Better RingQt
=============
New classes added to RingQt :


* @override
.. code-block:: ring

	load "typehints.ring"
	o = new MyNewLib {
		? isGreaterThanTwo(10)
		? isGreaterThanTwo(1)
	}
	class MyLib {
		boolean func isGreaterThanTwo(int x) {
			if x > 2
				return true
			else
				return false
			ok
		}
	}
	class MyNewLib < MyLib {
		@override
		boolean func isGreaterThanTwo(int x) {
			? "Using override"
			return x > 2
		}
	}
Output:
.. code-block:: none
	Using override
	1
	Using override
	0
.. index:: 
	pair: What is new in Ring  1.19; Better RingRayLib
Better RingRayLib
=================
The next functions are added to the RingRayLib extension
	* vec2() --> Vector2
	* vec2set(Vector2 vec,double x,double y)
	* vec2getx() --> double
	* vec2setx(double)
	* vec2gety() --> double
	* vec2sety(double)
	* vec3() --> Vector3
	* vec3set(Vector3 vec,double x,double y,double z)
	* vec3getx() --> double
	* vec3setx(double)
	* vec3gety() --> double
	* vec3sety(double)
	* vec3getz() --> double
	* vec3setz(double)
	* vec4() --> Vector4
	* vec4set(Vector4 vec,double x,double y,double z,double w)
	* vec4getx() --> double
	* vec4setx(double)
	* vec4gety() --> double
	* vec4sety(double)
	* vec4getz() --> double
	* vec4setz(double)
	* vec4getw() --> double
	* vec4setw(double)
	* getcamera3dposx() --> double
	* setcamera3dposx(double)
	* getcamera3dposy() --> double
	* setcamera3dposy(double)
	* getcamera3dposz() --> double
	* setcamera3dposz(double)
	* getcamera3dtarx() --> double
	* setcamera3dtarx(double)
	* getcamera3dtary() --> double
	* setcamera3dtary(double)
	* getcamera3dtarz() --> double
	* setcamera3dtarz(double)
	* getcamera3dupx() --> double
	* setcamera3dupx(double)
	* getcamera3dupy() --> double
	* setcamera3dupy(double)
	* getcamera3dupz() --> double
	* setcamera3dupz(double)
	* getcamera3fovy() --> double
	* setcamera3fovy(double)
	* getcamera3type() --> double
	* setcamera3type(double)
.. index:: 
	pair: What is new in Ring  1.19; Better RingStbImage
Better RingStbImage
===================
The extension is improved to support the next features


* To ask from the code generator to execute Ring code during reading the configuration file, just 
.. code-block:: ring

	<runcode>
	aNumberTypes + "al_fixed"
	</runcode>
The previous line of code add the string "al_fixed" to the list aNumberTypes, This list contains 
types that can be considered as numbers when the code generator find it in the function prototype.
.. index:: 
	pair: Code Generator; Enum and Numbers
Enum and Numbers
================
We have the list aEnumTypes to use for adding each Enumeration we uses in the functions prototype.
Example:
	<runcode>
	aNumberTypes + "qreal"
	aNumberTypes + "qint64"
	aEnumTypes + "Qt::GestureType"
	aEnumTypes + "Qt::GestureFlag"
	</runcode>
.. index:: 
	pair: Code Generator; Filtering using Expressions
Filtering using Expressions
===========================
using <filter> and </filter> we can include/exclude parts of the configuration file
based on a condition, for example 
		<filter> iswindows() 
			... functions related to windows
		</filter>
.. index:: 
	pair: Code Generator; Constants Type
Constants Type
==============
The default type for constant is Number 
But Some constants may be another type, for example (pointer : void *)
before using <constant> and </constant> we can use <runcode> and </runcode>
to determine the constant type using two global variables used by the code generator.
The first variable is $nDefaultConstantType which can be


* Switch Statement
.. code-block:: ring

	switch Expression
	case Expression
		Block of statements
	else
		Block of statements
	end		
Example:
	Put " 
		Main Menu
		---------
		(1) Say Hello
		(2) About
		(3) Exit
	    " Get nOption
	Switch nOption
	Case 1 Put "Enter your name : " Get name Put "Hello " + name + nl
	Case 2 Put "Sample : using switch statement" + nl
	Case 3 Bye
	Else Put "bad option..." + nl
	End
.. index:: 
	pair: Control Structures - Second Style; Looping
Looping
=======


* Using unary negative (-) before a list/object will produce a runtime error.
.. code-block:: ring

	x = +10
	? x		# 10
	? +x		# 10
	y = "10"
	? +y		# 10	
	? type(+y)	# STRING
	x = 10
	? -x		# -10
	y = "10"
	? -y		# -10
	? type(-y)	# NUMBER
	aList = [1,2,3]
	? - aList	# RUNTIME ERROR	
 


* Functions and Classes written in C/C++ languages
.. code-block:: ring

	func main
		o1 = new myclass { test() test2() }
		test2()
	func f1
		see "f1 function" + nl
	func f2 
		see "f2 function" + nl
	func f3 
		see "f3 function" + nl
	func test2
		myline()
		see "test2 function" + nl
		new myclass {
			f1()
			f2()
			f3()
			self.f3()
		}	
		myobj = new myclass
		myobj.f3()
		myline()
	func myline
		see copy("=",40) + nl
	Class myclass
		func test
			myline()
			see "test method" + nl
			f1()
			f2()
			f3()
			myline()
		func f3
			see "f3 method" + nl
		func test2
			myline()
			see "test2 method" + nl
			self {
				f1()
				f2()
				f3()
			}
			myline()
Output:
.. code-block:: none
	========================================
	test method
	f1 function
	f2 function
	f3 method
	========================================
	========================================
	test2 method
	f1 function
	f2 function
	f3 method
	========================================
	========================================
	test2 function
	f1 function
	f2 function
	f3 method
	f3 method
	f3 method
	========================================
.. index::
	pair: Scope Rules for Functions and Methods; Calling a function sharing the name with a method in the current class
Calling a function sharing the name with a method in the current class
======================================================================
In the previous example we have a function called f3() and we have a method called f3() 
How we can call the f3() function from the test() method ?
Solution (1) : Change the current object scope to another object scope
In this solution we will have an empty class called local that we will use to change the current object scope.
	func main
		o1 = new myclass { test()}
	func f1
		see "f1 function" + nl
	func f2 
		see "f2 function" + nl
	func f3 
		see "f3 function" + nl
	func myline
		see copy("=",40) + nl
	Class myclass
		func test
			myline()
			see "test method" + nl
			f1()
			f2()
			f3()		   # call f3() method	
			new local { f3() } # call f3() function 
			myline()
		func f3
			see "f3 method" + nl
	class local
Output:
.. code-block:: none
	========================================
	test method
	f1 function
	f2 function
	f3 method
	f3 function
	========================================


* Using 'endpackage'/'endclass'/'endfunc' keywords after Packages/Classes/Functions
.. code-block:: ring

	import mypackage
	new myclass { myfunc() }
	package mypackage
		class myclass
			func myfunc			
				see "welcome"  + nl
			endfunc
		endclass
	endpackage
.. index:: 
	pair: What is new in Ring 1.5?; Type Hints Library
Type Hints Library
==================
Ring 1.5 comes with the Type Hints library
Using this library we can add the type information to the source code which will be 
very useful for tools like


* Using braces { } in Packages/Classes/Functions
.. code-block:: ring

	load "stdlib.ring"
	import mypackage
	new myclass {
		myfunc() 
	}
	package mypackage 
	{
		class myclass 
		{
			func myfunc 
			{
				print("Hello, World!\n")
			}
		}
	}


* If Statement
.. code-block:: ring

	if Expression {
		Block of statements
	elseif Expression
		Block of statements
	else
		Block of statements
	}
Example:
	print(" 
		Main Menu
		---------
		(1) Say Hello
		(2) About
		(3) Exit
	    ")
	nOption = getnumber()
	if nOption = 1	{
		print("Enter your name : ") 
		name = getstring() 	
		print("Hello #{name}\n")
	elseif nOption = 2 
		print("Sample : using if statement\n")
	elseif nOption = 3 
		bye
	else 
		print("bad option...\n")
	}
.. index:: 
	pair: Control Structures - Third Style; Switch Statement


* Transform Substring To Another Substring 
.. code-block:: ring

	substr(string,substring)  ---> the starting position of substring in string
Example:
	cStr = "Welcome to the Ring programming language"
	see substr(cStr,"Ring")		# print 16
.. index:: 
	pair: Strings; Get Substring from position to end
Get substring from position to end
==================================
Syntax:
	substr(string,position)  ---> Get substring starting from position to end
Example:
	cStr = "Welcome to the Ring programming language"
	nPos = substr(cStr,"Ring")	# nPos = 16
	see substr(cStr,nPos)		# print Ring programming language
.. index:: 
	pair: Strings; Get Number of Characters from position
Get Number of Characters From Position
======================================
Syntax:
	substr(string,position,count)  ---> Get characters starting from position
Example:
	cStr = "Welcome to the Ring programming language"
	nPos = substr(cStr,"Ring")	# nPos = 16
	see substr(cStr,nPos,4)		# print Ring 
.. index:: 
	pair: Strings; Transform Substring To Another Substring
Transform Substring To Another Substring
========================================
Syntax:
	substr(string,substring,newsubstring)  ---> Transformed string (Match case)
	substr(string,substring,newsubstring,1)  ---> Transformed string (Ignore case)
Example:
	cStr = "Welcome to the New programming language"
	see substr(cStr,"New","Ring") + nl  # print Welcome to the Ring programming language 
	see substr(cStr,"new","Ring",1)+ nl # print Welcome to the Ring programming language 
.. index:: 
	pair: Strings; strcmp()
strcmp() Function
=================
In Ring we can use the = operator to compare between strings
Also, we can compare between two strings using the strcmp() function.
Syntax:
	strcmp(cString1,cString2) ---> value = 0 if cString1 = cString2
				       value < 0 if cString1 < cString2
				       value > 0 if cString1 > cString2
Example:
	see strcmp("hello","hello") + nl +
  	    strcmp("abc","bcd") + nl + 
  	    strcmp("bcd","abc") + nl
Output:
	0
	-1
	1
.. index:: 
	pair: Strings; Reverse() Function
Reverse() Function
==================
Using the Reverse() function we can reverse the string characters
.. note:: This functions support lists too
Syntax:
	Reverse(cString) ---> cReversedString 
	cStr = "Welcome to Ring"
	? reverse(cStr)			# gniR ot emocleW
.. index:: 
	pair: Strings; str2list() and list2str()
str2list() and list2str() Functions
===================================
We can convert string lines to list items using the str2list() function.
Also we can convert the list to a string using list2str() function.
Syntax:
	str2list(cString) 		---> list contains the string lines 
	list2str(aList)   		---> string contains the list items
	list2str(aList,[nStart],[nEnd]) ---> string contains the list items from nStart to nEnd
Example:
	/* output:
	** Items : 4
	** Item : Hello
	** Item : How are you ?
	** Item : are you fine ?
	** Item : ok
	** list2Str result = Hello
	** How are you ?
	** are you fine ?
	** ok
	** Done
	*/
	mystr = "Hello
	How are you ?
	are you fine ?
	ok"
	mylist = str2list(mystr)
	see "Items : " + len(mylist) + nl
	for x in mylist
		see "Item : " + x + nl
	next
	newstr = list2str(mylist)
	see "list2Str result = " + newstr
	if mystr = newstr
		see nl + "Done"
	else
		see nl + "Error!"
	ok
Example:
	aList = 1:10
	cStr  = list2str(aList,6,10)
	? cStr				# 6 7 8 9 10
			
.. index:: 
	pair: Strings; Merge binary characters
Merge binary characters
=======================
From Ring 1.0 we can create binary strings and do operations on these strings.
Starting from Ring 1.8, we can get individual characters from these strings and merge them together using
the '+' operator.
Example:
	cStr = "Welcome"
	? cstr[1] + cstr[2] + cStr[5]
	v = cstr[1] + cstr[2] + cStr[5]
	? v
	? len(v)
	c1 = cStr[1]
	? c1
	aList = [1,2,3]
	cStr = ""
	for item in aList 
		cStr += int2bytes(item)
	next 
	? "All String"
	? len(cStr)
	? "First Part"
	n1 = cStr[1] + cStr[2] + cStr[3] + cStr[4]
	? len(n1)
	? "Second Part"
	n2 = cStr[5] + cStr[6] + cStr[7] + cStr[8]
	? len(n2)
	? "Third Part"
	n3 = cStr[9] + cStr[10] + cStr[11] + cStr[12]
	? len(n3)
	? "All String"
	cString = cStr[1] + cStr[2] + cStr[3] + cStr[4] + 
		  cStr[5] + cStr[6] + cStr[7] + cStr[8] + 
		  cStr[9] + cStr[10] + cStr[11] + cStr[12]
	? len(cString)
	? ascii(cStr[1])
	? len(cStr[2])
Output:
	Weo
	Weo
	3
	W
	All String
	12
	First Part
	4
	Second Part
	4	
	Third Part
	4
	All String
	12
	1
	1
	


* isnull()
.. code-block:: ring

	IsString(value) ---> 1 if the value is a string or 0 if not	
Example:
	see isstring(5) + nl +		# print 0
	    isstring("hello") + nl	# print 1
.. index:: 
	pair: Data Type; IsNumber()
IsNumber() Function
===================
Using the IsNumber() function we can know if the value is a number or not
Syntax:
	IsNumber(value) ---> 1 if the value is a number or 0 if not	
Example:
	see isnumber(5) + nl +		# print 1
	    isnumber("hello") + nl	# print 0
.. index:: 
	pair: Data Type; IsList()
IsList() Function
===================
Using the IsList() function we can know if the value is a list or not
Syntax:
	IsList(value) ---> 1 if the value is a list or 0 if not	
Example:
	see islist(5) + nl +		# print 0
	    islist("hello") + nl +	# print 0
	    islist([1,3,5]) 		# print 1
	
.. index:: 
	pair: Data Type; Type()
Type() Function
===============
We can know the type of a value using the Type() Function.
Syntax:
	Type(value) ---> The Type as String 
Example:
	    see Type(5) + nl +		# print NUMBER
	    Type("hello") + nl +	# print STRING
	    Type([1,3,5]) 		# print LIST
.. index:: 
	pair: Data Type; IsNULL()
IsNULL() Function
=================
We can check the value to know if it's null or not using the IsNULL() function
Syntax:
	IsNULL(value) ---> 1 if the value is NULL or 0 if not	
Example:
	    see isnull(5) + nl +	# print 0
	    isnull("hello") + nl +	# print 0
	    isnull([1,3,5]) + nl +	# print 0
	    isnull("") + nl +		# print 1
	    isnull("NULL")		# print 1
.. index:: 
	pair: Data Type; Check Character
Check Character
===============
The next functions can be used to check character


* To generate code directly type it between <code> and </code>
.. code-block:: ring

	<code>
		/* some C code will be written here */
	</code>
We use this feature when we need to do something without the help of the code generator. for
example including header files and defining constants using Macro.
.. index:: 
	pair: Code Generator; Prefix for Functions Names
Prefix for Functions Names 
==========================


* Ring 1.14 is 3x Faster in programs that have strings with long and fixed size
.. code-block:: ring

	load "openssllib.ring"
	f = fopen(exefilename(),"rb")
	h = SHA256Init()
	while true 
	  s = fread(f, 4096)
	  if isstring(s) 
	    SHA256Update(h, s)
	  else
	    exit
	  ok
	end
	digest = SHA256Final(h)
	fclose(f)
	? digest
Output:
.. code-block:: none
	4e677154639dae3baa048ce5ae0b04b63bcd33316e2d2041297dcee85604d778


* SyntaxIsCommandExpressions(aPara,nCount)
.. code-block:: ring

	load "stdlib.ring"
	load "naturallib.ring"
	MyLanguage = New NaturalLanguage {
		SetLanguageName(:MyLanguage)
		setCommandsPath(CurrentDir()+"/../command")
		SetPackageName("MyLanguage.Natural")
		UseCommand(:Hello)
		UseCommand(:Count)
		UseCommand(:Print)
		UseCommand(:IWantWindow)
		UseCommand(:WindowTitleIs)
		UseCommand(:IWantButton)
	}
Example (1)
In the next example we will define the Print command.
We will use the SyntaxIsKeywordExpression() Method.
We pass list (as Hash) to the method. We determine the package name, the keyword and
the function that will be executed.
Inside this function we uses the Expr(nExprNumber) function to get the expression value that the
user will write after the keyword.
File: print.ring
	DefineNaturalCommand.SyntaxIsKeywordExpression([
		:Package = "MyLanguage.Natural",
		:Keyword = :print, 
		:Function = func {
			See  Expr(1)  
		}
	])
Usage:
	load "mylanguage.ring"
	MyLanguage.RunString('
 		print "Hello, World!"
	')
Output:
.. code-block:: none
	Hello, World!
Example (2)
File: iwantwindow.ring
	DefineNaturalCommand.SyntaxIsCommand([
		:Package = "MyLanguage.Natural",
		:Command = "i want window", 
		:Function = func {
			See  "Command: I want window" + nl
		}
	])
Usage:
	load "mylanguage.ring"
	MyLanguage.RunString('
 		i want window
	')
Output:
.. code-block:: none
	Command: I want window
Example (3)
File: windowtitleis.ring
	DefineNaturalCommand.SyntaxIsCommandString([
		:Package = "MyLanguage.Natural",
		:Command = "window title is", 
		:Function = func {
			See  "Command: Window title is " + Expr(1) + nl
		}	
	])
Usage:
	load "mylanguage.ring"
	MyLanguage.RunString('
 		I want window and the window title is "Hello World"
	')
Output:
.. code-block:: none
	Command: I want window
	Command: Window title is Hello World
.. index:: 
	pair: Using the Natural Library; Operators
Natural Library - Operators
===========================
In the next example we uses the Count command without using operators
	load "mylanguage.ring"
	MyLanguage.RunString("
		Hello	
		Count 1 5
		Count 5 1
	")
We can add more description
	load "mylanguage.ring"
	MyLanguage.RunString("
		Hello, Please 	Count from 1 to 5 then count from 5 to 1
	")
Also we can use operators like "(" and ")" around the instruction
	load "mylanguage.ring"
	MyLanguage {
		SetOperators("()")
		RunString("
			Here we will play and will try something
			that looks like Lisp Syntax
			(count  (count 1 5)  (count 20 15))
			Just for fun!
		")
	}
.. index:: 
	pair: Using the Natural Library; Defining commands using classes
Defining commands using classes
===============================
This section is related to the implementation details.
When we define new command, Each command is defined by the Natural Library as a class.
We have the choice to define commands using the simple interface provided by the
DefineNaturalCommand object or by defining new class as in the next examples.
If we used DefineNaturalCommand (More Simple), The class will be defined during the runtime.
File: hello.ring
	Package MyLanguage.Natural
	class Hello
		func AddAttributes_Hello	
			AddAttribute(self,:hello)
		func GetHello   
			See  "Hello, Sir!" + nl + nl
File: count.ring
	Package MyLanguage.Natural
	class Count
           
		func Getcount
			StartCommand()
			CommandData()[:name] = :Count
			CommandData()[:nExpr] = 0
			CommandData()[:aExpr] = []
		func BraceExprEval_Count nValue
			if isCommand() and CommandData()[:name] = :Count {
				if isNumber(nValue) {
					CommandData()[:nExpr]++     
					CommandData()[:aExpr] + nValue
					if CommandData()[:nExpr] = 2 {
						Count_Execute()
					}
				}
			}
		func AddAttributes_Count	
			AddAttribute(self,:count)
	
		func Count_Execute
			if not isattribute(self,:count_times) {
				AddAttribute(self,:count_times)
				Count_Times = 0
			}
			if Expr(1) > Expr(2) { 
				nStep = -1 
			else 
				nStep = 1
			}
			if Count_Times = 0 { 
				see nl+"The Numbers!" + nl 
				Count_Times++
			else 
				see nl + "I will count Again!" +nl 
			}
			for x = Expr(1) to Expr(2) step nStep {
				see nl+x+nl 
			}
			CommandReturn(fabs(Expr(1)-Expr(2))+1)				


* English.ring
.. code-block:: ring

	T_LANGUAGE = "english"
	T_LAYOUTDIRECTION = 0			# Left to Right
	T_FORMDESIGNER_FORMDESIGNER 		= "Form Designer"
	T_FORMDESIGNER_FORMTITLE 		= "Form1"
	T_FORMDESIGNER_FILE 			= "File"
	T_FORMDESIGNER_NEW 			= "New"
	T_FORMDESIGNER_OPEN 			= "Open"
	T_FORMDESIGNER_SAVE 			= "Save"
	T_FORMDESIGNER_SAVEAS 			= "Save As"
	T_FORMDESIGNER_CLOSE 			= "Close"
The form designer source code files will use these constants instead of typing the string literals 
the next section from the formdesigner/mainwindow/formdesignerview.ring
		# Create the Main Window and use the Mdi Area
			win = new qMainwindow() {
				setWindowTitle(T_FORMDESIGNER_FORMDESIGNER) # "Form Designer"
				setcentralWidget(this.oArea)
				setLayoutDirection(T_LAYOUTDIRECTION)
			}


* To generate code that wraps a C function, we just write the C function prototype
.. code-block:: ring

	ALLEGRO_DISPLAY *al_create_display(int w, int h)
	void al_destroy_display(ALLEGRO_DISPLAY *display)
	int al_get_new_display_flags(void)
	void al_set_new_display_flags(int flags)
	int al_get_new_display_option(int option, int *importance)
The previous example will guide the code generator to generate 5 functions that wraps the 
al_create_display(), al_destroy_display(), al_get_new_display_flags(), al_set_new_diplay_flas()
and al_get_new_display_option() functions.
The generated code will be as in the next example
	RING_FUNC(ring_al_create_display)
	{
		if ( RING_API_PARACOUNT != 2 ) {
			RING_API_ERROR(RING_API_MISS2PARA);
			return ;
		}
		if ( ! RING_API_ISNUMBER(1) ) {
			RING_API_ERROR(RING_API_BADPARATYPE);
			return ;
		}
		if ( ! RING_API_ISNUMBER(2) ) {
			RING_API_ERROR(RING_API_BADPARATYPE);
			return ;
		}
		RING_API_RETCPOINTER(al_create_display( (int ) RING_API_GETNUMBER(1),
					 (int ) RING_API_GETNUMBER(2)),"ALLEGRO_DISPLAY");
	}
	RING_FUNC(ring_al_destroy_display)
	{
	  if ( RING_API_PARACOUNT != 1 ) {
		RING_API_ERROR(RING_API_MISS1PARA);
		return ;
	  }
	  if ( ! RING_API_ISPOINTER(1) ) {
		RING_API_ERROR(RING_API_BADPARATYPE);
		return ;
	  }
	  al_destroy_display((ALLEGRO_DISPLAY *) RING_API_GETCPOINTER(1,"ALLEGRO_DISPLAY"));
	}
	RING_FUNC(ring_al_get_new_display_flags)
	{
		if ( RING_API_PARACOUNT != 0 ) {
			RING_API_ERROR(RING_API_BADPARACOUNT);
			return ;
		}
		RING_API_RETNUMBER(al_get_new_display_flags());
	}
	RING_FUNC(ring_al_set_new_display_flags)
	{
		if ( RING_API_PARACOUNT != 1 ) {
			RING_API_ERROR(RING_API_MISS1PARA);
			return ;
		}
		if ( ! RING_API_ISNUMBER(1) ) {
			RING_API_ERROR(RING_API_BADPARATYPE);
			return ;
		}
		al_set_new_display_flags( (int ) RING_API_GETNUMBER(1));
	}
	RING_FUNC(ring_al_get_new_display_option)
	{
		if ( RING_API_PARACOUNT != 2 ) {
			RING_API_ERROR(RING_API_MISS2PARA);
			return ;
		}
		if ( ! RING_API_ISNUMBER(1) ) {
			RING_API_ERROR(RING_API_BADPARATYPE);
			return ;
		}
		if ( ! RING_API_ISSTRING(2) ) {
			RING_API_ERROR(RING_API_BADPARATYPE);
			return ;
		}
		RING_API_RETNUMBER(al_get_new_display_option( (int ) RING_API_GETNUMBER(1),
					RING_API_GETINTPOINTER(2)));
		RING_API_ACCEPTINTVALUE(2) ;
	}
from the previous example we can see how much of time and effort is saved using the Code Generator.
.. index:: 
	pair: Code Generator; Adding code to the generated code
Adding code to the generated code
=================================
	


* SHA224Init(), SHA224Update(), SHA224Final()
.. code-block:: ring

	load "internetlib.ring"
	# Use the Internet functions


* Emscripten (1.39.8) : https://emscripten.org/docs/getting_started/index.html
.. code-block:: ring

	# Get the emsdk repo
	git clone https://github.com/emscripten-core/emsdk.git
	# Enter that directory
	cd emsdk
Use emsdk to install and activate the required version for Qt 5.15
	emsdk install 1.39.8
	emsdk activate --embedded 1.39.8
Check Emscripten installation
	em++ --version
Output
	emcc (Emscripten gcc/clang-like replacement) 1.39.8 
	(commit 24d88487f47629fac9d4acd231497a3a412bdee8)
	Copyright (C) 2014 the Emscripten authors (see AUTHORS.txt)
	This is free and open source software under the MIT license.
	There is NO warranty; not even for MERCHANTABILITY or FITNESS FOR A 
	PARTICULAR PURPOSE.


* Try-Catch
.. code-block:: ring

	see "if statement.." + nl
	x = 1
	if x = 1 
		see "one" + nl
	elseif x=2 
		see "two" + nl
	elseif x=3 
		see "three" + nl
	end
	see "for loop.." + nl
	for t = 1 to 10
		see t 
	end
	see nl
	see "switch..." + nl
	x = 1
	switch x
		on 1 see "one" + nl
		on 2 see "two" + nl
	end
	see "try catch..." + nl
	try
		x = 1 / 0
	catch
		see "catching error" + nl
	end
Output:
	if statement..
	one
	for loop..
	12345678910
	switch...
	one
	try catch...
	catching error
.. index:: 
	pair: Syntax Flexibility; Using braces to start and end different control structures
Using braces to start and end different control structures
==========================================================
We can use braces { } to start and end different control structures


* RingThreads - Better code for creating the Mutex
.. code-block:: ring

	load "rogueutil.ring"
	setConsoleTitle("Using PrintXY()")
	setColor(Black)
	setBackgroundColor(Cyan)
	cls()
	printXY(10,2,'In Mathematics, we call multiplying a number by itself "squaring" the number.')
	for t=1 to 12
		printXY(10,10+t, "Number: " + t + " x " + t + " = " + (t*t) )
	next
	getch() 
.. image:: conui1.png
	:alt: Using RogueUtil Shot1
.. image:: conui2.png
	:alt: Using RogueUtil Shot2
.. index:: 
	pair: What is new in Ring  1.20; Pause/Resume Embedded Ring VM
Pause/Resume Embedded Ring VM
=============================
Ring already supports embedding Ring VM in Ring programs to be able to execute Ring code in isloated Ring state.
Starting from Ring 1.20 we can pause/resume the embedded Ring VM
To pause the VM, just use the (Bye) command which as expected will end the execution but will store
the nPC value (Program Counter) so using ring_state_resume() we can continue the execution at any time starting from this nPC value.
Syntax:
	ring_state_resume(oState,[cPara|nPara],[lUseReturn])
To learn more about this feature check the chapter: Embedding Ring in Ring
.. index:: 
	pair: What is new in Ring  1.20; Better Tools and Extensions
Better Tools and Extensions
===========================


* DisableHashComments
.. code-block:: ring

	DisableHashComments
	#define = 10
	EnableHashComments
	# Just a comment
	DisableHashComments
	? #define
	EnableHashComments
	# End of program


* Bytes2Double()
.. code-block:: ring

	see "Test Int2Bytes() and Bytes2Int() - Value : 77" + nl
	r = Int2Bytes(77)
	see "Int Size : " + len(r) + nl
	see r + nl
	see Bytes2Int(r) + nl
	see "Test Float2Bytes() and Bytes2Float() - Value 77.12" + nl
	r = Float2Bytes(77.12)
	see "Float Size : " + len(r) + nl
	see r + nl
	see Bytes2Float(r) + nl
	see "Test Double2Bytes() and Bytes2Double() - Value 9999977.12345" + nl
	r = Double2Bytes(9999977.12345)
	see "Double Size : " + len(r) + nl
	see r + nl
	decimals(5)
	see Bytes2Double(r) + nl


* Plane Rotations 
.. code-block:: ring

	# Load the library
		load "stbimage.ring"
	# Image Information
		width=0	height=0 channels=0
	# Ring will Free cData automatically in the end of the program
		cData = stbi_load("ring.jpg",:width,:height,:channels,STBI_rgb)
	# Display the output
		? "Size (bytes): " + len(cData)
		? "Width : " + width
		? "Height: " + height
		? "Channels: " + channels
Output:
	Size (bytes): 557371
	Width : 563
	Height: 330
	Channels: 3
.. index:: 
	pair: What is new in Ring 1.13?; More Low Level Functions
More Low Level Functions
========================
The next functions are added to the Low Level functions 
For more information see the Low Level Functions chapter in the documentation
	setpointer(pointer,nNewAddress)
	getpointer(pointer) ---> nAddress
	pointer2string(pointer,nStart,nCount) ---> cString
	memcpy(pDestinationPointer,cSourceString,nSize)
.. index:: 
	pair: What is new in Ring 1.13?; Better Organization
Better Organization
===================
We have better organization for the project folders and source code files
(1) A folder called (Language) contains the source code and the visual source of the Compiler and the Virtual Machine
(2) The (Extensions) folder (Bindings for C/C++ libraries) - contains also the (libdepwin, android & webassembly folders)
(3) The (Libraries) folder - contains Ring libraries written in Ring itself, contains now (GuiLib & ObjectsLib) too
(4) The (Tools) folder - contains development tools - contains now (Editors, RingNotepad, FormDesigner, etc) 
(5) The (Samples) folder - contains Ring samples - a lot of organization is done in this folder
.. index:: 
	pair: What is new in Ring 1.13?; More Improvements
More Improvements
=================


* helloworldview.ring
.. code-block:: ring

	func CloseWindow
		oView {
		}
(2) Then write the method code in the
controller class.
.. image:: fdeventcode.png
	:alt: Form Designer - Event Code
In this example we write
	func CloseWindow
		oView.win.close()
Where inside the controller class, We uses the oView object to access the form.
Another Example : 
.. image:: fdsayhello1.png
	:alt: Form Designer - Say Hello
The Event Code
	func SayHello 
		oView {
			LineEdit2.setText("Hello "+ LineEdit1.text() )
		}
.. index:: 
	pair: Form Designer; Keyboard Shortcuts
Keyboard Shortcuts
==================
After selecting one or group of controls


* Static-Analysis
.. code-block:: ring

	load "typehints.ring"
	see sum(3,4) + nl ;
	see sayHello("Mahmoud");
	int func sum(int x,int y) {
		return x+y ;
	}
	string func sayHello(string name) {
		return "Hello " + name ;
	}
The library is very powerful and support the User types (Classes) automatically!
Example:
	load "typehints.ring"
	import mypackage 
	test()  { main([:one,:two,:three]) }
	myclass func test() {
		see "Testing User Types!" + nl
		return new myclass
	}
	package mypackage {
		public class myclass {
			public static void func main(list args) {
				see "welcome" + nl
				see args
			}
		}
	}
Also You can use the types inside the code (not only the function prototype)
Example:
	load "typehints.ring"
	int 	sum = sum(3,4)
	string 	msg = sayHello("Mahmoud")
	see "Sum = " + sum + nl + msg + nl
	int func sum(int x,int y) {
		return x+y ;
	}
	string func sayHello(string name) {
		return "Hello " + name ;
	}
Rules:


* updateList()/updateColumn() functions - Support Serial/Pow/Rem options.
.. code-block:: ring

	load "fastpro.ring"
	aList = [
		list(5),
		list(5),
		list(5),
		list(5),
		list(5)
	]
	updateList(aList,:serial,:col,1,0)
	updateList(aList,:serial,:col,2,5)
	updateList(aList,:serial,:col,3,10)
	updateList(aList,:serial,:col,4,15)
	updateList(aList,:serial,:col,5,20)
	? copy("*",20)
	for x=1 to 5
		for y=1 to 5
			print(aList[x][y])
			if y != 5 print ("-") ok
		next
		? ""
	next
Output:
The output displays a row of asterisks followed by the elements of the list arranged 
in a structured format.
.. code-block:: none
	********************
	1-6-11-16-21
	2-7-12-17-22
	3-8-13-18-23
	4-9-14-19-24
	5-10-15-20-25
The following screenshot is from the Fast Animation sample: 
The sample uses RingFastPro extension to generate 1024x768 image (Over 60 FPS)
.. image:: fastanimation.png
	:alt: Fast Animation
.. index:: 
	pair: What is new in Ring  1.22; Return Attribute by Reference
Return Attribute by Reference
=============================
In early Ring releases, returning an attribute that contains a List/Object would return a shared reference to this List/Object. This release supports advanced cases for using this feature, allowing nested method calls before returning the List/Object.
.. note:: For advanced usage of references with full customization, refer to the 'Using References' chapter, which explains the Ref()/Reference() function.
Example:
	o = new myclass
	o.getObject().x = 1000
	o.getObject().y = 2000
	o.getObject().z = 3000
	? o.aList
	class myclass
		aList = [ new point {x=10  y=20  z=30 },
			  new point {x=100 y=200 z=300}  ]
		func getObject
			return myMethod()
		func myMethod
			return aList[myIndex()]
	
		func myIndex
			return 2
	class point x y z
Output:
.. code-block:: none
	x: 10
	y: 20
	z: 30
	x: 1000
	y: 2000
	z: 3000
.. index:: 
	pair: What is new in Ring 1.22?; Better Compiler
Better Compiler
===============


* RingInternet
.. code-block:: ring

	load "stdlib.ring"
	load "naturallib.ring"
	New NaturalLanguage {
		SetLanguageName(:MyLanguage)
		SetCommandsPath(CurrentDir()+"/../command")
		SetPackageName("MyLanguage.Natural")
		UseCommand(:Hello)
		UseCommand(:Count)
		RunFile("program.txt")
	}
We defined a language called MyLanguage, We have folder for the language commands.
Each command will define a class that belong to the MyLanguage.Natural package.
We will define two commands, Hello and Count.
So we must have two files for defining the commands in the CurrentDir()+"/../command" folder
File: hello.ring
	DefineNaturalCommand.SyntaxIsKeyword([
		:Package = "MyLanguage.Natural",
		:Keyword = :hello, 
		:Function = func {
			See  "Hello, Sir!" + nl + nl
		}
	])
File: count.ring
	DefineNaturalCommand.SyntaxIsKeywordNumberNumber([
		:Package = "MyLanguage.Natural",
		:Keyword = :count, 
		:Function = func {
			if not isattribute(self,:count_times) {
				AddAttribute(self,:count_times)
				Count_Times = 0
			}
			if Expr(1) > Expr(2) { 
				nStep = -1 
			else 
				nStep = 1
			}
			if Count_Times = 0 { 
				see nl+"The Numbers!" + nl 
				Count_Times++
			else 
				see nl + "I will count Again!" +nl 
			}
			for x = Expr(1) to Expr(2) step nStep {
				see nl+x+nl 
			}
			CommandReturn(fabs(Expr(1)-Expr(2))+1)				
		}
	])
.. index:: 
	pair: What is new in Ring 1.4?; New Style to Ring Notepad
New Style is added to Ring Notepad
==================================
In Ring Notepad - From View - Styles - Select the (Modern) Style
Screen Shot:
.. image:: rnotemodernstyle.png
	:alt: Using Ring Notepad - Modern Style
.. index:: 
	pair: What is new in Ring 1.4?; RingREPL
RingREPL
========
In the application folder, You will find RingREPL (Read-Eval-Print-Loop)
Also you can run it from Ring Notepad (Menubar - Tools)
Screen Shot:
.. image:: ringrepl.png
	:alt: Using RingREPL
.. index:: 
	pair: What is new in Ring 1.4?; Convert between Numbers and Bytes
Convert between Numbers and Bytes
=================================
Ring 1.4 comes with the next functions to convert between Numbers and Bytes.


* Added functions for writing images
.. code-block:: ring

	load "stbimage.ring"
	
	width    = 640
	height   = 480
	channels = 3
	cData    = space(width*height*channels)
	
	? "Creating the image..."
	t1 = clock()
	nIndex=0
	for x=1 to height
   		for y=1 to width
        		cData[nIndex++] = x*x
        		cData[nIndex++] = x*y
        		cData[nIndex++] = x*2
    		next
	next
	t2 = clock()
	
	# Write the image
	? "Writing mynewimage.bmp"
	stbi_write_bmp("mynewimage.bmp", width, height, channels, cData)
	t3 = clock()
	? "Time to create the image : " + ((t2-t1)/clockspersecond()) + " seconds"
	? "Time to save the image   : " + ((t3-t2)/clockspersecond()) + " seconds"
	system("mynewimage.bmp")
Output:
.. code-block:: none
	Creating the image...
	Writing mynewimage.bmp
	Time to create the image : 0.12 seconds
	Time to save the image   : 0.00 seconds
Screen Shot:
.. image:: generateimage.png
	:alt: generateimage
.. index:: 
	pair: What is new in Ring  1.19; Better Extensions Generator
Better Extensions Generator
===========================
The code generator for C/C++ extensions is updated and support defining extra names for functions
Using the predefined list (aExtraFunctionName) we can define the extra names
Example:
To define vec2() as another name for raylib_new_managed_vector2()
.. code-block:: none
	<runcode>
	aExtraFunctionName + ["vec2","raylib_new_managed_vector2"]
	aExtraFunctionName + ["vec2setx","raylib_set_vector2_x"]
	aExtraFunctionName + ["vec2sety","raylib_set_vector2_y"]
	</runcode>
.. index:: 
	pair: What is new in Ring  1.19; Better Documentation
Better Documentation
====================
The CHM file is updated to support search
We added a new chapter: Using FastPro Extension
Also, The next chapters are revised and improved 


* To use the types in the function code, You must set the variable value (Assignment).
.. code-block:: ring

	# Low Level Types
	char 		 
	unsigned 	 
	signed 		 
	int 		 
	short 		 
	long 		 
	float 		 
	double 		 
	void
	byte
	boolean
	 		 
	# High Level Types 
	string 		 
	list 		 
	number 		 
	object		 
	# Other
	public		 
	static		 
	abstract	 
	protected	 
	override
	@override	 


* If Statement
.. code-block:: ring

	if Expression
		Block of statements
	elseif Expression
		Block of statements
	else
		Block of statements
	end
Example:
	put " 
		Main Menu
		---------
		(1) Say Hello
		(2) About
		(3) Exit
	    " get nOption
	if nOption = 1	put "Enter your name : " get name put "Hello " + name + nl
	elseif nOption = 2 put "Sample : using if statement" + nl
	elseif nOption = 3 bye
	else put "bad option..." + nl
	end
.. index:: 
	pair: Control Structures - Second Style; Switch Statement


* Better Documentation
.. code-block:: ring

	# Natural Code
	new program {
		Accept 2 numbers then print the sum
	}
	# Natural Code Implementation
	class program
		# Keywords
			Accept=0 numbers=0 then=0 print=0 the=0 sum=0
		# Execution
		func braceexpreval x
			value = x
		func getnumbers
			for x=1 to value
				see "Enter Number ("+x+") :" give nNumber
				aNumbers + nNumber
			next
		func getsum
			nSUm = 0
			for x in aNumbers nSum+= x next
			see "The Sum : " + nSum
		private
			value=0	aNumbers=[]
		
Output: 
	Enter Number (1) :3
	Enter Number (2) :4
	The Sum : 7
for more information see the "Natural Language Programming" chapter.
.. index:: 
	pair: What is new in Ring 1.1?; Generate/Execute Ring Object Files (*.ringo)
Generate/Execute Ring Object Files (*.ringo)
============================================
This feature enable you to distribute your applications without distributing
the source code. Also it makes application distribution a simple process where
you get one Ring object file for the complete project (many source code files).
Also using Ring object file remove the loading time required for compiling the application.
Check the "command line options" chapter to know more about this feature.
.. index:: 
	pair: What is new in Ring 1.1?; Syntax Flexibility
Syntax Flexibility and different styles for I/O and Control Structures
======================================================================
Programmers are sensitive to the programming language syntax. Great programmers know how to work
using many different styles but each programmer may have his/her favorite style.
Each programming language comes with a style that you may like or not. Ring is just one of these
languages, but as a response to many programmers asking for a better syntax we decided to provide
more options.
Also some of these features are very necessary for Natural Language Programming.
Example : 
We have two commands to change language keywords and operators.
	ChangeRingOperator + plus 
	ChangeRingKeyword see print
	Print 5 plus 5  
	ChangeRingOperator plus +
	ChangeRingKeyword print see
We have new styles (Optional) for Input/Output.
Example :
	Put "What is your name? "
	Get cName
	Put "Hello " + cName
Example : 
	Load "stdlib.ring"
	Print("What is your name? ") 	# print message on screen
	cName=GetString()		# get input from the user
	print("Hello #{cName}")		# say hello!
We have new styles (optional) for control structures.
Example :
	While True
		Put " 
			Main Menu
			---------
			(1) Say Hello
			(2) About
			(3) Exit
		    " Get nOption
		Switch nOption
		Case 1 
			Put "Enter your name : " 
			Get name 
			Put "Hello " + name + nl
		Case 2 
			Put "Sample : using while loop" + nl
		Case 3 
			Bye
		Else 
			Put "bad option..." + nl
		End
	End
Example :
	Load "stdlib.ring"
	While True {
		print(" 
			Main Menu
			---------
			(1) Say Hello
			(2) About
			(3) Exit
			  ")
		nOption = GetString()
		switch nOption {
		case 1 
			print("Enter your name : ")
			name = getstring()
			print("Hello #{name}\n")
		case 2 
			print("Sample : using switch statement\n")
		case 3 
			Bye
		else 
			print("bad option...\n")
		}
	}
Check the next chapters:-


* Ring 1.14 is 60x Faster when adding strings to other strings
.. code-block:: ring

	t1 = clock()
	test = "My Ring"
	for x = 1 to 20000
		test += x
	next 
	? "Time : " + ((clock()-t1)/clockspersecond()) + " seconds"
Output:
.. code-block:: none
	Time : 0.01 seconds


* To type comments just type it between <comment> and </comment>
.. code-block:: ring

	<comment>
	configuration files
	</comment>
.. index:: 
	pair: Code Generator; Executing code during code generation
Executing code during code generation
=====================================


