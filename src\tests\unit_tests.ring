load "stdlibcore.ring"
load "../core/transformer.ring"
load "../core/parallel_processor.ring"
load "../utils/data_utils.ring"

# نظام اختبارات الوحدة
Class UnitTests {
    # المتغيرات
    totalTests
    passedTests
    failedTests
    
    func init {
        totalTests = 0
        passedTests = 0
        failedTests = 0
    }
    
    # تشغيل جميع الاختبارات
    func runAllTests {
        ? "=== بدء اختبارات الوحدة ==="
        
        # اختبارات المحول
        testTransformerInitialization()
        testTokenization()
        testAttention()
        testForwardPass()
        
        # اختبارات المعالجة المتوازية
        testThreadPool()
        testTaskQueue()
        testCacheSystem()
        
        # اختبارات معالجة البيانات
        testDataLoading()
        testPreprocessing()
        testBatchProcessing()
        
        # طباعة النتائج
        printResults()
    }
    
    # اختبار تهيئة المحول
    func testTransformerInitialization {
        startTest("تهيئة المحول")
        
        try {
            transformer = new SmartTransformer()
            assert(transformer != NULL, "فشل إنشاء المحول")
            assert(len(transformer.layers) > 0, "لم يتم تهيئة الطبقات")
            testPassed()
        catch
            testFailed("خطأ في تهيئة المحول: " + cCatchError)
        }
    }
    
    # اختبار الترميز
    func testTokenization {
        startTest("الترميز")
        
        try {
            text = "مرحباً بالعالم"
            tokens = transformer.tokenize(text)
            assert(len(tokens) > 0, "فشل الترميز")
            assert(tokens[1] = "[BOS]", "لم يتم إضافة رمز البداية")
            testPassed()
        catch
            testFailed("خطأ في الترميز: " + cCatchError)
        }
    }
    
    # اختبار آلية الانتباه
    func testAttention {
        startTest("آلية الانتباه")
        
        try {
            query = [[1, 2, 3]]
            key = [[1, 2, 3]]
            value = [[4, 5, 6]]
            
            attention = transformer.calculateAttention(query, key, value)
            assert(len(attention) > 0, "فشل حساب الانتباه")
            testPassed()
        catch
            testFailed("خطأ في حساب الانتباه: " + cCatchError)
        }
    }
    
    # اختبار التمرير الأمامي
    func testForwardPass {
        startTest("التمرير الأمامي")
        
        try {
            input = "Test input"
            output = transformer.forward(input)
            assert(output != NULL, "فشل التمرير الأمامي")
            testPassed()
        catch
            testFailed("خطأ في التمرير الأمامي: " + cCatchError)
        }
    }
    
    # اختبار مجموعة المعالجات
    func testThreadPool {
        startTest("مجموعة المعالجات")
        
        try {
            processor = new ParallelProcessor(4)
            assert(len(processor.threadPool) = 4, "عدد المعالجات غير صحيح")
            testPassed()
        catch
            testFailed("خطأ في مجموعة المعالجات: " + cCatchError)
        }
    }
    
    # اختبار قائمة المهام
    func testTaskQueue {
        startTest("قائمة المهام")
        
        try {
            processor = new ParallelProcessor(2)
            task = new Task()
            processor.addTask(task)
            assert(len(processor.taskQueue) > 0, "لم تتم إضافة المهمة")
            testPassed()
        catch
            testFailed("خطأ في قائمة المهام: " + cCatchError)
        }
    }
    
    # اختبار نظام التخزين المؤقت
    func testCacheSystem {
        startTest("نظام التخزين المؤقت")
        
        try {
            cache = new Cache()
            cache.set("test", "value")
            assert(cache.has("test"), "لم يتم تخزين القيمة")
            assert(cache.get("test") = "value", "القيمة المخزنة غير صحيحة")
            testPassed()
        catch
            testFailed("خطأ في نظام التخزين المؤقت: " + cCatchError)
        }
    }
    
    # اختبار تحميل البيانات
    func testDataLoading {
        startTest("تحميل البيانات")
        
        try {
            utils = new DataUtils()
            data = utils.loadData("test_data.txt")
            assert(data != NULL, "فشل تحميل البيانات")
            testPassed()
        catch
            testFailed("خطأ في تحميل البيانات: " + cCatchError)
        }
    }
    
    # اختبار المعالجة المسبقة
    func testPreprocessing {
        startTest("المعالجة المسبقة")
        
        try {
            utils = new DataUtils()
            text = "Sample text"
            processed = utils.preprocess(text)
            assert(processed != NULL, "فشلت المعالجة المسبقة")
            testPassed()
        catch
            testFailed("خطأ في المعالجة المسبقة: " + cCatchError)
        }
    }
    
    # اختبار معالجة الدفعات
    func testBatchProcessing {
        startTest("معالجة الدفعات")
        
        try {
            utils = new DataUtils()
            data = [1, 2, 3, 4, 5, 6, 7, 8]
            batches = utils.createBatches(data, 2)
            assert(len(batches) = 4, "عدد الدفعات غير صحيح")
            testPassed()
        catch
            testFailed("خطأ في معالجة الدفعات: " + cCatchError)
        }
    }
    
    # وظائف مساعدة
    func startTest(name) {
        ? "اختبار: " + name
        totalTests++
    }
    
    func testPassed {
        passedTests++
        ? "✓ نجح الاختبار"
    }
    
    func testFailed(error) {
        failedTests++
        ? "✗ فشل الاختبار: " + error
    }
    
    func assert(condition, message) {
        if not condition {
            raise(message)
        }
    }
    
    func printResults {
        ? "=== نتائج الاختبارات ==="
        ? "إجمالي الاختبارات: " + totalTests
        ? "الناجحة: " + passedTests
        ? "الفاشلة: " + failedTests
        ? "نسبة النجاح: " + (passedTests/totalTests*100) + "%"
    }
}
