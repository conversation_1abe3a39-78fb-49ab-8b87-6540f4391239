# 🌐 نظام API

## 📋 نظرة عامة
نظام API كامل يوفر واجهة برمجة للتفاعل مع المحول الذكي عبر HTTP.

## 🔌 المكونات

### 1. خادم API (api_server.ring)
- يدير الطلبات HTTP
- يتعامل مع المهام المتزامنة
- يوفر واجهة REST

### 2. عميل API (api_client.ring)
- يوفر واجهة برمجية سهلة
- يتعامل مع الطلبات HTTP
- يدير التحويل JSON

## 🛣️ المسارات المتاحة

### الترجمة
```
POST /api/translate
{
    "text": "النص المراد ترجمته",
    "direction": "ar-en"
}

GET /api/translate/status/:id
```

### توليد الكود
```
POST /api/generate
{
    "prompt": "وصف الكود المطلوب",
    "language": "ring"
}

GET /api/generate/status/:id
```

### التدريب
```
POST /api/train
{
    "config": {
        // إعدادات التدريب
    }
}

GET /api/train/status
```

### النموذج
```
GET /api/model/info
POST /api/model/save
POST /api/model/load
{
    "path": "مسار النموذج"
}
```

## 🚀 كيفية الاستخدام

### تشغيل الخادم
```ring
load "api_server.ring"
server = new APIServer()
server.start()
```

### استخدام العميل
```ring
load "api_client.ring"
client = new APIClient("localhost:8080")

# الترجمة
response = client.translate("مرحباً بالعالم")
? "معرف المهمة: " + response["task_id"]

# التحقق من النتيجة
status = client.getTranslateStatus(response["task_id"])
? "النتيجة: " + status["result"]

# توليد الكود
response = client.generateCode("دالة لحساب المضروب")
? "معرف المهمة: " + response["task_id"]

# التحقق من الكود
status = client.getGenerateStatus(response["task_id"])
? "الكود: " + status["code"]
```

## 🔒 الأمان والتحكم

### التحقق من الصحة
- التحقق من البيانات المدخلة
- التحقق من نوع المحتوى
- معالجة الأخطاء

### إدارة الأخطاء
- رسائل خطأ واضحة
- تتبع الأخطاء
- استجابات HTTP مناسبة

### التحكم في المهام
- تتبع حالة المهام
- إلغاء المهام
- تحديد المهلة الزمنية

## 📊 المراقبة والتقارير

### مراقبة الأداء
- وقت الاستجابة
- عدد الطلبات
- استخدام الموارد

### التقارير
- سجل الطلبات
- إحصائيات الاستخدام
- تقارير الأخطاء

## 🔧 التطوير والتحسين

### إضافة مسار جديد
1. إضافة المسار في `setupRoutes()`
2. إنشاء دالة المعالجة
3. إضافة الوظيفة في العميل
4. تحديث التوثيق

### تحسين الأداء
1. تحسين إدارة المهام
2. تحسين التخزين المؤقت
3. تحسين المعالجة المتوازية

## 📝 ملاحظات هامة

1. **الأداء**:
   - استخدام المعالجة المتوازية
   - تخزين مؤقت للنتائج
   - إدارة فعالة للموارد

2. **الموثوقية**:
   - معالجة الأخطاء
   - استعادة الاتصال
   - نسخ احتياطي للبيانات

3. **القابلية للتوسع**:
   - تصميم مرن
   - واجهة موحدة
   - توثيق شامل

# واجهة برمجة التطبيقات (API) لنظام Smart Transformer

## نظرة عامة
واجهة برمجة تطبيقات RESTful قوية لنظام Smart Transformer، توفر خدمات الترجمة وتوليد الكود.

## المميزات الرئيسية
- مصادقة JWT
- تخزين مؤقت للأداء
- دعم CORS
- تحديد معدل الطلبات
- دعم SSL
- تسجيل الأحداث
- معالجة الأخطاء
- توثيق كامل

## النقاط النهائية

### الترجمة
- `POST /api/v1/translate`
  - ترجمة نص واحد
  - المعاملات:
    * text: النص المراد ترجمته
    * source_lang: لغة المصدر (اختياري، الافتراضي: en)
    * target_lang: لغة الهدف (اختياري، الافتراضي: ar)

- `POST /api/v1/translate/batch`
  - ترجمة مجموعة نصوص
  - المعاملات:
    * texts: مصفوفة النصوص
    * source_lang: لغة المصدر
    * target_lang: لغة الهدف

- `POST /api/v1/translate/analyze`
  - تحليل جودة الترجمة
  - المعاملات:
    * original: النص الأصلي
    * translated: النص المترجم
    * source_lang: لغة المصدر
    * target_lang: لغة الهدف

### توليد الكود
- `POST /api/v1/code/generate`
  - توليد كود من وصف
  - المعاملات:
    * description: وصف الكود المطلوب
    * language: لغة البرمجة (اختياري)
    * with_tests: توليد اختبارات (اختياري)
    * with_docs: توليد توثيق (اختياري)

- `POST /api/v1/code/improve`
  - تحسين كود موجود
  - المعاملات:
    * code: الكود المراد تحسينه
    * suggestions: اقتراحات للتحسين (اختياري)

- `POST /api/v1/code/tests`
  - توليد اختبارات لكود
  - المعاملات:
    * code: الكود المراد اختباره
    * language: لغة البرمجة (اختياري)

### المصادقة
- `POST /api/v1/auth/login`
  - تسجيل الدخول
  - المعاملات:
    * username: اسم المستخدم
    * password: كلمة المرور

### الحالة
- `GET /api/v1/status`
  - حالة الخادم

## الإعداد

1. تثبيت المتطلبات:
   ```ring
   load "stdlib.ring"
   load "weblib.ring"
   load "jsonlib.ring"
   ```

2. تكوين الإعدادات:
   - تعديل `config.ring` حسب بيئتك

3. تشغيل الخادم:
   ```ring
   ring server.ring
   ```

## الأمان
- مصادقة JWT
- دعم SSL
- تحديد معدل الطلبات
- تنقية المدخلات
- حماية CORS

## التخزين المؤقت
- تخزين مؤقت للترجمات
- تخزين مؤقت للكود المولد
- إدارة تلقائية للذاكرة المؤقتة

## التسجيل
- تسجيل كامل للطلبات
- تسجيل الأخطاء
- إحصائيات الأداء

## المساهمة
1. انسخ المستودع
2. أنشئ فرعاً جديداً
3. قم بالتغييرات
4. أرسل طلب سحب

## الترخيص
MIT
