
Class DataTransformer {

    func transformTranslation(source, target) {
        # تنظيف النصوص
        source = cleanText(source)
        target = cleanText(target)
        
        if source = "" or target = "" {
            debug("خطأ: النص فارغ بعد التنظيف")
            return []
        }
        
        debug("تم تحويل الترجمة")
        return [source, target]
    }
    
    func transformCodeExample(code, comment) {
        # تنظيف التعليق
        comment = cleanText(comment)
        
        if code = "" or comment = "" {
            debug("خطأ: الكود أو التعليق فارغ بعد التنظيف")
            return []
        }
        
        debug("تم تحويل مثال الكود")
        return [code, comment]
    }
    
    func transform(data) {
        if isString(data) {
            return cleanText(data)
        }
        if isList(data) {
            if len(data) = 2 {
                return [cleanText(data[1]), cleanText(data[2])]
            }
        }
        return data
    }

    func prepare(data) {
        return cleanText(data)
    }

    private 

    # تنظيف النص من الأخطاء الشائعة
    func cleanText(text) {
        if !isString(text) {
            debug("خطأ: النص غير صالح للتنظيف")
            return ""
        }
        
        text = trim(text)
        # إزالة المسافات المتكررة
        while substr(text, "  ") {
            text = substr(text, "  ", " ")
        }
        # تنظيف علامات الترقيم
        text = substr(text, "،", ",")
        text = substr(text, "؛", ";")
        text = substr(text, "'", "'")
        text = substr(text, '"', '"')
        text = substr(text, '"', '"')
        return text
    }

    func normalize(data) {
        if isString(data) {
            return cleanText(data)
        }
        return data
    }

    func format(data) {
        if isString(data) {
            return trim(data)
        }
        return data
    }
    # دالة للتتبع مع التحكم في الطباعة
    func debug(msg) {
        see "[DATA-TRANSFORMER-DEBUG] " + msg + nl
    }

}
