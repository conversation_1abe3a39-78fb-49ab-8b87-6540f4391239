.. index:: 
	single: Getting Started - First Style; Introduction

=============================
Getting Started - First Style
=============================

.. index:: 
	pair: Getting Started - First Style; Hello World

Hello World
===========

The next program prints the Hello World message on the screen (std-out).

.. code-block:: ring

	see "Hello World"

.. index:: 
	pair: Getting Started - First Style; Run the program

Run the program
===============

to run the program, save the code in a file, for example : hello.ring
then from the command line or terminal, run it using Ring

.. code-block:: ring

	ring hello.ring


.. index:: 
	pair: Getting Started - First Style; Create Executable File

Create Executable File
======================

Using Ring2EXE we can create executable file for our application

.. code-block:: ring

	ring2exe hello.ring -static

.. index:: 
	pair: Getting Started - First Style; Not Case-Sensitive

Not Case-Sensitive
==================

Since the Ring language is not case-sensitive, the same program can
be written in different styles

.. tip:: It's better to select one style and use it in all of the program source code

.. code-block:: ring

	SEE "Hello World"

.. code-block:: ring

	See "Hello World"


.. index:: 
	pair: Getting Started - First Style; Multi-Line literals

Multi-Line literals
===================

Using Ring we can write multi-line literal, see the next example

.. code-block:: ring

	See "
		Hello 
		Welcome to the Ring programming language
		How are you?

	    "

Also you can use the nl variable to insert new line
and you can use the + operator to concatenate strings

As we have NL for new lines, we have Tab and CR (Carriage return) too!

.. note:: nl value means a new line and the actual codes that
	 represent a newline is different between operating systems

.. code-block:: ring

	See "Hello" + nl + "Welcome to the Ring programming language" + 
	    nl + "How are you?"

.. index:: 
	pair: Getting Started - First Style; Getting Input

Getting Input
=============

You can get the input from the user using the give command

.. code-block:: ring

	See "What is your name? "
	Give cName
	See "Hello " + cName

.. index:: 
	pair: Getting Started - First Style; No Explicit End For Statements

No Explicit End For Statements
==============================

You don't need to use ';' or press ENTER to separate statements.
The previous program can be written in one line.

.. code-block:: ring

	See "What is your name? " give cName see "Hello " + cName

.. index:: 
	pair: Getting Started - First Style; Using ? to print expression then new line

Using ? to print expression then new line
=========================================

It's common to print new line after printing an expression, We can use the ? operator to do that!

Example:

.. code-block:: ring

	? "Hello, World!"
	for x = 1 to 10
		? x
	next

Output:

.. code-block:: none

	Hello, World!
	1
	2
	3
	4
	5
	6
	7
	8
	9
	10

.. index:: 
	pair: Getting Started - First Style; Writing Comments

Writing Comments
================

We can write one line comments and multi-line comments

The comment starts with # or //

Multi-lines comments are written between /* and */

.. code-block:: ring


	/* 
		Program Name : My first program using Ring
		Date         : 2016.09.09
		Author       : Mahmoud Fayed
	*/

	See "What is your name? " 	# print message on screen
	give cName 			# get input from the user
	see "Hello " + cName		# say hello!

	// See "Bye!"

.. note:: Using // to comment a lines of code is just a code style.



