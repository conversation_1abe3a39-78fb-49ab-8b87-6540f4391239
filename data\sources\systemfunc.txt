.. index:: 
	single: System Functions; Introduction

================
System Functions
================

In this chapter we are going to learn about the system functions

* System()

* SysGet()

* SysSet()

* SysUnset()

* IsMSDOS()

* IsWindows()

* IsWindows64()

* IsUnix()

* IsMacOSX()

* IsLinux()

* IsFreeBSD()

* IsAndroid()

* GetArch()

* Windowsnl()

* Get Command Line Arguments

* Get Active Source File Name

* CurrentDir()

* ExeFileName()

* ChDir()

* ExeFolder()

* Version()

* Shutdown()

* NofProcessors()

.. index:: 
	pair: System Functions; System() Function

System() Function
=================

We can execute system commands using the system() function

Syntax:

.. code-block:: ring

	System(cCommand)

Example:

.. code-block:: ring

	System("myapp.exe") 	# Run myapp.exe
	System("ls")		# print list of files

.. index:: 
	pair: System Functions; SysGet() Function

SysGet() Function
=================

We can get environment variables using the Get() function

Syntax:

.. code-block:: ring

	SysGet(cVariable)

Example:

.. code-block:: ring

	see sysget("path")		# print system path information

.. index:: 
	pair: System Functions; SysSet() Function

SysSet() Function
=================

We can set environment variables using the SysSet() function

.. code-block:: none

	SysSet(cVariable, cValue) ---> Returns 1 for success and return 0 for failure

.. index:: 
	pair: System Functions; SysUnset() Function

SysUnset() Function
===================

We can delete an environment variables using the SysUnset() function

.. code-block:: none

	SysUnset(cVariable) ---> Returns 1 for success and return 0 for failure



.. index:: 
	pair: System Functions; IsMSDOS() Function

IsMSDOS() Function
==================

We can check if the operating system is MSDOS or not using the IsMSDOS() function

Syntax:

.. code-block:: ring

	IsMSDOS() ---> Returns 1 if the operating system is MS-DOS, Returns 0 if it's not
		       

.. index:: 
	pair: System Functions; IsWindows() Function

IsWindows() Function
====================

We can check if the operating system is Windows or not using the IsWindows() function

Syntax:

.. code-block:: ring

	IsWindows() ---> Returns 1 if the operating system is Windows, Returns 0 if it's not


.. index:: 
	pair: System Functions; IsWindows64() Function

IsWindows64() Function
======================

We can check if the operating system is Windows 64bit or not using the IsWindows64() function

Syntax:

.. code-block:: ring

	IsWindows64() ---> Returns 1 if the operating system is Windows64, Returns 0 if it's not


.. index:: 
	pair: System Functions; IsUnix() Function

IsUnix() Function
=================

We can check if the operating system is Unix or not using the IsUnix() function

Syntax:

.. code-block:: ring

	IsUnix() ---> Returns 1 if the operating system is Unix, Returns 0 if it's not


.. index:: 
	pair: System Functions; IsMacOSX() Function

IsMacOSX() Function
===================

We can check if the operating system is macOS or not using the IsMacOSX() function

Syntax:

.. code-block:: ring

	IsMacOSX() ---> Returns 1 if the operating system is Mac OS X, Returns 0 if it's not


.. index:: 
	pair: System Functions; IsLinux() Function

IsLinux() Function
==================

We can check if the operating system is Linux or not using the IsLinux() function

Syntax:

.. code-block:: ring

	IsLinux() ---> Returns 1 if the operating system is Linux, Returns 0 if it's not


.. index:: 
	pair: System Functions; IsFreeBSD() Function

IsFreeBSD() Function
====================

We can check if the operating system is FreeBSD or not using the IsFreeBSD() function

Syntax:

.. code-block:: ring

	IsFreeBSD() ---> Returns 1 if the operating system is FreeBSD, Returns 0 if it's not


.. index:: 
	pair: System Functions; IsAndroid() Function

IsAndroid() Function
====================

We can check if the operating system is Android or not using the IsAndroid() function

Syntax:

.. code-block:: ring

	IsAndroid() ---> Returns 1 if the operating system is Android, Returns 0 if it's not

.. index:: 
	pair: System Functions; Example

Example
=======

.. code-block:: ring

	see "IsMSDOS()     --> " + ismsdos()     + nl
	see "IsWindows()   --> " + iswindows()   + nl
	see "IsWindows64() --> " + iswindows64() + nl
	see "IsUnix()      --> " + isunix()      + nl
	see "IsMacOSX()    --> " + ismacosx()    + nl
	see "IsLinux()     --> " + islinux()     + nl
	see "IsFreeBSD()   --> " + isfreebsd()   + nl
	see "IsAndroid()   --> " + isandroid()   + nl

Output:

.. code-block:: ring

	IsMSDOS()     --> 0
	IsWindows()   --> 1
	IsWindows64() --> 0
	IsUnix()      --> 0
	IsMacOSX()    --> 0
	IsLinux()     --> 0
	IsFreeBSD()   --> 0
	IsAndroid()   --> 0


.. index:: 
	pair: System Functions; GetArch() Function

GetArch() Function
==================

We can detect the architecture of the Ring executable using the GetArch() function

Syntax:

.. code-block:: none

	GetArch() ---> cString (The name of the architecture of the Ring executable)

Possible values are:

.. code-block:: none

    x86
    x64
    arm64
    arm
    unknown

Example:

.. code-block:: ring

	switch getarch()
	on "x86"
	        ? "x86 32bit architecture"
	on "x64"
	        ? "x64 64bit architecture"
	on "arm64"
	        ? "ARM64 64bit architecture"
	on "arm"
	        ? "ARM 32bit architecture"
	other
	        ? "Unknown architecture"
	off


.. index:: 
	pair: System Functions; Windowsnl() Function

Windowsnl() Function
====================

We can get the windows new line string using the Windowsnl() function.

Syntax:

.. code-block:: ring

	WindowsNL() ---> Returns a string contains CR+LF = CHAR(13) + CHAR(10)

Example:

.. code-block:: ring

	cStr = read("input.txt")

	if iswindows()
		cStr = substr(cStr,windowsnl(),nl)
	ok

	aList = str2list(cStr)
	# to do - list items processing using "for in"
	cStr = list2str(aList)

	if iswindows()
		cStr = substr(cStr,nl,windowsnl())
	ok

	write("output.txt",cStr)

.. index:: 
	pair: System Functions; Get Command Line Arguments

Get Command Line Arguments
==========================

We can get the command line arguments passed to the ring script using the sysargv variable.

The sysargv variable is a list contains the command line parameters.

Example

.. code-block:: ring

	see copy("=",30) + nl
	see "Command Line Parameters" + nl
	see "Size : " + len(sysargv) + nl
	see sysargv
	see copy("=",30) + nl
	if len(sysargv) < 4 return ok
	nStart = sysargv[3]
	nEnd = sysargv[4]
	for x = nStart to nEnd
		see x + nl
	next

Output

.. code-block:: ring

	b:\mahmoud\apps\ring>ring tests\syspara.ring 1 10
	==============================
	Command Line Parameters
	Size : 4
	ring
	tests\syspara.ring
	1
	10
	==============================
	1
	2
	3
	4
	5
	6
	7
	8
	9
	10

.. index:: 
	pair: System Functions; Get Active Source File Name

Get Active Source File Name
===========================

We can get the active source file name (*.ring) using the filename() function

Syntax:

.. code-block:: ring

	filename() ---> String contains the active source file name.

Example:

.. code-block:: ring

	see "Active Source File Name : " + filename() + nl

Output:

.. code-block:: ring

	Active Source File Name : tests\filename.ring


Example:

.. code-block:: ring

	if sysargv[2] = filename()
		see "I'm the main program file!" + nl
		# we can run tests here!
	else
		see "I'm a sub file in a program" + nl
	ok


.. index:: 
	pair: System Functions; PrevFileName() Function

PrevFileName() Function
=======================

Using the PrevFileName() function we can get the previous active source file name.

The previous file would be the file of the caller function, Or the file of the function
that we called before calling PrevFileName().

Syntax:

.. code-block:: ring

	prevfilename() ---> String contains the previous source file name.

Example:

The next function in stdlib.ring uses the PrevFileName() to know if the file of the
caller function is the main source file of the program or not.

.. code-block:: ring

	Func IsMainSourceFile
		if PrevFileName() = sysargv[2]
			return true
		ok
		return false

.. index:: 
	pair: System Functions; CurrentDir() Function

CurrentDir() Function
=====================

Return the path of the current directory

Syntax:

.. code-block:: ring

	CurrentDir() ---> String contains the path of the current directory 

.. index:: 
	pair: System Functions; ExeFileName() Function

ExeFileName() Function
======================

Return the Ring executable file name

Syntax:

.. code-block:: ring

	exefilename() ---> String contains the Ring executable file name

.. index:: 
	pair: System Functions; ChDir() Function

ChDir() Function
================

Change the current directory

Syntax:

.. code-block:: ring

	ChDir(cNewPath)

.. index:: 
	pair: System Functions; ExeFolder() Function

ExeFolder() Function
====================

Return the Ring executable file path

Syntax:

.. code-block:: ring

	exefolder() ---> String contains the Ring executable path

.. index:: 
	pair: System Functions; Version() Function

Version() Function
==================

Return the Ring version

Syntax:

.. code-block:: ring

	version([lPatch]) ---> String contains the Ring version

Example:

.. code-block:: ring

	? version()
	? version(True)

Output:

.. code-block:: ring

	1.22
	1.22.0


.. index:: 
	pair: System Functions; Shutdown() Function

Shutdown() Function
===================

Close the application

Syntax:

.. code-block:: ring

	shutdown(nStatus) ---> Close the application

.. index:: 
	pair: System Functions; NofProcessors() Function

NofProcessors() Function
========================

We can detect the number of processors using the NofProcessors() Function

Syntax:

.. code-block:: none

	NofProcessors() ---> nProcessors

Example:

.. code-block:: ring

	? NofProcessors()
