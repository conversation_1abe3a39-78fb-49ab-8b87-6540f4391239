.. index:: 
	Single: Using HTTPLib;  Introduction

=============
Using HTTPLib
=============

In this chapter we will learn how to use the HTTPLib library.

.. index:: 
	pair: Using HTTPLib; Introduction

Introduction
============

This extension provides support for the httplib library

URL: https://github.com/yhirose/cpp-httplib


.. index:: 
	pair: Using HTTPLib; Server Class Methods

Server Class Methods
====================

* route(cType,cURL,cCode)
* setContent(cContent,cType)
* setHTMLPage(oPage)
* shareFolder(cFolder)
* setCookie(cStr)
* cookies() -> aList
* getFileContent(cFile) -> cString
* getFileName(cFile) -> cString

.. index:: 
	pair: Using HTTPLib; Example

Example
=======

.. code-block:: ring

	load "httplib.ring"

	oServer = new Server {

		? "Try localhost:8080/hi"
		route(:Get,"/hi",:mytest)

		? "Listen to port 8080"
		listen("0.0.0.0", 8080)

	}

	func mytest 
		oServer.setContent("Hello World!", "text/plain")


.. index:: 
	pair: Using HTTPLib; Samples

Samples
=======

The samples exist in ring/samples/UsingHTTPLib folder

.. index:: 
	pair: Using HTTPLib; Printing Constants

Printing Constants
==================

The next example print the constants defined by the extension

.. code-block:: ring

	load "httplib.ring"

	? "Constants:"

	? HTTPLIB_KEEPALIVE_TIMEOUT_SECOND
	? HTTPLIB_KEEPALIVE_MAX_COUNT
	? HTTPLIB_CONNECTION_TIMEOUT_SECOND
	? HTTPLIB_CONNECTION_TIMEOUT_USECOND
	? HTTPLIB_READ_TIMEOUT_SECOND
	? HTTPLIB_READ_TIMEOUT_USECOND
	? HTTPLIB_WRITE_TIMEOUT_SECOND
	? HTTPLIB_WRITE_TIMEOUT_USECOND
	? HTTPLIB_IDLE_INTERVAL_SECOND
	? HTTPLIB_IDLE_INTERVAL_USECOND
	? HTTPLIB_REQUEST_URI_MAX_LENGTH
	? HTTPLIB_REDIRECT_MAX_COUNT
	? HTTPLIB_PAYLOAD_MAX_LENGTH
	? HTTPLIB_TCP_NODELAY
	? HTTPLIB_COMPRESSION_BUFSIZ
	? HTTPLIB_THREAD_POOL_COUNT
	? HTTPLIB_RECV_FLAGS
	? HTTPLIB_LISTEN_BACKLOG

.. index:: 
	pair: Using HTTPLib; Using HTTP Get

Using HTTP GET
==============

Example(1):

.. code-block:: ring

	load "httplib.ring"

	? "Start the server..."
	oServer = new Server 

	? "Try localhost:8080/hi"
	oServer.route(:Get,"/hi",:mytest)

	? "Listen to port 8080"
	oServer.listen("0.0.0.0", 8080)

	func mytest 
		oServer.setContent("Hello World!", "text/plain")

Example(2):

.. code-block:: ring

	load "httplib.ring"

	? "Start the server..."
	oServer = new Server 

	? "Try localhost:8080/one"
	oServer.route(:Get,"/one",:one)
	? "Try localhost:8080/two"
	oServer.route(:Get,"/two",:two)

	? "Listen to port 8080"
	oServer.listen("0.0.0.0", 8080)

	func one
		oServer.setContent("one", "text/plain")

	func two
		oServer.setContent("two", "text/plain")

Example(3):

In this example we will use anonymous function

.. code-block:: ring

	load "httplib.ring"

	? "Try localhost:8080/hello"

	oServer = new Server {

		route(:Get,"/hello",func {
			oServer.setContent("Hello, World!", "text/plain")
		})

		listen("0.0.0.0", 8080)

	}

Example(4):

.. code-block:: ring 

	load "httplib.ring"

	? "Try localhost:8080/hi - See output in console at Server-Side"
	? "Try localhost:8080/hello - See output in web browser at Client-Side"

	oServer = new Server {

		route(:Get,"/hi",'? "Wow, I love Ring programming!"')
		route(:Get,"/hello",'oServer.setContent("Hello, World!", "text/plain")')

		listen("0.0.0.0", 8080)

	}

Example(5):

.. code-block:: ring

	load "httplib.ring"

	new Client("localhost:8080") {
		? download("/one")
		? download("/two")
	}

.. tip:: Using the Download() method in the InternetLib is faster

.. index:: 
	pair: Using HTTPLib; Using WebLib

Using WebLib
============

Example(1):

.. code-block:: ring 

	load "httplib.ring"
	load "weblib.ring"
	import System.Web

	? "Start the server..."
	oServer = new Server 

	? "Try localhost:8080/report"
	oServer.route(:Get,"/report",:report)

	? "Listen to port 8080"
	oServer.listen("0.0.0.0", 8080)

	func report
		oPage = New HTMLPage
		{
			nRowsCount = 10
			title = "Report"
			h1 { text("Customers Report") }
			Table
			{
				style = stylewidth("100%") + stylegradient(4)
				TR
				{
					TD { WIDTH="10%" text("Customers Count : " )  }
								TD { text (nRowsCount) }
				}
			}
			Table
			{
				style = stylewidth("100%") + stylegradient(26)
				TR
				{
					style = stylewidth("100%") + stylegradient(24)
					TD { text("Name " )  }
					TD { text("Age" ) }
					TD { text("Country" ) }
					TD { text("Job" ) }	
					TD { text("Company" ) }
				}
				for x =  1 to nRowsCount
					TR
					{
						TD { text("Test" )  }
						TD { text("30" ) }
						TD { text("Egypt" ) }
						TD { text("Sales" ) }	
						TD { text("Future" ) }
					}
				next
			}
		}

		oServer.setHTMLPage(oPage)

.. index:: 
	pair: Using HTTPLib; Using HTTP Post

Using HTTP Post
===============
		
Example(1):

.. code-block:: ring

	load "httplib.ring"
	load "weblib.ring"
	import System.Web

	? "Start the server..."
	oServer = new Server 

	? "Try localhost:8080/form"
	oServer.route(:Get,"/form",:form)
	oServer.route(:Post,"/formresponse",:formresponse)

	? "Listen to port 8080"
	oServer.listen("0.0.0.0", 8080)

	func form

		oPage = New HTMLPageFunctions
		{
			boxstart()
				text( "Post Test")
				newline()
			boxend()
			divstart([:style=StyleFloatLeft()+StyleWidth("100px") ])
				newline()
				text( "Number1 : " )  	newline() newline()
				text( "Number2 : " ) 	newline() newline()
			divend()
			formpost("formresponse")
				divstart([ :style = styleFloatLeft()+StyleWidth("200px") ])
					newline()
					textbox([ :name = "Number1" ]) 	newline() newline()
					textbox([ :name = "Number2" ]) 	newline() newline()
					submit([ :value = "Send" ] )
				divend()
			formend()
		}		
		oServer.setHTMLPage(oPage)

	func formresponse

		oPage = New HTMLPageFunctions
		{
			boxstart()
				text( "Post Result" )
				newline()
			boxend()
			divstart([ :style = styleFloatLeft()+styleWidth("200px") ])
				newline()
				text( "Number1 : " + oServer["Number1"] )  	
				newline() newline()
				text( "Number2 : " + oServer["Number2"] )  	
				newline() newline()
				text( "Sum : " + (0 + oServer["Number1"] + 
						oServer["Number2"] ) )
				newline()
			divend()		
		}	

		oServer.setHTMLPage(oPage)

Example(2):

.. code-block:: ring

	load "httplib.ring"
	Load "openssllib.ring"
	load "weblib.ring"
	import System.Web

	? "Start the server..."
	oServer = new Server 

	? "Try localhost:8080/hash"
	oServer.route(:Get,"/hash",:hash)
	oServer.route(:Post,"/hashresponse",:hashresponse)

	? "Listen to port 8080"
	oServer.listen("0.0.0.0", 8080)

	func hash

		oPage = New HTMLPageFunctions
		{
			boxstart()
				text( "Hash Test")
				newline()
			boxend()
			divstart([:style = StyleFloatLeft() + StyleWidth("100px") ])
				newline()
				text( "Value : " )  	
				newline() newline()
			divend()
			formpost("/hashresponse")
				divstart([:style = StyleFloatLeft() + StyleWidth("300px") ])
					newline()
					textbox([ :name = "Value" ]) 	
					newline() newline()
					submit([ :value = "Send" ])
				divend()
			formend()
		}		
		oServer.setHTMLPage(oPage)

	func hashresponse

		oPage = New HTMLPageFunctions
		{
			boxstart()
				text( "Hash Result" )
				newline()
			boxend()
			divstart([:style = styleFloatLeft() + styleWidth("100%") ])
				newline()
				text( "Value : " + oServer["Value"] )  	
				newline()
				text( "MD5 : " + md5(oServer["Value"]) )  	
				newline()
				text( "SHA1 : " + SHA1(oServer["Value"]) )
				newline()
				text( "SHA256 : " + SHA256(oServer["Value"]) )
				newline()
				text( "SHA224 : " + SHA224(oServer["Value"]) )
				newline()
				text( "SHA384 : " + SHA384(oServer["Value"]) )
				newline()
				text( "SHA512 : " + SHA512(oServer["Value"]) )
				newline()
			divend()
		}	

		oServer.setHTMLPage(oPage)

.. index:: 
	pair: Using HTTPLib; More Samples
		
More Samples
============

Using Gradients:

.. code-block:: ring

	load "httplib.ring"
	load "weblib.ring"
	import System.Web

	? "Start the server..."
	oServer = new Server 

	? "Try localhost:8080/gradient"
	oServer.route(:Get,"/gradient",:gradient)

	? "Listen to port 8080"
	oServer.listen("0.0.0.0", 8080)

	func gradient

		oPage = New HTMLPageFunctions
		{
			boxstart()
				text("StyleGradient() Function")
			boxend()		 
			for x = 1 to 60
				divstart([ :id = x , :align = "center" , 
					   :style = stylefloatleft() + 
								stylesize(string(100/60*6)+"%","50px") + 
								stylegradient(x) ])
					h3(x)
				divend()
			next
		}		
		oServer.setHTMLPage(oPage)

Using Lists:

.. code-block:: ring 

	load "httplib.ring"
	load "weblib.ring"
	import System.Web

	? "Start the server..."
	oServer = new Server 

	? "Try localhost:8080/lists"
	oServer.route(:Get,"/lists",:lists)

	? "Listen to port 8080"
	oServer.listen("0.0.0.0", 8080)

	func lists

		oPage = New HTMLPageFunctions
		{
			ulstart([])
				for x = 1 to 10
					listart([])
						text(x)
					liend()
				next
			ulend()

			list2ul(["one","two","three","four","five"])

			ulstart([])
				for x = 1 to 10
					listart([])
						cFuncName = "btn"+x+"()"
						button([ :onclick = cFuncName , :value = x])
						script(scriptfuncalert(cFuncName,string(x)))
					liend()
				next
			ulend()
		}		
		oServer.setHTMLPage(oPage)

Using Tables:

.. code-block:: ring 

	load "httplib.ring"
	load "weblib.ring"
	import System.Web

	? "Start the server..."
	oServer = new Server 

	? "Try localhost:8080/table"
	oServer.route(:Get,"/table",:table)

	? "Listen to port 8080"
	oServer.listen("0.0.0.0", 8080)

	func table

		oPage = New HTMLPageFunctions
		{
			divstart([ :style = styledivcenter("400px","500px") ] )
				style(styletable() + styletablerows("t01"))
				tablestart([ :id = :t01 , :style = stylewidth("100%") ])
					rowstart([]) 
						headerstart([]) text("Number") headerend()
						headerstart([]) text("square") headerend()
					rowend() 
					for x = 1 to 10
						rowstart([])
							cellstart([]) text(x) cellend()
							cellstart([]) text(x*x) cellend()
						rowend()
					next
				tableend()
			divend()
		}		
		oServer.setHTMLPage(oPage)

Play Video:

.. code-block:: ring

	load "httplib.ring"
	load "weblib.ring"
	import System.Web

	? "Start the server..."
	oServer = new Server 

	? "Try localhost:8080/play"
	oServer.route(:Get,"/play",:play)

	? "We support files in the res folder like res/horse.ogg and res/movie.mp4"
	oServer.shareFolder("res")

	? "Listen to port 8080"
	oServer.listen("0.0.0.0", 8080)

	func play

		oPage = New HTMLPage
		{
			Title = "Welcome"
			h1 { text("Play sound and video!") }
			div 
			{
				audio
				{
					src = "res/horse.ogg"
					type = "audio/ogg"
				}
		
				video
				{
					width = 320
					height = 240
					src = "res/movie.mp4"
					type = "video/mp4" 
				}
		
			}
		}		
		oServer.setHTMLPage(oPage)

.. index:: 
	pair: Using HTTPLib; Using Cookies
		
Using Cookies
=============

Example:

.. code-block:: ring

	load "httplib.ring"
	load "weblib.ring"
	import System.Web

	? "Start the server..."
	oServer = new Server 

	? "Try localhost:8080/cookie"
	oServer.route(:Get,"/cookie",:cookie)
	oServer.route(:Get,"/cookieresponse",:cookieresponse)

	? "Listen to port 8080"
	oServer.listen("0.0.0.0", 8080)

	func cookie

		oPage = New HTMLPageFunctions
		{
			boxstart()
				text( "Cookie Test" )
				newline()
			boxend()
			link([ :url = "/cookieresponse", :title = "Use Cookies" ]) 	
		}		

		oServer.setCookie("programminglanguage=Ring")
		oServer.setCookie("library=HTTPLib")

		oServer.setHTMLPage(oPage)

	func cookieresponse

		aCookies = oServer.Cookies()

		oPage = New HTMLPageFunctions
		{
			boxstart()
				text( "Cookies Values" )
				newline()
			boxend()
			link([ :url = "cookie", :title = "back" ]) 			
			newline() 
			divstart([:style="float:left;width:200px"])
				text( "Programming Language : " + aCookies[:programminglanguage] )
				newline()
				text( "Library : " + aCookies[:library] )
				newline()
			divend()
		}	
		oServer.setHTMLPage(oPage)

.. index:: 
	pair: Using HTTPLib; Uploading Files
		
Uploading Files
===============

Example:

.. code-block:: ring 

	load "httplib.ring"
	load "weblib.ring"
	import System.Web

	? "Start the server..."
	oServer = new Server 

	cUploadFolder = "upload/"
	oServer.shareFolder(cUploadFolder)

	? "Try localhost:8080/upload"
	oServer.route(:Get,"/upload",:upload)
	oServer.route(:Post,"/uploadresponse",:uploadresponse)

	? "Listen to port 8080"
	oServer.listen("0.0.0.0", 8080)

	func upload

		oPage = New HTMLPageFunctions
		{
			boxstart()
				text( "Upload File" )
				newline()
			boxend()
			for x = 1 to 3 newline() next
			formupload("/uploadresponse")		
				text( "Customer Name : " )
				textbox([ :name = "custname" ]) 	
				newline() newline()		
				divstart([ :style = styleFloatLeft() + styleWidth("90px") ])
					uploadfile("file1")  newline() newline()
					uploadfile("file2") newline() newline()
					submit([ :value = "Send" ])
				divend()		
			formend()
		}		

		oServer.setHTMLPage(oPage)

	func uploadresponse

		oPage = New HTMLPageFunctions
		{
			boxstart()
				text( "Upload Result" )
				newline()
			boxend()			
			newline() 
			divstart([ :style=  styleFloatLeft() + styleWidth("100px") ])
				text( "Name : " + oServer["custname"] )
				newline()
			divend()
			getuploadedfile(self,"file1")
			getuploadedfile(self,"file2")
		}	

		oServer.setHTMLPage(oPage)


	Func getUploadedFile oObj,cFile
		cNewFileName = oServer.getfilename(cFile)
		if cNewFileName = NULL return ok
		cNewFileContent = oServer.getFileContent(cFile)
		/*
			Here we use object.property instead of object { } 
			To avoid executing braceend() method
		*/
		cFileName = cUploadFolder + cNewFileName
		write(cFileName,cNewFileContent)
		if isLinux()
			system("chmod a+x "+cFileName)
		ok
		oObj.newline() 
		oObj.text( "File "+cFileName+ " Uploaded ..." ) 
		oObj.newline() 
		imageURL = cFileName
		oObj.link([ :url = imageURL, :title = "Download" ]) 
		oObj.newline()
		oObj.image( [ :url = imageURL , :alt = :image  ] )
		oObj.newline()

.. index:: 
	pair: Using HTTPLib; Using Templates
		
Using Templates
===============

Example:

.. code-block:: ring

	load "httplib.ring"
	load "weblib.ring"
	import System.Web

	? "Start the server..."
	oServer = new Server 

	? "Try localhost:8080/template"
	oServer.route(:Get,"/template"," new numbersController { start() } ")

	? "Listen to port 8080"
	oServer.listen("0.0.0.0", 8080)

	class numbersController

		MyHeader aNumbers		

		func Start

			MyHeader = New Header
			{
				cColumn1 = "Number" cColumn2 = "Square"
			}

			aNumbers = list(20)

			for x = 1 to len(aNumbers)
				aNumbers[x] = new number
				{
					nValue  = x   nSquare = x*x
				}
			next	  

			cTemp = Template("templates/mynumbers.html",self)

			oPage = new HTMLPageFunctions
			{ 			
				boxstart()
					text( "Test Templates" )  			
					newline()
				boxend()
				html(cTemp)  
			} 

			oServer.setHTMLPage(oPage)

	Class Header cColumn1 cColumn2

	Class Number nValue   nSquare

.. index:: 
	pair: Using HTTPLib; Regular Expressions
	
Regular Expressions
===================

Example:

.. code-block:: ring

	load "httplib.ring"

	? "Start the server..."
	oServer = new Server 

	? "Try localhost:8080/numbers/<number>"
	? "Example: localhost:8080/numbers/123"
	oServer.route(:Get,"(/numbers/(\d+))",:mytest)

	? "Listen to port 8080"
	oServer.listen("0.0.0.0", 8080)

	func mytest 
		cOutput = "Match(1): " + oServer.Match(1) + nl
		cOutput += "Match(2): " + oServer.Match(2) + nl
		oServer.setContent(cOutput, "text/plain")


.. index:: 
	pair: Using HTTPLib; Stop the Server
		
Stop the Server
===============

Example:

.. code-block:: ring

	load "httplib.ring"

	? "Start the server..."
	oServer = new Server 

	? "Try localhost:8080/time"
	? "Try localhost:8080/stop"
	oServer.route(:Get,"/time",:gettime)
	oServer.route(:Get,"/stop",:stop)

	? "Listen to port 8080"
	oServer.listen("0.0.0.0", 8080)

	func gettime
		oServer.setContent("Time: " + time(), "text/plain")

	func stop
		oServer.stop()

