# آلية الانتباه متعدد الرؤوس (Multi-Head Attention)
# تسمح هذه الآلية للنموذج بالتركيز على أجزاء مختلفة من المدخلات في نفس الوقت
# من خلال عدة رؤوس للانتباه تعمل بالتوازي
Load "stdlibcore.ring"
Load "../utils/math.ring"

Class MultiHeadAttention {
    # المتغيرات العامة للفئة
    nHeads      # عدد رؤوس الانتباه
    nDModel     # البعد الأساسي للنموذج
    nDK         # بعد المفتاح (Key dimension)
    nDV         # بعد القيمة (Value dimension)
    
    # مصفوفات الأوزان
    WQ  # مصفوفات الأوزان للاستعلام (Query)
    WK  # مصفوفات الأوزان للمفتاح (Key)
    WV  # مصفوفات الأوزان للقيمة (Value)
    WO  # مصفوفة الأوزان للمخرج (Output)
    
    # دالة التهيئة: تقوم بإعداد مصفوفات الأوزان وتحديد الأبعاد
    # المدخلات:
    #   heads: عدد رؤوس الانتباه
    #   d_model: البعد الأساسي للنموذج
    func init(heads, d_model) {
        nHeads = heads
        nDModel = d_model
        nDK = floor(d_model / heads)
        nDV = nDK
        
        # تهيئة المصفوفات
        WQ = []
        WK = []
        WV = []
        WO = []
        
        # تهيئة الأوزان بقيم عشوائية صغيرة
        for i = 1 to nDModel
            WQ_row = []
            WK_row = []
            WV_row = []
            WO_row = []
            for j = 1 to nDModel
                add(WQ_row, random_normal(0, 0.02))
                add(WK_row, random_normal(0, 0.02))
                add(WV_row, random_normal(0, 0.02))
                add(WO_row, random_normal(0, 0.02))
            next
            add(WQ, WQ_row)
            add(WK, WK_row)
            add(WV, WV_row)
            add(WO, WO_row)
        next
    }
    
    # دالة الانتباه المقياسي للنقطة (Scaled Dot-Product Attention)
    # تحسب درجات الانتباه بين الاستعلام والمفاتيح، ثم تطبقها على القيم
    # المدخلات:
    #   Q: مصفوفة الاستعلام
    #   K: مصفوفة المفاتيح
    #   V: مصفوفة القيم
    #   mask: قناع اختياري لإخفاء بعض الاتصالات
    # المخرجات:
    #   مصفوفة تمثل نتيجة الانتباه المرجح
    func scaled_dot_product_attention(Q, K, V, mask) {
        # حساب درجات الانتباه: Q × K^T
        scores = matrixMultiply(Q, matrixTranspose(K))
        
        # التحجيم
        scale = sqrt(nDK)
        scores = matrixDivide(scores, scale)
        
        # تطبيق القناع إذا وجد
        if mask != null {
            scores = applyMask(scores, mask)
        }
        
        # تطبيق softmax
        attention_weights = softmax(scores)
        
        # الضرب النهائي مع V
        return matrixMultiply(attention_weights, V)
    }
    
    # دالة تقسيم الرؤوس: تقسم المدخلات إلى عدة رؤوس للمعالجة المتوازية
    # المدخلات:
    #   X: المصفوفة المراد تقسيمها
    # المخرجات:
    #   مصفوفة مقسمة حسب عدد الرؤوس
    func splitHeads(X) {
        # إعادة تشكيل المصفوفة لتقسيم الرؤوس
        batch_size = len(X)
        result = []
        
        for b = 1 to batch_size
            head_matrices = []
            for h = 1 to nHeads
                head_matrix = []
                for i = 1 to len(X[b])
                    row = []
                    start_idx = (h-1) * nDK + 1
                    end_idx = h * nDK
                    for j = start_idx to end_idx
                        add(row, X[b][i][j])
                    next
                    add(head_matrix, row)
                next
                add(head_matrices, head_matrix)
            next
            add(result, head_matrices)
        next
        return result
    }
    
    # دالة دمج الرؤوس: تجمع نتائج الرؤوس المختلفة في مصفوفة واحدة
    # المدخلات:
    #   X: مصفوفة تحتوي على نتائج الرؤوس المنفصلة
    # المخرجات:
    #   مصفوفة مدمجة تجمع كل النتائج
    func mergeHeads(X) {
        # دمج الرؤوس مرة أخرى
        batch_size = len(X)
        result = []
        
        for b = 1 to batch_size
            merged = []
            for i = 1 to len(X[b][1])
                row = []
                for h = 1 to nHeads
                    for j = 1 to nDK
                        add(row, X[b][h][i][j])
                    next
                next
                add(merged, row)
            next
            add(result, merged)
        next
        return result
    }
    
    # دالة التقدم الأمامي: تنفذ عملية الانتباه متعدد الرؤوس بالكامل
    # المدخلات:
    #   X: المدخلات الأصلية
    #   mask: قناع اختياري
    # المخرجات:
    #   مصفوفة تمثل نتيجة الانتباه متعدد الرؤوس
    func forward(X, mask = null) {
        batch_size = len(X)
        
        # التحويلات الخطية
        Q = matrixMultiply(X, WQ)
        K = matrixMultiply(X, WK)
        V = matrixMultiply(X, WV)
        
        # تقسيم الرؤوس
        Q = splitHeads(Q)
        K = splitHeads(K)
        V = splitHeads(V)
        
        # حساب الانتباه لكل رأس
        attn_outputs = []
        for b = 1 to batch_size
            batch_outputs = []
            for h = 1 to nHeads
                attn = scaled_dot_product_attention(
                    Q[b][h], K[b][h], V[b][h], 
                    mask ? mask[b] : null
                )
                add(batch_outputs, attn)
            next
            add(attn_outputs, batch_outputs)
        next
        
        # دمج الرؤوس
        concat = mergeHeads(attn_outputs)
        
        # التحويل النهائي
        output = matrixMultiply(concat, WO)
        
        return output
    }
    
    private
    
    # دالة تطبيق القناع: تخفي بعض الاتصالات في مصفوفة درجات الانتباه
    # المدخلات:
    #   scores: مصفوفة درجات الانتباه
    #   mask: مصفوفة ثنائية تحدد المواقع المراد إخفاؤها
    # المخرجات:
    #   مصفوفة درجات الانتباه بعد تطبيق القناع
    func applyMask(scores, mask) {
        # تطبيق القناع عن طريق وضع -inf في المواقع المقنعة
        for i = 1 to len(scores)
            for j = 1 to len(scores[1])
                if mask[i][j] = 0
                    scores[i][j] = 0.000000001
                ok
            next
        next
        return scores
    }
}
