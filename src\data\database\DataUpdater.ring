load "DatabaseManager.ring"
load "DataCleaner.ring"

class DataUpdater
    db
    cleaner
    
    func init
        db = new DatabaseManager
        cleaner = new DataCleaner
        
    func batchUpdateTranslations data
        # data يجب أن تكون قائمة من القوائم: [[source_text, target_text, source_lang, target_lang], ...]
        db.sqlite_execute(db.db, "BEGIN TRANSACTION")
        try
            for record in data
                if len(record) != 4 loop ok
                
                sourceText = cleaner.cleanText(record[1])
                targetText = cleaner.cleanText(record[2])
                sourceLang = cleaner.normalizeLanguageCode(record[3])
                targetLang = cleaner.normalizeLanguageCode(record[4])
                
                if cleaner.validateTranslationData(sourceText, targetText, sourceLang, targetLang)
                    query = "INSERT OR REPLACE INTO translations 
                            (source_text, target_text, source_lang, target_lang) 
                            VALUES (?, ?, ?, ?)"
                    db.sqlite_execute(db.db, query, [sourceText, targetText, sourceLang, targetLang])
                ok
            next
            db.sqlite_execute(db.db, "COMMIT")
            return true
        catch
            db.sqlite_execute(db.db, "ROLLBACK")
            return false
        done
        
    func updatePerformanceMetrics metrics
        # metrics يجب أن تكون قائمة من القوائم: [[operation_type, execution_time, memory_usage, success_rate], ...]
        db.sqlite_execute(db.db, "BEGIN TRANSACTION")
        try
            for metric in metrics
                if len(metric) != 4 loop ok
                
                if metric[2] >= 0 and metric[3] >= 0 and metric[4] >= 0 and metric[4] <= 1
                    query = "INSERT INTO performance_metrics 
                            (operation_type, execution_time, memory_usage, success_rate) 
                            VALUES (?, ?, ?, ?)"
                    db.sqlite_execute(db.db, query, metric)
                ok
            next
            db.sqlite_execute(db.db, "COMMIT")
            return true
        catch
            db.sqlite_execute(db.db, "ROLLBACK")
            return false
        done
        
    func archiveOldData daysToKeep
        # نقل البيانات القديمة إلى جدول الأرشيف
        query = "INSERT INTO translations_archive 
                SELECT * FROM translations 
                WHERE created_at < DATE('now', '-' || ? || ' days')"
        db.sqlite_execute(db.db, query, [daysToKeep])
        
        # حذف البيانات المؤرشفة من الجدول الرئيسي
        query = "DELETE FROM translations 
                WHERE created_at < DATE('now', '-' || ? || ' days')"
        db.sqlite_execute(db.db, query, [daysToKeep])
        
    func optimizeTables
        db.sqlite_execute(db.db, "VACUUM")
        db.sqlite_execute(db.db, "ANALYZE")
        
    func createBackup backupPath
        try
            db.sqlite_execute(db.db, "BEGIN TRANSACTION")
            db.sqlite_execute(db.db, "VACUUM INTO '" + backupPath + "'")
            db.sqlite_execute(db.db, "COMMIT")
            return true
        catch
            db.sqlite_execute(db.db, "ROLLBACK")
            return false
        done
        
    func restoreFromBackup backupPath
        if not fileExists(backupPath) return false ok
        
        try
            db.close()
            remove("database.db")
            copy(backupPath, "database.db")
            db = new DatabaseManager
            return true
        catch
            return false
        done
