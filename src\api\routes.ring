load "middleware/auth.ring"
load "controllers/translation_controller.ring"
load "controllers/code_generation_controller.ring"

class Router
    auth
    translationCtrl
    codeGenCtrl
    
    func init
        auth = new AuthMiddleware
        translationCtrl = new TranslationController
        codeGenCtrl = new CodeGenerationController
    
    func setupRoutes app
        # مسارات الترجمة
        app.post("/api/v1/translate", auth.authenticate,
            func(req, res) translationCtrl.translate(req, res))
            
        app.post("/api/v1/translate/batch", auth.authenticate,
            func(req, res) translationCtrl.batchTranslate(req, res))
            
        app.post("/api/v1/translate/analyze", auth.authenticate,
            func(req, res) translationCtrl.analyzeQuality(req, res))
        
        # مسارات توليد الكود
        app.post("/api/v1/code/generate", auth.authenticate,
            func(req, res) codeGenCtrl.generateCode(req, res))
            
        app.post("/api/v1/code/improve", auth.authenticate,
            func(req, res) codeGenCtrl.improveCode(req, res))
            
        app.post("/api/v1/code/tests", auth.authenticate,
            func(req, res) codeGenCtrl.generateTests(req, res))
        
        # مسارات المصادقة
        app.post("/api/v1/auth/login", func(req, res)
            if not req.body[:username] or not req.body[:password]
                return res.badRequest("اسم المستخدم وكلمة المرور مطلوبان")
            ok
            
            # التحقق من المستخدم (يجب تنفيذ المنطق الخاص بك)
            user = [
                :id = 1,
                :username = req.body[:username],
                :role = "user"
            ]
            
            token = auth.generateToken(user)
            return res.ok([
                :token = token,
                :user = user
            ])
        )
        
        # مسار الحالة
        app.get("/api/v1/status", func(req, res)
            return res.ok([
                :status = "up",
                :timestamp = date(),
                :version = "1.0.0"
            ])
        )
        
        # مسار اختبار المصادقة
        app.get("/api/v1/test-auth", auth.authenticate, func(req, res)
            return res.ok([
                :message = "تمت المصادقة بنجاح",
                :user = req.user
            ])
        )

        # معالجة الأخطاء
        app.use(func(err, req, res, next)
            ? "خطأ: " + err
            return res.error("حدث خطأ في الخادم")
        )
