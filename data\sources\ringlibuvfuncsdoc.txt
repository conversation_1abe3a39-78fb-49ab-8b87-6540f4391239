.. index:: 
     single: RingLibuv Functions Reference; Introduction

=============================
RingLibuv Functions Reference
=============================


Introduction
============

In this chapter we have a list of the supported functions by this extension 

Reference
=========

* int uv_loop_init(uv_loop_t* loop)
* int uv_loop_configure(uv_loop_t* loop, uv_loop_option option, int)
* int uv_loop_close(uv_loop_t* loop)
* uv_loop_t* uv_default_loop(void)
* int uv_run(uv_loop_t* loop, uv_run_mode mode)
* int uv_loop_alive(const uv_loop_t* loop)
* void uv_stop(uv_loop_t* loop)
* size_t uv_loop_size(void)
* int uv_backend_fd(const uv_loop_t* loop)
* int uv_backend_timeout(const uv_loop_t* loop)
* uint64_t uv_now(const uv_loop_t* loop)
* void uv_update_time(uv_loop_t* loop)
* void uv_walk(uv_loop_t* loop, uv_walk_cb walk_cb, void* arg)
* void uv_walk_2(uv_loop_t* loop, uv_walk_cb walk_cb, void* arg)
* int uv_loop_fork(uv_loop_t* loop)
* int uv_is_active(const uv_handle_t* handle)
* int uv_is_closing(const uv_handle_t* handle)
* void uv_close(uv_handle_t* handle, uv_close_cb close_cb)
* void uv_close_2(uv_handle_t* handle, uv_close_cb close_cb)
* void uv_ref(uv_handle_t* handle)
* void uv_unref(uv_handle_t* handle)
* int uv_has_ref(const uv_handle_t* handle)
* size_t uv_handle_size(uv_handle_type type)
* int uv_send_buffer_size(uv_handle_t* handle, int* value)
* int uv_recv_buffer_size(uv_handle_t* handle, int* value)
* int uv_fileno(const uv_handle_t* handle, uv_os_fd_t* fd)
* int uv_cancel(uv_req_t* req)
* size_t uv_req_size(uv_req_type type)
* int uv_timer_init(uv_loop_t* loop, uv_timer_t* handle)
* int uv_timer_start(uv_timer_t* handle, uv_timer_cb cb, uint64_t timeout, uint64_t repeat)
* int uv_timer_start_2(uv_timer_t* handle, uv_timer_cb cb, uint64_t timeout, uint64_t repeat)
* int uv_timer_stop(uv_timer_t* handle)
* int uv_timer_again(uv_timer_t* handle)
* void uv_timer_set_repeat(uv_timer_t* handle, uint64_t repeat)
* uint64_t uv_timer_get_repeat(const uv_timer_t* handle)
* int uv_prepare_init(uv_loop_t* loop, uv_prepare_t* prepare)
* int uv_prepare_start(uv_prepare_t* prepare, uv_prepare_cb cb)
* int uv_prepare_start_2(uv_prepare_t* prepare, uv_prepare_cb cb)
* int uv_prepare_stop(uv_prepare_t* prepare)
* int uv_check_init(uv_loop_t* loop, uv_check_t* check)
* int uv_check_start(uv_check_t* check, uv_check_cb cb)
* int uv_check_start_2(uv_check_t* check, uv_check_cb cb)
* int uv_check_stop(uv_check_t* check)
* int uv_idle_init(uv_loop_t* loop, uv_idle_t* idle)
* int uv_idle_start(uv_idle_t* idle, uv_idle_cb cb)
* int uv_idle_start_2(uv_idle_t* idle, uv_idle_cb cb)
* int uv_idle_stop(uv_idle_t* idle)
* int uv_async_init(uv_loop_t* loop, uv_async_t* async, uv_async_cb async_cb)
* int uv_async_init_2(uv_loop_t* loop, uv_async_t* async, uv_async_cb async_cb)
* int uv_async_send(uv_async_t* async)
* int uv_poll_init(uv_loop_t* loop, uv_poll_t* handle, int fd)
* int uv_poll_init_socket(uv_loop_t* loop, uv_poll_t* handle, uv_os_sock_t socket)
* int uv_poll_start(uv_poll_t* handle, int events, uv_poll_cb cb)
* int uv_poll_start_2(uv_poll_t* handle, int events, uv_poll_cb cb)
* int uv_poll_stop(uv_poll_t* poll)
* int uv_signal_init(uv_loop_t* loop, uv_signal_t* signal)
* int uv_signal_start(uv_signal_t* signal, uv_signal_cb cb, int signum)
* int uv_signal_start_2(uv_signal_t* signal, uv_signal_cb cb, int signum)
* int uv_signal_start_oneshot(uv_signal_t* signal, uv_signal_cb cb, int signum)
* int uv_signal_start_oneshot_2(uv_signal_t* signal, uv_signal_cb cb, int signum)
* int uv_signal_stop(uv_signal_t* signal)
* void uv_disable_stdio_inheritance(void)
* int uv_spawn(uv_loop_t* loop, uv_process_t* handle, const uv_process_options_t* options)
* int uv_process_kill(uv_process_t* handle, int signum)
* int uv_kill(int pid, int signum)
* int uv_shutdown(uv_shutdown_t* req, uv_stream_t* handle, uv_shutdown_cb cb)
* int uv_shutdown_2(uv_shutdown_t* req, uv_stream_t* handle, uv_shutdown_cb cb)
* int uv_listen(uv_stream_t* stream, int backlog, uv_connection_cb cb)
* int uv_listen_2(uv_stream_t* stream, int backlog, uv_connection_cb cb)
* int uv_accept(uv_stream_t* server, uv_stream_t* client)
* int uv_read_start(uv_stream_t* stream, uv_alloc_cb alloc_cb, uv_read_cb read_cb)
* int uv_read_start_2(uv_stream_t* stream, uv_alloc_cb alloc_cb, uv_read_cb read_cb)
* int uv_read_stop(uv_stream_t*)
* int uv_write(uv_write_t* req, uv_stream_t* handle, uv_buf_t *bufs, unsigned int nbufs, uv_write_cb cb)
* int uv_write_2(uv_write_t* req, uv_stream_t* handle, uv_buf_t *bufs, unsigned int nbufs, uv_write_cb cb)
* int uv_write2(uv_write_t* req, uv_stream_t* handle, uv_buf_t *bufs, unsigned int nbufs, uv_stream_t* send_handle, uv_write_cb cb)
* int uv_write2_2(uv_write_t* req, uv_stream_t* handle, uv_buf_t *bufs, unsigned int nbufs, uv_stream_t* send_handle, uv_write_cb cb)
* int uv_try_write(uv_stream_t* handle, uv_buf_t *bufs, unsigned int nbufs)
* int uv_is_readable(const uv_stream_t* handle)
* int uv_is_writable(const uv_stream_t* handle)
* int uv_stream_set_blocking(uv_stream_t* handle, int blocking)
* int uv_tcp_init(uv_loop_t* loop, uv_tcp_t* handle)
* int uv_tcp_init_ex(uv_loop_t* loop, uv_tcp_t* handle, unsigned int flags)
* int uv_tcp_open(uv_tcp_t* handle, uv_os_sock_t sock)
* int uv_tcp_nodelay(uv_tcp_t* handle, int enable)
* int uv_tcp_keepalive(uv_tcp_t* handle, int enable, unsigned int delay)
* int uv_tcp_simultaneous_accepts(uv_tcp_t* handle, int enable)
* int uv_tcp_bind(uv_tcp_t *handle,sockaddr *addr,unsigned int flags)
* int uv_tcp_getsockname(const uv_tcp_t* handle, struct sockaddr* name, int* namelen)
* int uv_tcp_getpeername(const uv_tcp_t* handle, struct sockaddr* name, int* namelen)
* int uv_tcp_connect(uv_connect_t* req, uv_tcp_t* handle, sockaddr * addr, uv_connect_cb cb)
* int uv_tcp_connect_2(uv_connect_t* req, uv_tcp_t* handle, sockaddr * addr, uv_connect_cb cb)
* int uv_pipe_init(uv_loop_t* loop, uv_pipe_t* handle, int ipc)
* int uv_pipe_open(uv_pipe_t* handle, uv_file file)
* int uv_pipe_bind(uv_pipe_t* handle, const char * name)
* void uv_pipe_connect(uv_connect_t* req, uv_pipe_t* handle, const char * name, uv_connect_cb cb)
* void uv_pipe_connect_2(uv_connect_t* req, uv_pipe_t* handle, const char * name, uv_connect_cb cb)
* int uv_pipe_getsockname(const uv_pipe_t* handle, char* buffer, size_t* size)
* int uv_pipe_getpeername(const uv_pipe_t* handle, char* buffer, size_t* size)
* void uv_pipe_pending_instances(uv_pipe_t* handle, int count)
* int uv_pipe_pending_count(uv_pipe_t* handle)
* uv_handle_type uv_pipe_pending_type(uv_pipe_t* handle)
* int uv_pipe_chmod(uv_pipe_t* handle, int flags)
* int uv_tty_init(uv_loop_t* loop, uv_tty_t* handle, uv_file fd, int readable)
* int uv_tty_set_mode(uv_tty_t* handle, uv_tty_mode_t mode)
* int uv_tty_reset_mode(void)
* int uv_tty_get_winsize(uv_tty_t* handle, int* width, int* height)
* int uv_udp_init(uv_loop_t* loop, uv_udp_t* handle)
* int uv_udp_init_ex(uv_loop_t* loop, uv_udp_t* handle, unsigned int flags)
* int uv_udp_open(uv_udp_t* handle, uv_os_sock_t sock)
* int uv_udp_bind(uv_udp_t* handle, sockaddr * addr, unsigned int flags)
* int uv_udp_getsockname(const uv_udp_t* handle, struct sockaddr* name, int* namelen)
* int uv_udp_set_membership(uv_udp_t* handle, const char * multicast_addr, const char * interface_addr, uv_membership membership)
* int uv_udp_set_multicast_loop(uv_udp_t* handle, int on)
* int uv_udp_set_multicast_ttl(uv_udp_t* handle, int ttl)
* int uv_udp_set_multicast_interface(uv_udp_t* handle, const char * interface_addr)
* int uv_udp_set_broadcast(uv_udp_t* handle, int on)
* int uv_udp_set_ttl(uv_udp_t* handle, int ttl)
* int uv_udp_send(uv_udp_send_t* req, uv_udp_t* handle, uv_buf_t *bufs, unsigned int nbufs, sockaddr * addr, uv_udp_send_cb send_cb)
* int uv_udp_send_2(uv_udp_send_t* req, uv_udp_t* handle, uv_buf_t *bufs, unsigned int nbufs, sockaddr * addr, uv_udp_send_cb send_cb)
* int uv_udp_try_send(uv_udp_t* handle, uv_buf_t *bufs, unsigned int nbufs, sockaddr * addr)
* int uv_udp_recv_start(uv_udp_t* handle, uv_alloc_cb alloc_cb, uv_udp_recv_cb recv_cb)
* int uv_udp_recv_start_2(uv_udp_t* handle, uv_alloc_cb alloc_cb, uv_udp_recv_cb recv_cb)
* int uv_udp_recv_stop(uv_udp_t* handle)
* int uv_fs_event_init(uv_loop_t* loop, uv_fs_event_t* handle)
* int uv_fs_event_start(uv_fs_event_t* handle, uv_fs_event_cb cb, const char * path, unsigned int flags)
* int uv_fs_event_start_2(uv_fs_event_t* handle, uv_fs_event_cb cb, const char * path, unsigned int flags)
* int uv_fs_event_stop(uv_fs_event_t* handle)
* int uv_fs_event_getpath(uv_fs_event_t* handle, char* buffer, size_t* size)
* int uv_fs_poll_init(uv_loop_t* loop, uv_fs_poll_t* handle)
* int uv_fs_poll_start(uv_fs_poll_t* handle, uv_fs_poll_cb poll_cb, const char * path, unsigned int interval)
* int uv_fs_poll_start_2(uv_fs_poll_t* handle, uv_fs_poll_cb poll_cb, const char * path, unsigned int interval)
* int uv_fs_poll_stop(uv_fs_poll_t* handle)
* int uv_fs_poll_getpath(uv_fs_poll_t* handle, char* buffer, size_t* size)
* void uv_fs_req_cleanup(uv_fs_t* req)
* int uv_fs_close(uv_loop_t* loop, uv_fs_t* req, uv_file file, uv_fs_cb cb)
* int uv_fs_open(uv_loop_t* loop, uv_fs_t* req, const char * path, int flags, int mode, uv_fs_cb cb)
* int uv_fs_read(uv_loop_t* loop, uv_fs_t* req, uv_file file, uv_buf_t *bufs, unsigned int nbufs, int64_t offset, uv_fs_cb cb)
* int uv_fs_unlink(uv_loop_t* loop, uv_fs_t* req, const char * path, uv_fs_cb cb)
* int uv_fs_write(uv_loop_t* loop, uv_fs_t* req, uv_file file, uv_buf_t *bufs, unsigned int nbufs, int64_t offset, uv_fs_cb cb)
* int uv_fs_mkdir(uv_loop_t* loop, uv_fs_t* req, const char * path, int mode, uv_fs_cb cb)
* int uv_fs_mkdtemp(uv_loop_t* loop, uv_fs_t* req, const char * tpl, uv_fs_cb cb)
* int uv_fs_rmdir(uv_loop_t* loop, uv_fs_t* req, const char * path, uv_fs_cb cb)
* int uv_fs_scandir(uv_loop_t* loop, uv_fs_t* req, const char * path, int flags, uv_fs_cb cb)
* int uv_fs_scandir_next(uv_fs_t* req, uv_dirent_t* ent)
* int uv_fs_stat(uv_loop_t* loop, uv_fs_t* req, const char * path, uv_fs_cb cb)
* int uv_fs_fstat(uv_loop_t* loop, uv_fs_t* req, uv_file file, uv_fs_cb cb)
* int uv_fs_lstat(uv_loop_t* loop, uv_fs_t* req, const char * path, uv_fs_cb cb)
* int uv_fs_rename(uv_loop_t* loop, uv_fs_t* req, const char * path, const char * new_path, uv_fs_cb cb)
* int uv_fs_fsync(uv_loop_t* loop, uv_fs_t* req, uv_file file, uv_fs_cb cb)
* int uv_fs_fdatasync(uv_loop_t* loop, uv_fs_t* req, uv_file file, uv_fs_cb cb)
* int uv_fs_ftruncate(uv_loop_t* loop, uv_fs_t* req, uv_file file, int64_t offset, uv_fs_cb cb)
* int uv_fs_copyfile(uv_loop_t* loop, uv_fs_t* req, const char * path, const char * new_path, int flags, uv_fs_cb cb)
* int uv_fs_sendfile(uv_loop_t* loop, uv_fs_t* req, uv_file out_fd, uv_file in_fd, int64_t in_offset, size_t length, uv_fs_cb cb)
* int uv_fs_access(uv_loop_t* loop, uv_fs_t* req, const char * path, int mode, uv_fs_cb cb)
* int uv_fs_chmod(uv_loop_t* loop, uv_fs_t* req, const char * path, int mode, uv_fs_cb cb)
* int uv_fs_fchmod(uv_loop_t* loop, uv_fs_t* req, uv_file file, int mode, uv_fs_cb cb)
* int uv_fs_utime(uv_loop_t* loop, uv_fs_t* req, const char * path, double atime, double mtime, uv_fs_cb cb)
* int uv_fs_futime(uv_loop_t* loop, uv_fs_t* req, uv_file file, double atime, double mtime, uv_fs_cb cb)
* int uv_fs_link(uv_loop_t* loop, uv_fs_t* req, const char * path, const char * new_path, uv_fs_cb cb)
* int uv_fs_symlink(uv_loop_t* loop, uv_fs_t* req, const char * path, const char * new_path, int flags, uv_fs_cb cb)
* int uv_fs_readlink(uv_loop_t* loop, uv_fs_t* req, const char * path, uv_fs_cb cb)
* int uv_fs_realpath(uv_loop_t* loop, uv_fs_t* req, const char * path, uv_fs_cb cb)
* int uv_fs_chown(uv_loop_t* loop, uv_fs_t* req, const char * path, uv_uid_t uid, uv_gid_t gid, uv_fs_cb cb)
* int uv_fs_fchown(uv_loop_t* loop, uv_fs_t* req, uv_file file, uv_uid_t uid, uv_gid_t gid, uv_fs_cb cb)
* int uv_fs_close_2(uv_loop_t* loop, uv_fs_t* req, uv_file file, uv_fs_cb cb)
* int uv_fs_open_2(uv_loop_t* loop, uv_fs_t* req, const char * path, int flags, int mode, uv_fs_cb cb)
* int uv_fs_read_2(uv_loop_t* loop, uv_fs_t* req, uv_file file, uv_buf_t *bufs, unsigned int nbufs, int64_t offset, uv_fs_cb cb)
* int uv_fs_unlink_2(uv_loop_t* loop, uv_fs_t* req, const char * path, uv_fs_cb cb)
* int uv_fs_write_2(uv_loop_t* loop, uv_fs_t* req, uv_file file, uv_buf_t *bufs, unsigned int nbufs, int64_t offset, uv_fs_cb cb)
* int uv_fs_mkdir_2(uv_loop_t* loop, uv_fs_t* req, const char * path, int mode, uv_fs_cb cb)
* int uv_fs_mkdtemp_2(uv_loop_t* loop, uv_fs_t* req, const char * tpl, uv_fs_cb cb)
* int uv_fs_rmdir_2(uv_loop_t* loop, uv_fs_t* req, const char * path, uv_fs_cb cb)
* int uv_fs_scandir_2(uv_loop_t* loop, uv_fs_t* req, const char * path, int flags, uv_fs_cb cb)
* int uv_fs_stat_2(uv_loop_t* loop, uv_fs_t* req, const char * path, uv_fs_cb cb)
* int uv_fs_fstat_2(uv_loop_t* loop, uv_fs_t* req, uv_file file, uv_fs_cb cb)
* int uv_fs_lstat_2(uv_loop_t* loop, uv_fs_t* req, const char * path, uv_fs_cb cb)
* int uv_fs_rename_2(uv_loop_t* loop, uv_fs_t* req, const char * path, const char * new_path, uv_fs_cb cb)
* int uv_fs_fsync_2(uv_loop_t* loop, uv_fs_t* req, uv_file file, uv_fs_cb cb)
* int uv_fs_fdatasync_2(uv_loop_t* loop, uv_fs_t* req, uv_file file, uv_fs_cb cb)
* int uv_fs_ftruncate_2(uv_loop_t* loop, uv_fs_t* req, uv_file file, int64_t offset, uv_fs_cb cb)
* int uv_fs_copyfile_2(uv_loop_t* loop, uv_fs_t* req, const char * path, const char * new_path, int flags, uv_fs_cb cb)
* int uv_fs_sendfile_2(uv_loop_t* loop, uv_fs_t* req, uv_file out_fd, uv_file in_fd, int64_t in_offset, size_t length, uv_fs_cb cb)
* int uv_fs_access_2(uv_loop_t* loop, uv_fs_t* req, const char * path, int mode, uv_fs_cb cb)
* int uv_fs_chmod_2(uv_loop_t* loop, uv_fs_t* req, const char * path, int mode, uv_fs_cb cb)
* int uv_fs_fchmod_2(uv_loop_t* loop, uv_fs_t* req, uv_file file, int mode, uv_fs_cb cb)
* int uv_fs_utime_2(uv_loop_t* loop, uv_fs_t* req, const char * path, double atime, double mtime, uv_fs_cb cb)
* int uv_fs_futime_2(uv_loop_t* loop, uv_fs_t* req, uv_file file, double atime, double mtime, uv_fs_cb cb)
* int uv_fs_link_2(uv_loop_t* loop, uv_fs_t* req, const char * path, const char * new_path, uv_fs_cb cb)
* int uv_fs_symlink_2(uv_loop_t* loop, uv_fs_t* req, const char * path, const char * new_path, int flags, uv_fs_cb cb)
* int uv_fs_readlink_2(uv_loop_t* loop, uv_fs_t* req, const char * path, uv_fs_cb cb)
* int uv_fs_realpath_2(uv_loop_t* loop, uv_fs_t* req, const char * path, uv_fs_cb cb)
* int uv_fs_chown_2(uv_loop_t* loop, uv_fs_t* req, const char * path, uv_uid_t uid, uv_gid_t gid, uv_fs_cb cb)
* int uv_fs_fchown_2(uv_loop_t* loop, uv_fs_t* req, uv_file file, uv_uid_t uid, uv_gid_t gid, uv_fs_cb cb)
* int uv_queue_work(uv_loop_t* loop, uv_work_t* req, uv_work_cb work_cb, uv_after_work_cb after_work_cb)
* int uv_queue_work_2(uv_loop_t* loop, uv_work_t* req, uv_work_cb work_cb, uv_after_work_cb after_work_cb)
* int uv_getaddrinfo(uv_loop_t* loop, uv_getaddrinfo_t* req, uv_getaddrinfo_cb getaddrinfo_cb, const char * node, const char * service, const struct addrinfo* hints)
* int uv_getaddrinfo_2(uv_loop_t* loop, uv_getaddrinfo_t* req, uv_getaddrinfo_cb getaddrinfo_cb, const char * node, const char * service, const struct addrinfo* hints)
* void uv_freeaddrinfo(struct addrinfo* ai)
* int uv_getnameinfo(uv_loop_t* loop, uv_getnameinfo_t* req, uv_getnameinfo_cb getnameinfo_cb, sockaddr * addr, int flags)
* int uv_getnameinfo_2(uv_loop_t* loop, uv_getnameinfo_t* req, uv_getnameinfo_cb getnameinfo_cb, sockaddr * addr, int flags)
* int uv_dlopen(const char * filename, uv_lib_t* lib)
* void uv_dlclose(uv_lib_t* lib)
* int uv_dlsym(uv_lib_t* lib, const char * name, void** ptr)
* const char * uv_dlerror(const uv_lib_t* lib)
* int uv_thread_create(uv_thread_t* tid, uv_thread_cb entry, void* arg)
* int uv_thread_create_2(uv_thread_t* tid, uv_thread_cb entry, void* arg)
* uv_thread_t uv_thread_self(void)
* int uv_thread_join(uv_thread_t *tid)
* int uv_thread_equal(const uv_thread_t* t1, const uv_thread_t* t2)
* int uv_key_create(uv_key_t* key)
* void uv_key_delete(uv_key_t* key)
* void* uv_key_get(uv_key_t* key)
* void uv_key_set(uv_key_t* key, void* value)
* int uv_mutex_init(uv_mutex_t* handle)
* int uv_mutex_init_recursive(uv_mutex_t* handle)
* void uv_mutex_destroy(uv_mutex_t* handle)
* void uv_mutex_lock(uv_mutex_t* handle)
* int uv_mutex_trylock(uv_mutex_t* handle)
* void uv_mutex_unlock(uv_mutex_t* handle)
* int uv_rwlock_init(uv_rwlock_t* rwlock)
* void uv_rwlock_destroy(uv_rwlock_t* rwlock)
* void uv_rwlock_rdlock(uv_rwlock_t* rwlock)
* int uv_rwlock_tryrdlock(uv_rwlock_t* rwlock)
* void uv_rwlock_rdunlock(uv_rwlock_t* rwlock)
* void uv_rwlock_wrlock(uv_rwlock_t* rwlock)
* int uv_rwlock_trywrlock(uv_rwlock_t* rwlock)
* void uv_rwlock_wrunlock(uv_rwlock_t* rwlock)
* int uv_sem_init(uv_sem_t* sem, unsigned int value)
* void uv_sem_destroy(uv_sem_t* sem)
* void uv_sem_post(uv_sem_t* sem)
* void uv_sem_wait(uv_sem_t* sem)
* int uv_sem_trywait(uv_sem_t* sem)
* int uv_cond_init(uv_cond_t* cond)
* void uv_cond_destroy(uv_cond_t* cond)
* void uv_cond_signal(uv_cond_t* cond)
* void uv_cond_broadcast(uv_cond_t* cond)
* void uv_cond_wait(uv_cond_t* cond, uv_mutex_t* mutex)
* int uv_cond_timedwait(uv_cond_t* cond, uv_mutex_t* mutex, uint64_t timeout)
* int uv_barrier_init(uv_barrier_t* barrier, unsigned int count)
* void uv_barrier_destroy(uv_barrier_t* barrier)
* int uv_barrier_wait(uv_barrier_t* barrier)
* uv_handle_type uv_guess_handle(uv_file file)
* int uv_replace_allocator(uv_malloc_func malloc_func, uv_realloc_func realloc_func, uv_calloc_func calloc_func, uv_free_func free_func)
* uv_buf_t uv_buf_init(char* base, unsigned int len)
* char** uv_setup_args(int argc, char** argv)
* int uv_get_process_title(char* buffer, size_t size)
* int uv_set_process_title(const char * title)
* int uv_resident_set_memory(size_t* rss)
* int uv_uptime(double* uptime)
* int uv_getrusage(uv_rusage_t* rusage)
* uv_pid_t uv_os_getpid(void)
* uv_pid_t uv_os_getppid(void)
* int uv_cpu_info(uv_cpu_info_t** cpu_infos, int* count)
* void uv_free_cpu_info(uv_cpu_info_t* cpu_infos, int count)
* int uv_interface_addresses(uv_interface_address_t** addresses, int* count)
* void uv_free_interface_addresses(uv_interface_address_t* addresses, int count)
* int uv_ip6_addr(const char * ip, int port, sockaddr_in6* addr)
* int uv_ip4_name(sockaddr_in* src, char* dst, size_t size)
* int uv_ip6_name(sockaddr_in6* src, char* dst, size_t size)
* int uv_inet_ntop(int af, const void* src, char* dst, size_t size)
* int uv_inet_pton(int af, const char * src, void* dst)
* int uv_if_indextoname(unsigned int ifindex, char* buffer, size_t* size)
* int uv_if_indextoiid(unsigned int ifindex, char* buffer, size_t* size)
* int uv_exepath(char* buffer, size_t* size)
* int uv_cwd(char* buffer, size_t* size)
* int uv_chdir(const char * dir)
* int uv_os_homedir(char* buffer, size_t* size)
* int uv_os_tmpdir(char* buffer, size_t* size)
* int uv_os_get_passwd(uv_passwd_t* pwd)
* void uv_os_free_passwd(uv_passwd_t* pwd)
* uint64_t uv_get_total_memory(void)
* uint64_t uv_hrtime(void)
* void uv_print_all_handles(uv_loop_t* loop, FILE* stream)
* void uv_print_active_handles(uv_loop_t* loop, FILE* stream)
* int uv_os_getenv(const char * name, char* buffer, size_t* size)
* int uv_os_setenv(const char * name, const char * value)
* int uv_os_unsetenv(const char * name)
* int uv_os_gethostname(char* buffer, size_t* size)
