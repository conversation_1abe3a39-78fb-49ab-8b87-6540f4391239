.. index:: 
	single: Language Specification; Introduction

======================
Language Specification
======================

In this chapter we will learn about

* Scanner Commands
* Language keywords
* Language Functions
* Compiler Errors
* Runtime Errors
* Environment Errors
* Language Grammar
* Virtual Machine (VM) Instructions

.. index:: 
	pair: Language Specification; Scanner Commands

Scanner Commands
================

Commands Count: 5

* ChangeRingKeyword
* ChangeRingOperator
* DisableHashComments
* EnableHashComments
* LoadSyntax

Grammar:

.. code-block:: none

	Command ---> 'changeringkeyword' <OldKeyword> <NewKeyword>
	Command ---> 'changeringoperator' <OldOperator> <NewOperator>
	Command ---> 'EnableHashComments'
	Command ---> 'DisableHashComments'
	Command ---> 'loadsyntax' <Literal>

.. index:: 
	pair: Language Specification; Language Keywords

Language Keywords
=================

Keywords Count: 56

* again
* and
* but
* bye
* call
* case
* catch
* class
* def
* do
* done
* else
* elseif
* end
* exit
* for
* foreach
* from
* func
* get
* give
* if
* import
* in
* load
* loop
* new
* next
* not
* off
* ok
* on
* or
* other
* package
* private
* put
* return
* see
* step
* switch
* to
* try
* while
* endfunc
* endclass
* endpackage
* endif
* endfor
* endwhile
* endswitch
* endtry
* function
* endfunction
* break
* continue

.. index:: 
	pair: Language Specification; Language Functions

Language Functions
==================

Functions Count: 255

.. note:: Some functions could be used with different parameters type/count

.. code-block:: none

        acos(x) ---> The principal value of the arc cosine of x, expressed in radians
        add(List,Item)
        addattribute(Object,cAttributeName|aAttributesList)
        adddays(cDate,nDays) ---> Date from cDate and after nDays
        addmethod(Object,cNewMethodName,cMethodName|AnonymousFunction)
        ascii(character) ---> ASCII Code
        asin(x) ---> The principal value of the arc sine of x, expressed in radians
        assert(condition)
        atan(x) ---> The principal value of the arc tangent of x, expressed in radians
        atan2(y,x) ---> The principal arc tangent of y/x, in the interval [-pi,+pi] radians
        attributes(object) ---> Returns a list contains the object attributes
	binarysearch(List,ItemValue) ---> Item Index
	binarysearch(List,ItemValue,nColumn) ---> Search in nColumn, returns the Item Index
        bytes2double(cBytes) ---> nNumber
        bytes2float(cBytes) ---> nNumber
        bytes2int(cBytes) ---> nNumber
        callgarbagecollector()
        callgc()
        ceil(x) ---> The smallest integer value greater than or equal to x
        cfunctions() ---> a list contains functions names
        char(ASCII Code) ---> character
        chdir(cNewPath)
        checkoverflow(lFlag)
        classes() ---> a list contains classes names
        classname(object) ---> Returns the object class name
        clearerr(FileHandle)
        clock() ---> The number of clock ticks from program start
        clockspersecond() ---> Number of clocks in one second
        closelib(pDLL)
	copy(string,nCount) ---> string replicated nCount times
        cos(x) ---> The cosine of an angle of x radians
        cosh(x) ---> The hyperbolic cosine of x radians
        currentdir() ---> String contains the path of the current directory
        date() ---> String represent the date "dd/mm/yyyy"
        dec(hexadecimal) ---> decimal
        decimals(n) ---> Determine the decimals digits after the point in float/double numbers
        del(list,index)
        diffdays(cDate1,cDate2) ---> number of days (Date1 - Date2)
	dir(cFolderPath) ---> List contains files & sub folders.
        direxists(cDirPath) ---> returns 1 if the directory exists
        double2bytes(nNumber) ---> cBytes
        eval(cCode)
        exefilename() ---> String contains the Ring executable file name
        exefolder() ---> String contains the Ring executable path
        exp(x) ---> The value of e raised to the xth power
        fabs(x) ---> The absolute value of x
	fclose(FileHandle)
        feof(FileHandle) ---> returns 1 if EOF and 0 if not
        ferror(FileHandle) ---> returns 1 if error and 0 if not
        fexists(cFileName) ---> returns 1 if the file exists
        fflush(FileHandle)
        fgetc(FileHandle) ---> returns character or EOF
        fgetpos(FileHandle) ---> position handle
        fgets(FileHandle,nSize) ---> string
        filename() ---> String contains the active source file name
	find(List,ItemValue) ---> Item Index
	find(List,ItemValue,nColumn) ---> Search in nColumn, returns the Item Index
	find(List,ItemValue,nColumn,cAttribute) ---> Item Index
        float2bytes(nNumber) ---> cBytes
        floor(x) ---> The largest integer value less than or equal to x
	fopen(cFileName,cMode) ---> FileHandle
        fputc(FileHandle,cChar)
        fputs(FileHandle,cString)
        fread(FileHandle,nSize)
        freopen(cFileName,cMode,file handle) ---> FileHandle
        fseek(FileHandle,nOffset,nWhence) ---> zero if successful
        fsetpos(FileHandle,PositionHandle)
        ftell(FileHandle) ---> file position as number
        functions() ---> a list contains functions names
        fwrite(FileHandle,cString)
        getarch() ---> cString (The name of the architecture of the Ring executable)
        getattribute(oObject,cAttributeName) ---> Attribute Value
        getchar() ---> Character
        getfilesize(cFilePath) ---> nSize
	getnumber() ---> Number
        getpathtype(cPath) ---> nStatus
        getpointer(pointer) ---> nAddress 
        getptr(pointer) ---> nAddress 
	getstring() ---> String
        globals() ---> a list contains variables names in the global scope
        hex(decimal) ---> hexadecimal
        hex2str(Hexadecimal string) ---> string
	importpackage(cPackageName)
        input(nCount) ---> string
	insert(List,Index,Item)
        int2bytes(nNumber) ---> cBytes
        intvalue(cVariableName)
        isalnum(value) ---> 1 if the value is digit/letter or 0 if not
        isalpha(value) ---> 1 if the value is a letter or 0 if not
        isandroid() ---> Returns 1 if the operating system is Android, Returns 0 if it's not
        isattribute(object,cAttributeName) ---> Bool
        iscfunction(cFunctionName) ---> returns 1 if the C function is defined
        isclass(cClassName) ---> returns 1 if the Class is defined
        iscntrl(value) ---> 1 if the value is a control character or 0 if not
        isdigit(value) ---> 1 if the value is a digit or 0 if not
        isfreebsd() ---> Returns 1 if the operating system is FreeBSD, Returns 0 if it's not
        isfunction(cFunctionName) ---> returns 1 if the Ring function is defined
        isglobal(cVariableName) ---> returns 1 if the variable is defined in the global scope
        isgraph(value) ---> 1 if the value can be printed (Except space) or 0 if not
        islinux() ---> Returns 1 if the operating system is Linux, Returns 0 if it's not
        islist(value) ---> 1 if the value is a list or 0 if not
        islocal(cVariableName) ---> returns 1 if the variable is defined in the local scope
        islower(value) ---> 1 if the value is lowercase letter or 0 if not
        ismacosx() ---> Returns 1 if the operating system is Mac OS X, Returns 0 if it's not
        ismethod(object,cMethodName) ---> Returns True if the object class contains the method
        ismsdos() ---> Returns 1 if the operating system is MS-DOS, Returns 0 if it's not
        isnull(value) ---> 1 if the value is NULL or 0 if not
        isnumber(value) ---> 1 if the value is a number or 0 if not
        isobject(variable) ---> Returns True if it's an object, False if it's not
        ispackage(cPackageName) ---> returns 1 if the Package is defined
        ispackageclass(cPackageName,cClassName) ---> returns 1 if the Class is defined
        ispointer(vPara) ---> True|False 
        isprint(value) ---> 1 if the value occupies a printing position or 0 if not
        isprivateattribute(object,cAttributeName) ---> lResult
        isprivatemethod(object,cMethodName) ---> lResult
        ispunct(value) ---> 1 if the value is a punctuation character or 0 if not
        isspace(value) ---> 1 if the value is a white-space or 0 if not
        isstring(value) ---> 1 if the value is a string or 0 if not
        isunix() ---> Returns 1 if the operating system is Unix, Returns 0 if it's not
        isupper(value) ---> 1 if the value is an uppercase alphabetic letter or 0 if not
        iswindows() ---> Returns 1 if the operating system is Windows
        iswindows64() ---> Returns 1 if the operating system is Windows64
        isxdigit(value) ---> 1 if the value is a hexadecimal digit character or 0 if not
	left(string,count) ---> Get characters starting from the left
	len(string) ---> String length
        len(List) ---> The list size
	lines(string) ---> Number of lines inside the string
        list(nSize) ---> aList
        list(nRows,nCols) ---> aList
	list2str(list) ---> string contains the list items
        loadlib(cDLLFileName) ---> pDLL
        locals() ---> a list contains the variables names in the current scope
        log(x) ---> The natural logarithm of x (the base of e)
	log(x,b) ---> The logarithm of x to the base of b
        log10(x) ---> The common logarithm (base-10 logarithm) of x
	lower(string) ---> convert string letters to lower c
        max(nNumber1,nNumber) ---> Maximum number
        max(aList) ---> Maximum number inside the list
        memcpy(pDestinationPointer,cSourceString,nSize)
        memorycopy(pDestinationPointer,cSourceString,nSize)
        mergemethods(cClassNameDestination,cClassNameSource)
        methods(object) ---> Returns a list contains the object methods
        min(nNumber,nNumber2) ---> Minimum number
	min(aList) ---> Minimum number inside the list
        murmur3hash(cString,nNumber) ---> nNumber
        newlist(nSize) ---> aList
        newlist(nRows,nCols) ---> aList
        nofprocessors() ---> nProcessors
	nothing(Any number/type of parameters) ---> Zero (0)
        nullpointer() ---> pPointer
        nullptr() ---> pPointer
        number(string) ---> Number
        obj2ptr(List|Object) --> Low Level Object ( C Pointer )
        object2pointer(List|Object) --> Low Level Object ( C Pointer )
        objectid(object) ---> Returns the object id
	optionalfunc(cFuncName)
        packageclasses() cPackageName) ---> a list contains classes names inside the package
        packagename() ---> Returns the package name of the latest successful import
        packages() ---> a list contains packages names
	parentclassname(object) ---> Returns the parent class name of the object class
        perror(cErrorMessage)
        pointer2object(Low Level Object) ---> List|Object
        pointer2string(pointer,nStart,nCount) ---> cString   
        pointercompare(oObject1,oObject2) ---> lResult
        pow(x,y) ---> x raised to the power of y
        prevfilename() ---> String contains the previous source file name.
	print(cString)
	print2str(cString) ---> String
        ptr2obj(Low Level Object) ---> List|Object     
        ptr2str(pointer,nStart,nCount) ---> cString   
        ptrcmp(oObject1,oObject2) ---> lResult
	puts(cString)
        raise(cErrorMessage)
        random(x) ---> A random number in the range [0,x]
        randomize(nNumber) ---> nNumber
	read(cFileName) ---> String contains the file content
	ref(aList|oObject) ---> List/Object reference
	reference(aList|oObject) ---> List/Object reference
	refcount(aList|oObject) ---> References Count
	remove(cFileName)
	rename(cOldFileName,cNewFileName) ---> Zero for Success or -1 for Error
	reverse(List|String) ---> Reversed List|String
        rewind(FileHandle)
	right(string,count) ---> get characters starting from the right
        ring_give(cVariable)
        ring_see(cMessage)
        ring_state_delete(oState)
        ring_state_filetokens(oState,cRingFileName) ---> aTokens
        ring_state_findvar(oState,cVariableName) ---> aVariableList
        ring_state_init() ---> oState
        ring_state_main(cRingFileName)
        ring_state_mainfile(oState,cRingFileName|cRingoFileName)
        ring_state_new() ---> oState
        ring_state_newvar(oState,cVariableName) ---> aVariableList
	ring_state_resume(oState,[cPara|nPara],[lUseReturn])
        ring_state_runcode(oState,cCode)
	ring_state_runcodeatins(oState,nPC)
        ring_state_runfile(oState,cRingFileName)
        ring_state_runobjectfile(oState,cRingObjectFileName)
        ring_state_scannererror(oState) ---> nStatus
        ring_state_setvar(oState,cVariableName,Value)
        ring_state_stringtokens(oState,cRingFileName)
        ring_state_stringtokens(oState,cRingFileName,lCaseSensitive)
        ringvm_callfunc(cFuncName)
        ringvm_calllist() ---> List
        ringvm_cfunctionslist() ---> List
        ringvm_classeslist() ---> List
	ringvm_codelist() ---> List
        ringvm_evalinscope(nScope,cCode)
        ringvm_fileslist() ---> List
        ringvm_functionslist() ---> List
        ringvm_genarray(aList)
        ringvm_give(cVariableName)
        ringvm_hideerrormsg(lStatus)
        ringvm_info() ---> List of information about the VM structure
	ringvm_ismempool() ---> lStatus (Can provide memory or not)
        ringvm_memorylist() ---> List
        ringvm_packageslist() ---> List
        ringvm_passerror()
	ringvm_runcode(cCode)
        ringvm_scopescount() ---> nScopes
        ringvm_see(cMessage)
        ringvm_settrace(cCode)
        ringvm_tracedata() ---> aDataList
        ringvm_traceevent() ---> nTraceEvent
        ringvm_tracefunc() ---> cCode
        setattribute(oObject,cAttributeName,Value)
        setpointer(pointer,nNewAddress)  
        setptr(pointer,nNewAddress)  
        shutdown(nStatus)
        sin(x) ---> The sine of an angle of x radians
        sinh(x) ---> The hyperbolic sine of x radians
	sort(List) ---> Sorted List
	sort(List,nColumn) ---> Sorted List based on nColumn
	sort(List,nColumn,cAttribute) ---> Sorted List based on Object Attribute
        space(nBytesCount) ---> String
        sqrt(x) ---> The square root of x
        srandom(x) ---> Initialize random number generator
        str2hex(string) ---> hexadecimal string
        str2hexcstyle(string) ---> hexadecimal string
	str2list(string) ---> list contains the string lines
	strcmp(cString1,cString2) ---> value = 0 if cString1 = cString2
        string(number) ---> String
	substr(string,substring) ---> the starting position of substring in string
	substr(string,position)  ---> Get substring starting from position to end
	substr(string,position,count)  ---> Get characters starting from position
	substr(string,substring,newsubstring)  ---> Transformed string (Match case)
	substr(string,substring,newsubstring,1) ---> Transformed string (Ignore case)
        swap(aList,nItem1,nItem2)
        sysget(cVariable)
        sysset(cVariable,cValue) ---> Returns 1 for success and return 0 for failure
	syssleep(nMilliSecs) ---> Returns 1 for success and return 0 for failure
        system(cCommand)
        sysunset(cVariable) ---> Returns 1 for success and return 0 for failure
        tan(x) ---> Tangent of an angle of x radians 
        tanh(x) ---> The hyperbolic tangent of x radians
        tempfile() ---> FileHandle
        tempname() ---> generated file name as string
        time() ---> The system time as String
        timelist() ---> List contains the time and date information.
	trim(string) ---> Remove spaces from right and left
        type(value) ---> The Type as String
        ungetc(FileHandle,character)
        unsigned(nNum1,nNum2,cOperator) ---> Perform operation using unsigned numbers
	upper(string) ---> convert string letters to UPPER case
        uptime() ---> nTime
        variablepointer(cVariableName,cPointerType) ---> Low Level Object (C Pointer)
        varptr(cVariableName,cPointerType) ---> Low Level Object (C Pointer)
        version([lPatch]) ---> String contains the Ring version
        windowsnl() ---> Returns a string contains CR+LF = CHAR(13) + CHAR(10)
	write(cFileName,cString)
	
.. index:: 
	pair: Language Specification; Compiler Errors

Compiler Errors
===============

* Error (C1)  : Error in parameters list, expected identifier
* Error (C2)  : Error in class name
* Error (C3)  : Unclosed control structure, 'ok' is missing
* Error (C4)  : Unclosed control structure, 'end' is missing
* Error (C5)  : Unclosed control structure, next is missing
* Error (C6)  : Error in function name
* Error (C7)  : Error in list items
* Error (C8)  : Parentheses ')' is missing 
* Error (C9)  : Brackets ']' is missing 
* Error (C10) : Error in parent class name
* Error (C11) : Error in expression operator
* Error (C12) : No class definition
* Error (C13) : Error in variable name
* Error (C14) : Try/Catch miss the Catch keyword!
* Error (C15) : Try/Catch miss the Done keyword!
* Error (C16) : Error in Switch statement expression!
* Error (C17) : Switch statement without OFF
* Error (C18) : Missing closing brace for the block opened!
* Error (C19) : Numeric Overflow!
* Error (C20) : Error in package name
* Error (C21) : Unclosed control structure, 'again' is missing
* Error (C22) : Function redefinition, function is already defined!
* Error (C23) : Using '(' after number!
* Error (C24) : The parent class name is identical to the subclass name
* Error (C25) : Trying to access the self reference after the object name
* Error (C26) : Class redefinition, class is already defined!
* Error (C27) : Syntax Error!
* Error (C28) : Expression is expected!
* Error (C29) : Braces are missing to define anonymous function!
* Error (C30) : Argument redefinition, argument is already defined!
* Error (C31) : Parentheses '(' is expected

* Error (S1) : Literal not closed!

* Warning (W1) : Unrecognized option 
* Warning (W2) : Duplication in file name
* Warning (W3) : The Compiler command ChangeRingKeyword requires two words as parameters
* Warning (W4) : Compiler command ChangeRingKeyword - Keyword not found!
* Warning (W5) : The Compiler command  ChangeRingOperator requires two words as parameters
* Warning (W6) : Compiler command ChangeRingOperator - Operator not found!
* Warning (W7) : Using the EXIT command outside loop!
* Warning (W8) : Using the LOOP command outside loop!

.. index:: 
	pair: Language Specification; Runtime Errors

Runtime Errors
==============

* Error (R1)  : Can't divide by zero
* Error (R2)  : Array Access (Index out of range)
* Error (R3)  : Calling Function without definition
* Error (R4)  : Stack Overflow
* Error (R5)  : Can't access the list item, Object is not list
* Error (R6)  : Variable is required
* Error (R7)  : Can't assign to a string letter more than one character
* Error (R8)  : Variable is not a string 
* Error (R9)  : Using exit command outside loops 
* Error (R10) : Using exit command with number outside the range 
* Error (R11) : Error in class name, class not found
* Error (R12) : Error in property name, property not found
* Error (R13) : Object is required
* Error (R14) : Calling Method without definition
* Error (R15) : Error in parent class name, class not found
* Error (R16) : Using braces to access unknown object
* Error (R17) : Error, using 'Super' without parent class
* Error (R18) : Numeric Overflow
* Error (R19) : Calling function with less number of parameters
* Error (R20) : Calling function with extra number of parameters
* Error (R21) : Using operator with values of incorrect type
* Error (R22) : Using loop command outside loops 
* Error (R23) : Using loop command with number outside the range 
* Error (R24) : Using uninitialized variable 
* Error (R25) : Error in package name, Package not found
* Error (R26) : Calling private method from outside the class 
* Error (R27) : Using private attribute from outside the class 
* Error (R28) : Using bad data type as step value
* Error (R29) : Using bad data type in for loop
* Error (R30) : Parent class name is identical to child class name 
* Error (R31) : Trying to destroy the object using the self reference 
* Error (R32) : The CALL command expect a variable contains string
* Error (R33) : Bad decimals number (correct range >= 0 and <= 90)
* Error (R34) : Variable is required for the assignment operation
* Error (R35) : Can't create/open the file
* Error (R36) : The column number is not correct! It's greater than the number of columns in the list
* Error (R37) : Sorry, The command is not supported in this context
* Error (R38) : Runtime Error in loading the dynamic library
* Error (R39) : Error occurred creating unique filename
* Error (R40) : Numeric underflow
* Error (R41) : Invalid numeric string
* Error (R42) : Error in eval() function
* Error (R43) : The variable contains a protected value
* Error (R44) : Maximum nested Eval()
* Error (R45) : Variable is not a list
* Error (R46) : The dynamic library doesn't contain the ringlib_init() function!
* Error (R47) : The function is not supported in this platform
* Error (R48) : Assertion Failed!
* Error (R49) : The Ring VM is not created/ready
* Error (R50) : Object does not support operator overloading
* Error (R51) : Value Error
* Error (R52) : Using Return inside function parameters is not allowed
* Error (R53) : Function redefinition, function is already defined!
* Error (R54) : Object attribute redefinition, attribute is already defined!


.. index:: 
	pair: Language Specification; Environment Errors

Environment Errors
==================

* Error (E1) : Caught SegFault
* Error (E2) : Out of Memory
* Error (E3) : Deleting scope while no scope!
* Error (E4) : Long VM Instruction!
* Error (E5) : The file type is not correct - the VM expect a ring object file
* Error (E6) : The Ring Object File version is not correct!
* Error (E7) : Internal error in using sscanf() function!
* Error (E8) : Internal error in using fscanf() function!
* Error (E9) : Can't open file

.. index:: 
	pair: Language Specification; Language Grammar

Language Grammar
================

Program ---> {statement}

Statement ---> 'package' <Identifier> { '.' <Identifier> } ['{' {statement} '}'] ['end'|'endpackage']

Statement ---> 'class' <Identifier> [ 'from'|':'|'<' <Identifier> ] ['{' {statement} '}']['end'|'endclass']

Statement ---> 'func'|'def'|'function' <Identifier> [ParaList] ['{' {statement} '}']['end'|'endfunc'|'endfunction']

Statement ---> 'import' <Identifier> { '.' <Identifier> }

Statement ---> 'private'

Statement ---> 'load' ['package'|'again'] <Literal>

Statement ---> 'see'|'put' <Expr>

Statement ---> 'give'|'get' <Identifier>

Statement ---> 'if' <Expr> ['{'] {statement} [ {'but'|'elseif' <Expr> {Statement} } ] ['else' {Statement} ] 'ok'|'end'|'}'|'endif'

Statement ---> 'Switch' <Expr> ['{'] { 'on'|'case' <Expr> {statement} } ['other' {Statement} ]  'off'|'end'|'}'|'endswitch'

Statement ---> 'for' <Identifier> '=' <Expr> 'to' <Expr> [ 'step' <Expr> ] ['{'] {Statement} 'next'|'end'|'}'|'endfor'

Statement ---> 'for'|'foreach' <Identifier> 'in' <Expr>  [ 'step' <Expr> ] ['{'] {statement} 'next'|'end'|'}'|'endfor'

Statement ---> 'while' <Expr> ['{'] {statement} 'end'|'}'|'endwhile'

Statement ---> 'do' {statement} 'again' <Expr> 

Statement ---> 'try' {statement} ['{'] 'catch' {statement} 'done'|'end'|'}'|'endtry'

Statement ---> 'return' <Expr>

Statement ---> 'bye'

Statement ---> 'exit'|'break'

Statement ---> 'loop'|'continue'

Statement ---> <Expr>

Statement ---> epsilon

ParaList ---> epsilon

ParaList ---> ['('] <Identifier> [{ ',' <Identifier> }] [')']

Expr ---> <LogicNot> [{ 'and'|'or' <LogicNot> }]

LogicNot --> ['not'] <EqualOrNot> 

EqualOrNot --> [ '='|'!=' ] <Compare>

Compare ---> <BitOrXor> [ { '<' | '>' | '<=' | '>=' <BitOrXor> } ]

BitOrXor ---> <BitAnd> [ { '|' | '^' <BitAnd> } ]

BitAnd ---> <BitShift> [ { '&' <BitShift> } ]

BitShift ---> <Arithmetic> [ { '<<' | '>>' <Arithmetic> } ]

Arithmetic ---> <Term> [ { '+' | '-'  <Term>  } ]

Term ---> <Range> [ { '*' | '/' | '%' | '**' | '^^' <Range> } ]

Range ---> <Factor> [ ':' <Factor> ]

Factor ---> <Identifier> [ {Mixer} ] [ '=' <Expr> ]

Factor ---> <Number>

Factor ---> <Literal>

Factor ---> ':' <Identifier>

Factor ---> '-' <Factor>

Factor ---> '(' <Expr> ')'

Factor ---> <List>

Factor ---> 'new' ['from'] <Identifier>

Factor ---> <AnonymousFunction>

Factor ---> 'call' <identifier> { '.' <Identifier> } '(' <Parameters> ')'

List ---> '[' [ <Expr> { ',' <Expr> } ] ']'

Mixer ---> { '.' <Identifier> }

Mixer ---> '[' <Expr> ']'

Mixer ---> '(' [ <Expr> [ { ',' <Expr> }] ]  ')'

Mixer ---> '{' {Statement} '}'

AnonymousFunction ---> 'func'|'def'|'function' [<ParaList>] '{' {Statement} '}'

.. index:: 
	pair: Language Specification; Virtual Machine Instructions

Virtual Machine (VM) Instructions
=================================

Definitions :-

* VM: Virtual Machine
* Stack: VM Stack
* IR: Instruction Register
* PC: Program Counter
* VP: Variable Pointer
* Stack[nSize]: Last Item in the Stack (Last In - First Out)
* VV: Variable Value (We have a Pointer to a variable, And we access this variable value)
* REG1: The first register in the instruction
* REG2: The second register in the instruction  

(Stack and Variables)

=======================	============================================================================
Operation			Description
=======================	============================================================================
ICO_PUSHC 		Push string from the IR to the stack
ICO_PUSHNL		Push new line to the stack
ICO_PUSHN 		Push number from the IR to the stack
ICO_PUSH2N		Push two numbers from the IR to the stack
ICO_PUSHV 		Replace VP in the stack[nSize] with the variable value 
ICO_LOADADDRESS 	Read variable name from the IR, push VP to the stack
ICO_ASSIGNMENT		Stack[nSize-1] VV = Stack[nSize] VV , POP Stack[nSize]
ICO_INC 		Increment Number in Stack[nSize] by 1
ICO_LOADAPUSHV		The same as ICO_LOADADDRESS then ICO_PUSHV
ICO_NEWLINE		Store new line number (debug info)
ICO_FREESTACK		Remove all items from the stack , nSize = 0
ICO_FILENAME		Store the source code file name (debug info)
ICO_FREELOADASCOPE	Free the Scope List of the current Expression
=======================	============================================================================

(Jump)

=======================	============================================================================
Operation			Description
=======================	============================================================================
ICO_JUMP		Set the PC to a new value from the IR 
ICO_JUMPZERO		If Stack[nSize] is a number = 0 then Set PC to new value from the IR 
ICO_JUMPFOR		End of for loop
ICO_JUMPONE		If Stack[nSize] is a number = 1 then Set PC to new value from the IR 
ICO_JUMPZERO2		As ICO_JUMPZERO but add 1 to the stack (required for many 'AND' conditions)
ICO_JUMPONE2		As ICO_JUMPONE but add 1 to the stack (required for many 'OR' conditions)
ICO_PUSHNULLTHENJUMP	Add NULL string to the Stack then set the PC to a new value from the IR
ICO_PUSHNTHENJUMP	Add number from REG1 to the Stack then set the PC to a new value from REG2
=======================	============================================================================

(Compare)

=======================	==========================================================================================================
Operation			Description
=======================	==========================================================================================================
ICO_LESSEQUAL		If stack[nSize-1] <= stack[nSize] , POP stack[nSize], set Stack[nSize-1] = 1 else set Stack[nSize-1] = 0
ICO_EQUAL		If stack[nSize-1] = stack[nSize] , POP stack[nSize], set Stack[nSize-1] = 1 else set Stack[nSize-1] = 0
ICO_LESS		If stack[nSize-1] < stack[nSize] , POP stack[nSize], set Stack[nSize-1] = 1 else set Stack[nSize-1] = 0
ICO_GREATER		If stack[nSize-1] > stack[nSize] , POP stack[nSize], set Stack[nSize-1] = 1 else set Stack[nSize-1] = 0
ICO_GREATEREQUAL	If stack[nSize-1] >= stack[nSize] , POP stack[nSize], set Stack[nSize-1] = 1 else set Stack[nSize-1] = 0
ICO_NOTEQUAL		If stack[nSize-1] != stack[nSize] , POP stack[nSize], set Stack[nSize-1] = 1 else set Stack[nSize-1] = 0
=======================	==========================================================================================================

(Math)

=======================	============================================================================
Operation			Description
=======================	============================================================================
ICO_SUM			Stack[nSize-1] = Stack[nSize-1] +  Stack[nSize] , POP stack[nSize]
ICO_SUB			Stack[nSize-1] = Stack[nSize-1] -  Stack[nSize] , POP stack[nSize]
ICO_MUL			Stack[nSize-1] = Stack[nSize-1] *  Stack[nSize] , POP stack[nSize]
ICO_DIV			Stack[nSize-1] = Stack[nSize-1] /  Stack[nSize] , POP stack[nSize]
ICO_MOD			Stack[nSize-1] = Stack[nSize-1] %  Stack[nSize] , POP stack[nSize]
ICO_POW			Stack[nSize-1] = Stack[nSize-1] ** Stack[nSize] , POP stack[nSize]
ICO_NEG			Stack[nSize]   = - Stack[nSize-1]
ICO_PLUSPLUS		Stack[nSize]   = Stack[nSize] +  1
ICO_MINUSMINUS		Stack[nSize]   = Stack[nSize] -  1
ICO_SUMN		Stack[nSize]   = Stack[nSize] +  REG1
ICO_SUBN		Stack[nSize]   = Stack[nSize] -  REG1
ICO_MULN		Stack[nSize]   = Stack[nSize] *  REG1	
ICO_DIVN		Stack[nSize]   = Stack[nSize] /  REG1
ICO_MODN		Stack[nSize]   = Stack[nSize] %  REG1
ICO_POWN		Stack[nSize]   = Stack[nSize] ** REG1
=======================	============================================================================

(Logic)

=======================	============================================================================
Operation			Description
=======================	============================================================================
ICO_AND			Stack[nSize-1] = Stack[nSize-1] && Stack[nSize] , POP stack[nSize]
ICO_OR			Stack[nSize-1] = Stack[nSize-1] || Stack[nSize] , POP stack[nSize]
ICO_NOT			Stack[nSize]   = ! Stack[nSize]  
=======================	============================================================================

(Lists)

=======================	============================================================================
Operation			Description
=======================	============================================================================
ICO_LISTSTART		Start New List in Temp. Memory
ICO_LISTITEM		Add List Item (Value from the stack)
ICO_LISTITEMN		Add List Item (Number from REG1)
ICO_LISTITEMC		Add List Item (String from REG1)
ICO_LISTEND		End List
ICO_LOADINDEXADDRESS	Stack[nSize-1] = Stack[nSize-1] VV [ Stack[nSize] ] , POP stack[nSize]
=======================	============================================================================

(Functions)

=======================	============================================================================
Operation			Description
=======================	============================================================================
ICO_LOADFUNC		Find function
ICO_CALL		Call function
ICO_RETURN		Return from function
ICO_RETNULL		Return NULL from function
ICO_RETURNN		Return Number (REG1) from function
ICO_RETFROMEVAL		Return after eval() 
ICO_RETITEMREF		Return the list item reference - not the value
ICO_NEWFUNC		Start new function
ICO_BLOCKFLAG		Flag to determine where to jump later (after ICO_RETURN)
ICO_ANONYMOUS		Anonymous function
ICO_FREETEMPLISTS	Delete temp. lists
=======================	============================================================================

(End Program/Loop)

=======================	============================================================================
Operation			Description
=======================	============================================================================
ICO_BYE			End execution of VM
ICO_EXITMARK		Place to exit to from a loop	
ICO_POPEXITMARK		Remove exit mark
ICO_EXIT		Break from one loop or more 
ICO_LOOP		Continue to next loop 
ICO_OPTIONALLOOP	Check if we have optional loop then push 1 to the Stack and call Loop
=======================	============================================================================

(For Better Performance)

========================	============================================================================
Operation			Description
========================	============================================================================
ICO_PUSHP			Push pointer to the stack
ICO_INCP			Increment variable value using pointer
ICO_PUSHPV			Push value of variable using variable pointer
ICO_INCJUMP			Increment then jump
ICO_INCPJUMP			Increment using pointer then jump
ICO_LOADFUNCP			Push function pointer
ICO_PUSHPLOCAL			Push pointer to local variable
ICO_PUSHARG			Push pointer to argument variable using argument number (REG1)
ICO_INCLPJUMP			Increment value using pointer to local variable then jump
ICO_INCPJUMPSTEP1	 	Increment value using variable pointer then jump (for loop step = 1)
ICO_INCLPJUMPSTEP1		Increment value using local variable pointer then jump (for loop step = 1)
ICO_LEN				Remove list from the stack and push the list size
========================	============================================================================

(Try-Catch-Done)

=======================	============================================================================
Operation			Description
=======================	============================================================================
ICO_TRY			Start try region
ICO_DONE		End try region
=======================	============================================================================

(Duplicate and Range)

=======================	============================================================================
Operation			Description
=======================	============================================================================
ICO_DUPLICATE		Duplicate stack value
ICO_RANGE		Create list from value to value
=======================	============================================================================

(OOP)

=======================	============================================================================
Operation			Description
=======================	============================================================================
ICO_NEWOBJ		Create new object, get class name from the IR, push object pointer to the stack.
ICO_SETSCOPE		Called after creating new object, set the active scope to be the object scope.
ICO_LOADSUBADDRESS	Get object attribute, push the pointer to the stack.
ICO_LOADMETHOD		Find object method 
ICO_AFTERCALLMETHOD	Used after calling a method - normal case
ICO_AFTERCALLMETHOD2	Used after calling a method - second case
ICO_PACKAGE		Start new package 
ICO_NEWCLASS		Start new class region
ICO_BRACESTART		Open brace
ICO_BRACEEND		End brace
ICO_IMPORT		Import package
ICO_NEWLABEL		Start a new section of byte code (Just for organization)
ICO_PRIVATE		Start private attributes region
ICO_SETPROPERTY		Set attribute value - check for setter	
ICO_CALLCLASSINIT	Call class init() method
ICO_CHECKBRACEMETHOD	Check if a method exist using braces
=======================	============================================================================

(Other)

=======================	============================================================================
Operation			Description
=======================	============================================================================
ICO_SETREFERENCE	Copy by reference
ICO_KILLREFERENCE	Remove reference
ICO_ASSIGNMENTPOINTER 	Determine the left side variable
ICO_BEFOREEQUAL		Determine operators like += , -= , ... etc
=======================	============================================================================

(Bitwise Operators)

=======================	============================================================================
Operation			Description
=======================	============================================================================
ICO_BITAND		Stack[nSize-1] = Stack[nSize-1] & Stack[nSize] , POP stack[nSize]
ICO_BITOR		Stack[nSize-1] = Stack[nSize-1] | Stack[nSize] , POP stack[nSize]
ICO_BITXOR		Stack[nSize-1] = Stack[nSize-1] ^ Stack[nSize] , POP stack[nSize]
ICO_BITNOT		Stack[nSize] = ! Stack[nSize] 
ICO_BITSHL		Stack[nSize-1] = Stack[nSize-1] << Stack[nSize] , POP stack[nSize]
ICO_BITSHR		Stack[nSize-1] = Stack[nSize-1] >> Stack[nSize] , POP stack[nSize]
=======================	============================================================================

(For Step)

=======================	============================================================================
Operation			Description
=======================	============================================================================
ICO_STEPNUMBER		Determine step number in for loop (Read step value from stack)
ICO_STEPFROMREG		Determine step number in for loop (Read step value from REG1)
ICO_POPSTEP		POP step number from steps stack
ICO_LOADAFIRST		Load the first address of variable name 
=======================	============================================================================

(Custom Global Scope)

=======================	============================================================================
Operation			Description
=======================	============================================================================
ICO_NEWGLOBALSCOPE	Start new custom global scope - used by 'load package' command
ICO_ENDGLOBALSCOPE	End of custom global scope - used by 'load package' command
ICO_SETGLOBALSCOPE	Set the current global scope 
=======================	============================================================================

(More Instructions)

=======================	============================================================================
Operation			Description
=======================	============================================================================
ICO_NOOP		No operation 
ICO_SETOPCODE		Change the operation code of a specific instruction 
=======================	============================================================================
