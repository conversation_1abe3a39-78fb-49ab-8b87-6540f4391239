* If Statement
.. code-block:: ring

	if Expression
		Block of statements
	but Expression
		Block of statements
	else
		Block of statements
	ok
Example:
	see " 
		Main Menu
		---------
		(1) Say Hello
		(2) About
		(3) Exit
	    " give nOption
	if nOption = 1	see "Enter your name : " give name see "Hello " + name + nl
	but nOption = 2 see "Sample : using if statement" + nl
	but nOption = 3 bye
	else see "bad option..." + nl
	ok
              
.. index:: 
	pair: Control Structures - First Style; Switch Statement


* Practical language designed for creating the next version of the Programming Without Coding Technology software.
.. code-block:: ring

	See "Hello, World!" 
The Main function is optional and will be executed after the statements, and is useful for using the local scope.
	Func Main
		See "Hello, World!" 
		
Uses Dynamic Typing and Lexical scoping. No $ is required before the variable name! 
You can use the '+' operator for string concatenation and the language is weakly typed and will convert automatically between numbers and strings based on the context.
	nCount = 10	# Global variable
	Func Main
		nID = 1	# Local variable
		See "Count = " + nCount + nl + " ID = " + nID
.. index:: 
	pair: Language Design; Trying to be natural
Trying to be natural
====================
Ring is not case-sensitive
	See "Enter your name ? " 
	Give name
	See "Hello " + Name	# Name is the same as name 
			
The list index starts from 1
	aList = ["one","two","three"]
	See aList[1]	# print one
			
Call functions before definition
	one() 
	two() 
	three()
	Func one 
		See "One" + nl
	Func two 
		See "two" + nl
	Func three 
		See "three" + nl
			
The assignment operator uses Deep copy (no references in this operation)
	aList = ["one","two","three"]
	aList2 = aList
	aList[1] = 1
	see alist[1]	# print 1
	see aList2[1]	# print one
				
Pass numbers and strings by value, but pass lists and objects by reference. 
The for in loop can update the list items.
	Func Main
		aList = [1,2,3]
		update(aList)
		see aList	# print one two three
	Func update aList
		for x in aList
			switch x
			on 1 x = "one"
			on 2 x = "two"
			on 3 x = "three"
			off
		next
			
Using Lists during definition
	aList = [ [1,2,3,4,5] , aList[1] , aList[1] ]
	see aList       # print 1 2 3 4 5 1 2 3 4 5 1 2 3 4 5
			
Exit from more than one loop
	for x = 1 to 10
			for y = 1 to 10
					see "x=" + x + " y=" + y + nl
					if x = 3 and y = 5
							exit 2     # exit from 2 loops
					ok
			next
	next
	
.. index:: 
	pair: Language Design; Encourage Organization
Encourage Organization
======================
The language encourage organization, Forget bad days using languages where the programmer start with function then class then function and a strange mix between things! 
Each source file follow the next structure 


* Numbers and Bytes
.. code-block:: ring

	Read(cFileName) ---> String contains the file content
Example:
	see read("myfile.txt")
The read function can read binary files too
Example:
	see read("myapp.exe")
.. index:: 
	pair: Files; Write file using Write()
Write() Function
================
We can write string to file using the Write() function
The write function can write binary data to binary files.
Syntax:
	Write(cFileName,cString)	# write string cString to file cFileName
Example:
	# copy file
	cFile = read("ring.exe")
	write("ring2.exe",cFile)
.. index:: 
	pair: Files; Dir()
Dir() Function
==============
We can get the folder contents (files & sub folders) using the Dir() function.
Syntax:
	Dir(cFolderPath) ---> List contains files & sub folders.
This function returns a list and each list item is a list of two items


* For in Loop
.. code-block:: ring

	for identifier in List/String  [step expression]
		Block of statements
	end
Example:
	aList = 1:10	# create list contains numbers from 1 to 10
	for x in aList  put x + nl  end  # print numbers from 1 to 10
.. index:: 
	pair: Control Structures - Second Style; Exceptions
Exceptions
==========
	try
		Block of statements
	catch
		Block of statements
	end


* GetArch() Function
.. code-block:: ring

	switch getarch()
	on "x86"
	        ? "x86 32bit architecture"
	on "x64"
	        ? "x64 64bit architecture"
	on "arm64"
	        ? "ARM64 64bit architecture"
	on "arm"
	        ? "ARM 32bit architecture"
	other
	        ? "Unknown architecture"
	off


* Form Designer
.. code-block:: ring

	Load "guilib.ring"
	New qapp 
	{
		win1 =  new qwidget() 
		{
			setwindowtitle("Drawing using QPixMap")
			setgeometry(100,100,500,500)
			label1 = new qlabel(win1) 
			{
				setgeometry(10,10,400,400)
				settext("")
			}
			imageStock = new qlabel(win1) 
			{               
				image = new qPixMap2(200,200)   
				color = new qcolor() {
					setrgb(255,255,255,255)
				}
				pen = new qpen() {
					setcolor(color)
					setwidth(10)
				}
				new qpainter() {
					begin(image)
						setpen(pen)
						drawline(0,0,200,200)
						drawline(200,0,0,200)
					endpaint()
				}
				setpixmap(image)   
			}   
			show()
		}
		exec()
	}
Screen Shot:
.. image:: ringqtpixmap2.png
	:alt: Using QPixMap2
(2) The Objects Library is updated to include the next functions


* Pointer to the package (or NULL if no package is used)
.. code-block:: ring

	RingVM_ClassesList() ---> List
Example:
	see ringvm_classeslist()
	class class1
		func f1
	class class2 from class1
	class class3 from class1
Output:
	class1
	9
	f1
	13
	B:/ring/tests/scripts/classeslist.ring
	0
	0
	00000000
	class2
	16
	class1
	0
	00000000
	class3
	20
	class1
	0
	00000000
.. index:: 
	pair: Low Level Functions; RingVM_PackagesList()
ringvm_packageslist() function
==============================
The Function return a list of Packages.
Each List Member is a list contains the next items


* Better Generated Code
.. code-block:: ring

	class Person
		name		# Define name as attribute if it's not a global variable
		address
		phone
	class person2
        	self.name	# Must Define the attribute
	        self.address
	        self.phone
.. index:: 
	pair: What is new in Ring 1.1?; Using This.Attribute in nested Braces inside the Class Methods
Using This.Attribute in nested Braces inside the Class Methods
==============================================================
We can use nested braces {} while we are inside methods to access another objects, In this case 
the current object scope will be changed while we are inside the brace and Self will point to the object
that we access using braces {}. In this case we can use This.Attribute and This.Method() to access the object
that will be created from the current class.
Check the Object Oriented Programming chapter for more information.
Also Check the Weight History Application in GUI Development using RingQt chapter.
.. index:: 
	pair: What is new in Ring 1.1?; Better Documentation
Better Documentation
====================
Ring 1.1 documentation (800 pages) is better than Ring 1.0 documentation (340 pages)
Many chapters are added for providing better information about the language like:


* In Windows - Define the next Environment Variables based on your system.
.. code-block:: ring

	
	For Example : C:\Program Files (x86)\Java\jdk1.8.0_05 
(2) ANDROID_HOME	
	For Example : B:\mahmoud\Tools\Java-Android\adt-bundle-windows-x86-20140702\sdk
.. index:: 
	pair: Building Games For Android; Project Folder
Project Folder
==============
Open the project folder : ring/extensions/android/ringlibsdl/project
.. image:: ringlibsdlandroid_shot1.png
	:alt: RingLibSDL for Android
You can add the source code (*.ring) and Images/Sound Files to the assets folder.
.. image:: ringlibsdlandroid_shot2.png
	:alt: RingLibSDL for Android
You will find the Flappy Bird 3000 Game ready for building.
The execution starts from the start.ring file
	load "game2.ring"
.. index:: 
	pair: Building Games For Android; Building the project
Building the project
====================
Move to the ring/extensions/android/ringlibsdl/project folder 
We can build using the next command (We need to do this for one time only).
.. code-block:: none
	ndk-build
Then we can create the package (*.apk) using the next command.
.. code-block:: none
	ant debug
We can write a batch file for building the project (file: build.bat)
.. code-block:: none
	rem You will need to modify this batch file based on your environment
	set JAVA_HOME=C:\Program Files (x86)\Java\jdk1.8.0_05
	set ANDROID_HOME=B:\mahmoud\Tools\JavaAndroid\adt-bundle-windows-x86-20140702\sdk
	set NDK_ROOT=B:\mahmoud\Tools\JavaAndroid\android-ndk-r10c
	set path=%path%;B:\mahmoud\Tools\JavaAndroid\android-ndk-r10c
	set path=%path%;B:\mahmoud\Tools\JavaAndroid\apache-ant-1.9.4\bin
	ndk-build


* JustFileName()
.. code-block:: ring

	Loop|Exit [Number]
Changed to
	Loop|Exit [Expression]
Example
	XLoop = 2	# The outer loop 
	YLoop = 1	# The first inner loop
	for x = 1 to 10
        	for y = 1 to 10
                	see "x=" + x + " y=" + y + nl
	                if x = 3 and y = 5
        	                exit XLoop  
                	ok
	        next
	next
.. index:: 
	pair: What is new in Ring 1.3?; New Functions
New Functions
=============


* For Loop
.. code-block:: ring

	for identifier=expression to expression [step expression]
		Block of statements
	next
Example:
	# print numbers from 1 to 10
	for x = 1 to 10	 see x + nl  next
Example:
	# Dynamic loop
	See "Start : " give nStart  nStart = 0+nStart       
	See "End   : " give nEnd    nEnd   = 0+nEnd
	See "Step  : " give nStep   nStep  = 0+nStep
	For x = nStart to nEnd Step nStep
		see x + nl
	Next
Example:
	# print even numbers from 0 to 10
	for x = 0 to 10 step 2
		see x + nl
	next
Example:
	# print even numbers from 10 to 0
	for x = 10 to 0 step -2
		see x + nl
	next
.. index:: 
	pair: Control Structures - First Style; For In Loop


* Update main.ring to use the LEDs project
.. code-block:: ring

	load "projects/leds/main.ring"


* Folder : ring/samples/other/UsingQt3D (Contains 18 samples)
.. code-block:: ring

	load "raylib.ring"
	screenWidth 	= 800
	screenHeight 	= 450
	InitWindow(screenWidth, screenHeight, "raylib [core] example - basic window")
	SetTargetFPS(60)
	while !WindowShouldClose() 
		BeginDrawing()
		ClearBackground(RED)
		DrawText("Congrats! You created your first window!", 190, 200, 20, WHITE)
	        EndDrawing()
	end
	CloseWindow()
Output:
.. image:: raylib_ex1.png
	:alt: RayLib Example
Example:
	load "raylib.ring"
	screenWidth = 800
	screenHeight = 450
	InitWindow(screenWidth, screenHeight, "raylib [shapes] example - basic shapes drawing")
	SetTargetFPS(60)
	while !WindowShouldClose()
		BeginDrawing()
		ClearBackground(RAYWHITE)
		DrawText("some basic shapes available on raylib", 20, 20, 20, DARKGRAY)
		DrawCircle(screenWidth/4, 120, 35, DARKBLUE)
		DrawRectangle(screenWidth/4*2 - 60, 100, 120, 60, RED)
		DrawRectangleLines(screenWidth/4*2 - 40, 320, 80, 60, ORANGE)  
		DrawRectangleGradientH(screenWidth/4*2 - 90, 170, 180, 130, MAROON, GOLD)
		DrawTriangle(Vector2(screenWidth/4*3, 80),
				 Vector2(screenWidth/4*3 - 60, 150),
				 Vector2(screenWidth/4*3 + 60, 150), VIOLET)
		DrawPoly(Vector2(screenWidth/4*3, 320), 6, 80, 0, BROWN)
		DrawCircleGradient(screenWidth/4, 220, 60, GREEN, SKYBLUE)
		DrawLine(18, 42, screenWidth - 18, 42, BLACK)
		DrawCircleLines(screenWidth/4, 340, 80, DARKBLUE)
		DrawTriangleLines(Vector2(screenWidth/4*3, 160),
				  Vector2(screenWidth/4*3 - 20, 230),
				  Vector2(screenWidth/4*3 + 20, 230), DARKBLUE)
		EndDrawing()
	end
	CloseWindow()
Output:
.. image:: ex1_basicshapes.png
	:alt: RayLib Example
.. index:: 
	pair: What is new in Ring 1.11?; ZeroLib Library
ZeroLib Library
===============
Ring 1.11 comes with the ZeroLib library that contains classes for Lists and Strings where the index starts from 0.
Example:
	load "zerolib.ring"
	? "Using List - Index start from 0"
	List = Z( [1,2,3] )
	List.Add(4)
	List.Add(5)
	? List[0]
	? List[1]
	? List[2]
	? List[3]
	? List[4]
	nIndex = List.find(2)
	? "Find(2) = " + nIndex
	List.delete(0)
	? "After deleting the first item : List[0]" 
	? "Now List[0] = " + List[0] 
	? "Using String - Index start from 0"
	String = Z( "Welcome" )
	? String[0]
	? String[1]
	? String[2]
	? String[3]
	? String[4]
	? String[5]
	? String[6]
Output:
	Using List - Index start from 0
	1
	2
	3
	4
	5
	Find(2) = 1
	After deleting the first item : List[0]
	Now List[0] = 2
	Using String - Index start from 0
	W
	e
	l
	c
	o
	m
	e
	
.. index:: 
	pair: What is new in Ring 1.11?; StdLib - More Functions
StdLib - More Functions
=======================
The next functions are added to the StdLib


* TreeWidget
.. code-block:: ring

	load "guilib.ring"
	import System.GUI
This doesn't have any effect on our previous code, It's just another choice for better code that is consistent with Ring rules.
Also the form designer is updated to provide us the choice between using classes where (index start from 0) or (index start from 1)
Example  (Uses the Form Designer)
(1) https://github.com/ring-lang/ring/blob/master/samples/UsingFormDesigner/indexstart/indexstartView.ring
(2) https://github.com/ring-lang/ring/blob/master/samples/UsingFormDesigner/indexstart/indexstartController.ring 
.. index:: 
	pair: Desktop, WebAssembly and Mobile Development; TableWidget - AddList() Method
TableWidget - AddList() Method
==============================
Using this method we can add Ring List to the TableWidget
.. tip:: TableWidget class is a subclass of QTableWidget class
.. note:: To use TableWidget class, import system.gui after loading guilib.ring or lightguilib.ring
Example:
Source code: https://github.com/ring-lang/ring/tree/master/samples/UsingQt/TableWidget/AddRingList
	class addRingListController from windowsControllerParent
		oView = new addRingListView
		aList = [["one","two"],
			 ["three","four"],
			 ["five","six"],
			 [7,8],
			 ["I","Love","Ring","Programming"]]
		
		oView.tablewidget1.addList(aList)
		aList = [["Number","Square"]]
		for t = 1 to 10
			aList + [ t, t*t]
		next
		oView.tablewidget1.addList(aList)
Screen Shot:
.. image:: addlisttotablewidget.png
	:alt: addlisttotablewidget
.. index:: 
	pair: Desktop, WebAssembly and Mobile Development; Creating Reports using the WebLib and the GUILib
Creating Reports using the WebLib and the GUILib 
================================================
The WebLib comes with a class called HtmlPage
Using this class we can create reports quickly using WebLib & GUILib together
Example:
	load "stdlib.ring"
	load "weblib.ring"
	load "guilib.ring"
	import System.Web
	import System.GUI
	new qApp {
		open_window(:CustomersReportController)
		exec()
	}
	class CustomersReportController from WindowsControllerParent
		oView = new CustomersReportView
		func Start
			CreateReport()
		func CreateReport
			mypage = new HtmlPage {
				h1 { text("Customers Report") }
				Table
				{
					style = stylewidth("100%") + stylegradient(4)
					TR
					{
						TD { WIDTH="10%" 
							text("Customers Count : " )  }
						TD { text (100) }
					}
				}
				Table
				{
					style = stylewidth("100%") + stylegradient(26)
					TR
					{
						style = stylewidth("100%") +
							stylegradient(24)
						TD { text("Name " )  }
						TD { text("Age" ) }
						TD { text("Country" ) }
						TD { text("Job" ) }	
						TD { text("Company" ) }
					}
					for x =  1 to 100
						TR
						{
							TD { text("Test" )  }
							TD { text("30" ) }
							TD { text("Egypt" ) }
							TD { text("Sales" ) }	
							TD { text("Future" ) }
						}
					next
				}
			}
			write("report.html",mypage.output())
		func PrintEvent
			printer1 = new qPrinter(0) {
				setoutputformat(1)
				setoutputfilename("report.pdf")
			}
			oView {
				web.print(printer1, Method( :OpenPDF ) )
				web.show()
			}
		func OpenPDF
			new QDesktopServices { 
				OpenURL(new qURL("report.pdf") ) 
			} 
	class CustomersReportView
			win = new window() {
					setwindowtitle("Report Window")
					setgeometry(100,100,500,500)
					web = new webview(win) {
						setgeometry(100,100,1000,500)
						loadpage(new qurl("file:///"+
						currentdir()+"/report.html"))
					}
					new pushbutton(win) {
							setGeometry(100,20,100,30)
							settext("Print")
							setclickevent(Method(:PrintEvent))
					}
					showMaximized()
				}
Screen Shot:
.. image:: ring15reportshot.png
	:alt: Customers Report


* file: ringapp/datalib.ring (Class: Database)
.. code-block:: ring

	#!ring -cgi 
	
Then run it from Ring Notepad (Ctrl+F6)


* Type  ---> What we can do with what we have or how we do things with what we have (Just a Logical Concept)
.. code-block:: ring

	? len( int2bytes(1) )
	? len( float2bytes(1) )
	? len( double2bytes(1) )
Output:
.. code-block:: none
	4
	4
	8
(4) Storing Numbers
When we use a number, Ring always use the (Double) data type for representing these numbers in memory.
This is important to know when we do arithmetic operations on numbers.
But when we convert the number to a String using "" + number  or using string(number) we get a string where each digit is represented in 1 byte (Not good idea for storage, but useful for string processing)
If you need the number to be represented in specific size (int|float|double) for storage then use bytes2int() , bytes2float() and bytes2double() when writing the data to binary files.
Ring Number (double) ----> int2bytes()  - will cast the number from double to int then return the bytes ----> 4 bytes (Ring String)
Ring Number (double) ----> float2bytes()  - will cast the number from double to float then return the bytes ----> 4 bytes (Ring String)
Ring Number (double) ----> double2bytes()  - will use the number (double) to return the bytes ----> 8 bytes (Ring String)
 
The (int) type is used only for internal Ring operations, but Ring applications|code will use only the (double) type for numbers.
(5) The Unsigned() Function
The function unsigned() expect the first and the second parameters as numbers
	unsigned(nNumber1,nNumber2,cOperator)
We can use the bytes2int() function to convert the bytes to a number
Example:
	B = list(4)
	for k=1 to 4
	{  
		B[k]= Space(4)
		for kk=1 to 4 { B[k][kk]= char(60+4*k +kk) }
		? " B" +k +": " +B[k]
	}
	A12= Space(4)     A12= bytes2int(B[1]) ^ bytes2int(B[2])      		
	? "A12: " +string(A12)  
	A34= Space(4)     A34= bytes2int(B[3]) ^ bytes2int(B[4])      		
	? "A34: " +string(A34)
	A12= space(4)     A12= Unsigned(bytes2int(B[1]),bytes2int(B[2]),"^") 	
	? "unsigned A12: " +A12
	A34= space(4)     A34= Unsigned(bytes2int(B[3]),bytes2int(B[4]),"^") 	
	? "unsigned A34: " +A34
Output:
.. code-block:: none
	B1: ABCD
	B2: EFGH
	B3: IJKL
	B4: MNOP
	A12: 201589764
	A34: 470025220
	unsigned A12: 201589764
	unsigned A34: 470025220


* Equality of functions
.. code-block:: ring

	Func Main
		aList = [1,2,3,4,5]
		aList2 = square(aList)
		see "aList" + nl
		see aList
		see "aList2" + nl
		see aList2
	Func Square aPara
		a1 = aPara		# copy the list
		for x in a1
			x *= x
		next
		return a1		# return new list
		
Output:
	aList
	1
	2
	3
	4
	5
	aList2
	1
	4
	9
	16
	25
.. index:: 
	pair: Functional Programming; First-Class Functions
First-class Functions
=====================
Functions inside the Ring programming language are first-class citizens, you can pass functions as parameters, return them as
value or store them in variables.
We can pass/return the function by typing the function name as literal like "FunctionName" or :FunctionName for example.
We can pass/return functions using the variable that contains the function name.
We can call function from variables contains the function name using the Call command
Syntax:
	Call Variable([Parameters])
Example:
	Func Main
		see "before test2()" + nl
		f = Test2(:Test)
		see "after test2()" + nl
		call f()
	Func Test
		see "Message from test!" + nl
	Func Test2 f1
		call f1()
		See "Message from test2!" + nl
		return f1	
Output:
	before test2()
	Message from test!
	Message from test2!
	after test2()
	Message from test!
.. index:: 
	pair: Functional Programming; Higher-order Functions
Higher-order Functions
======================
Higher-order functions are the functions that takes other functions as parameters.
Example:
	Func Main
		times(5,:test)
	Func Test
		see "Message from the test function!" + nl
	Func Times nCount,F
		for x = 1 to nCount
			Call F()
		next	
Output:
	Message from the test function!
	Message from the test function!
	Message from the test function!
	Message from the test function!
	Message from the test function!
.. index:: 
	pair: Functional Programming; Anonymous and Nested Functions
Anonymous and Nested Functions
==============================
Anonymous Functions are functions without names that can be passed as parameters to other functions or stored in variables.
Syntax:
	Func [Parameters] { [statements] }
Example:
	test( func x,y { 
				see "hello" + nl
				see "Sum : " + (x+y) + nl
		       } )
	new great { f1() }
	times(3, func { see "hello world" + nl } )
	func test x
		call x(3,3)
		see "wow!" + nl
	func times n,x
		for t=1 to n
			call x()
		next
	Class great
		func f1
			f2( func { see "Message from f1" + nl } )
		func f2 x
			call x()
Output:
	hello
	Sum : 6
	wow!
	Message from f1
	hello world
	hello world
	hello world
Example:
	Func Main
		aList = [1,2,3,4]
		Map (aList , func x { 
					return x*x 
				    } )
		see aList
		aList = [4,9,14,25]
		Map(aList, :myfilter )
		see aList
		aList = [11,12,13,14]
		Map (aList , func x {
			if x%2=0
				return "even"
			else
				return "odd"
			ok
		})
		see aList
	Func myfilter x
		if x = 9
			return "True"
		else
			return "False"
		ok
	Func Map aList,cFunc
		for x in aList
			x = call cFunc(x)
		next
Output:
	1
	4
	9
	16
	False
	True
	False
	False
	odd
	even
	odd
	even
.. index:: 
	pair: Functional Programming; Equality of functions
Equality of functions
=====================
We can test if function = function or not using the '=' or '!=' operators
Example:
	f1 = func { see "hello" + nl }
	f2 = func { see "how are you?" + nl }
	f3 = f1
	call f1()
	call f2()
	call f3()
	see (f1 = f2) + nl
	see (f2 = f3) + nl
	see (f1 = f3) + nl
Output:
	hello
	how are you?
	hello
	0
	0
	1


* Log() Function
.. code-block:: ring

	? log(10)	# 2.30
	? log(2)	# 0.69
	? log(10,10)	# 1
	? log(2,2)	# 1
	? log(100,10)	# 2
.. index:: 
	pair: What is new in Ring 1.14?; Better Performance For Strings
Better Performance For Strings
==============================


* You can build your application using Qt Creator
.. code-block:: ring

	if isandroid()
 		mypic = new QPixmap(":/cards.jpg")
	else
    		mypic = new QPixmap("cards.jpg")
	ok
.. index:: 
	pair: Building RingQt Applications for Mobile; Comments about developing for Android using RingQt
Comments about developing for Android using RingQt
==================================================
(1) The main project file is main.cpp 
	This file load Ring Compiler/Virtual Machine and RingQt 
	Then get the Ring Object File during the runtime from the resources
	Then run the Ring Object File (ringapp.ringo) using the Ring VM 
	Through main.cpp you can extract more files from the resources to temp. folder once you
	add them (create projects with many files).
(2) The next functions are missing from this Ring edition
	* Database (ODBC, SQLite & MySQL)
	* Security and Internet functions (LibCurl & OpenSSL)
	* RingAllegro (Allegro Library)
	* RingLibSDL (LibSDL Library)
	Just use Qt Classes through RingQt.
	For database access use the QSqlDatabase Class
.. note:: All of the missing libraries (LibCurl, OpenSSL & Allegro) can be compiled for Android, but they are not included in this Qt project.
(3) use if isandroid() when you want to modify the code just for android
Example:
	if isandroid()
		// Android code
	else
  		// other platforms
	ok
(4) Sometimes you will find that the button text/image is repeated in drawing !
it's Qt problem that you can avoid using the next code.
	if isandroid()
		setStyleSheet("
			border-style: outset;
			border-width: 2px;
			border-radius: 4px;
			border-color: black;
			padding: 6px;")
	 ok
(5)  Always use Layouts instead of manual setting of controls position and size. 
This is the best way to get the expected user interface to avoid problems like (controls with small/extra size)
(6) When you deal with Qt Classes you can determine the images from resources (you don't need to copy them using main.cpp)
Example: 
	if isandroid()
	    mypic = new QPixmap(":/cards.jpg")
	else
	    mypic = new QPixmap("cards.jpg")
	ok
Now RingQt comes with the AppFile() function to determine the file name 
Example:
	mypic = new QPixmap(AppFile("cards.jpg"))  # Desktop or Android
(7) When you update your project code, You don't have to use Ring2EXE to generate the Qt project again
Just use the Distribute Menu in Ring Notepad and select (Generate Ring Object File)
Then copy the YourAppName.ringo file to target/mobile/qtproject folder and accept replacing files.
(8) If your application folder contains a Qt resource file (project.qrc)
Then when you use Ring2EXE or Ring Notepad (Distribute - Prepare Qt project for Mobile devices) the 
resource file will be used 
See ring/applications/cards game as an example.


* OSRenameFile(cOldFileName,cNewFileName) : Rename file 
.. code-block:: ring

	see :one + nl + tab + :two + nl + tab + tab + :three
Output:
.. code-block:: none
	one
	        two
	                three
You can change the variable to another value
Example (2):
	tab = "  "
	see :one + nl + tab + :two + nl + tab + tab + :three
Output:
.. code-block:: none
	one
	  two
	    three
.. index:: 
	pair: What is new in Ring 1.6?; Using CR as Carriage return
Using CR as Carriage return
===========================
The next example count from 1 to 10 in the same line during 10 seconds
	load "stdlibcore.ring"
	for x = 1 to 10 see x sleep(1) see cr next
.. index:: 
	pair: What is new in Ring 1.6?; Using the ! operator as not
Using the ! operator as not
===========================
We have = and != in the Ring language
But for the logical operators we have and, or & not
Now we can use the ! operator as not
Example:
	if ! false
		see "True!" + nl
	ok
Output
.. code-block:: none
	True!
.. index:: 
	pair: What is new in Ring 1.6?; Using && and || operators
Using && and || operators
=========================
In Ring we have the next keywords for the logical operations


* Switch Statement
.. code-block:: ring

	switch Expression
	on Expression
		Block of statements
	other
		Block of statements
	off		
Example:
	See " 
		Main Menu
		---------
		(1) Say Hello
		(2) About
		(3) Exit
	    " Give nOption
	Switch nOption
	On 1 See "Enter your name : " Give name See "Hello " + name + nl
	On 2 See "Sample : using switch statement" + nl
	On 3 Bye
	Other See "bad option..." + nl
	Off
.. index:: 
	pair: Control Structures - First Style; Looping
Looping
=======
.. index:: 
	pair: Control Structures - First Style; While Loop


* More Improvements
.. code-block:: ring

	load "tokenslib.ring"
	func main
		oTokens = new RingTokens {
			fromFile("hello.ring")
			PrintTokens()
			? Copy("=",50)
			fromString("? 1+1")
			PrintTokens()
		}
Output:
.. code-block:: none
	Keyword     : SEE
	Literal     : Hello, World!
	EndLine
	==================================================
	Operator    : ? (23)
	Number      : 1
	Operator    : + (1)
	Number      : 1
	EndLine
.. index:: 
	pair: What is new in Ring  1.17; CSVLib Library
CSVLib Library
==============
Using this library we can generate and read CSV files 
Functions:
.. code-block:: none
	List2CSV(aList) --> cCSVString
	CSV2List(cCSVString) --> aList
Example:
	load "csvlib.ring"
	aList = [ ["number", "square" ] ]
	for t=1 to 10
		aList + [ t, t*t ]
	next
	
	write( "squares.csv", list2CSV(aList) )
Output:
.. image:: squarestable.png
	:alt: squarestable
Example (2)
	load "csvlib.ring"
	if ! fexists("squares.csv")
		? "The file squares.csv doesn't exist! - Run writeSquaresTable.ring to create it"
		return
	ok
	
	aList = CSV2List( read("squares.csv") )
	
	for subList in aList 
		? "" + subList[1] + " - " + subList[2]
	next
Output:
.. code-block:: none
	number - square
	1 - 1
	2 - 4
	3 - 9
	4 - 16
	5 - 25
	6 - 36
	7 - 49
	8 - 64
	9 - 81
	10 - 100
.. index:: 
	pair: What is new in Ring  1.17; JSONLib Library
JSONLib Library
===============
Using this library we can generate and read JSON files 
Functions:
.. code-block:: none
	List2JSON(aList) --> cJSONString
	JSON2List(cJSONString) --> aList
Example (1):
File: sample.json
.. code-block:: none
	{
	  "firstName": "John",
	  "lastName": "Smith", 
	  "age": 20,
	  "address": {
		"streetAddress": "21 2nd Street",
		"city": "New York",
		"state": "NY",
		"postalCode": "10021"
	  },
	  "phoneNumbers": [
		{ "type": "home", "number": "************" },
		{ "type": "fax", "number": "************" }
	  ]
	}
Ring Code:
.. code-block:: none
	load "jsonlib.ring"
	
	func main
	
		aList = JSON2List( read("sample.json") )
	
		? aList[:FirstName]
		? aList[:LastName]
		? aList[:Age]
		? aList[:Address][:city]
		? aList[:phoneNumbers][1][:Type]
		? aList[:phoneNumbers][1][:Number]
		? aList[:phoneNumbers][2][:Type]
		? aList[:phoneNumbers][2][:Number]
Output:
.. code-block:: none
	John
	Smith
	20
	New York
	home
	************
	fax
	************
Example (2):
	load "jsonlib.ring"
	
	func main
	
		aList = [
			:name = "Ring",
			:year = 2016
		]
	
		? List2JSON(aList)
Output:
.. code-block:: none
	{
	        "name": "Ring",
	        "year": 2016
	}
JSONLib uses RingCJSON to read JSON files at high-performance
This RingCJSON extension support the CJSON library
CJSON URL: https://github.com/DaveGamble/cJSON
.. index:: 
	pair: What is new in Ring  1.17; HTTPLib Library
HTTPLib Library
===============
This library provides support for HTTP based on cpp-httplib
URL: https://github.com/yhirose/cpp-httplib
Example:
	load "httplib.ring"
	
	oServer = new Server {
	
		? "Try localhost:8080/hi"
		route(:Get,"/hi",:mytest)
		
		? "Listen to port 8080"
		listen("0.0.0.0", 8080)
	}
	
	func mytest 
		oServer.setContent("Hello World!", "text/plain")
The other examples exist in this folder: ring/samples/UsingHTTPLib
See the (Using HTTPLib) chapter for more information.
.. index:: 
	pair: What is new in Ring  1.17; Better GUILib
Better GUILib
=============
(1) Many deprecated methods are removed from RingQt 
(2) Added: AddList() method to the TableWidget class 
.. tip:: TableWidget class is a subclass of QTableWidget class
.. note:: To use TableWidget class, import system.gui after loading guilib.ring or lightguilib.ring
Example:
Source code: https://github.com/ring-lang/ring/tree/master/samples/UsingQt/TableWidget/AddRingList
	class addRingListController from windowsControllerParent
		oView = new addRingListView
		aList = [["one","two"],
			 ["three","four"],
			 ["five","six"],
			 [7,8],
			 ["I","Love","Ring","Programming"]]
		
		oView.tablewidget1.addList(aList)
		aList = [["Number","Square"]]
		for t = 1 to 10
			aList + [ t, t*t]
		next
		oView.tablewidget1.addList(aList)
Screen Shot:
.. image:: addlisttotablewidget.png
	:alt: addlisttotablewidget
(3) Added: toList() method to the tableWidget class 
Example:
	aList = oView.tablewidget1.toList()
	? aList
(4) Added: AddList() method to the ListWidget class 
.. tip:: ListWidget class is a subclass of QListWidget class
.. note:: To use ListWidget class, import system.gui after loading guilib.ring or lightguilib.ring
Example:
		aList = [ 1:10, "one", "two", "three"]
		oView.listWidget1.addList(aList)
(5) Added: toList() method to the ListWidget class 
Example:
	aList = oView.listwidget1.toList()
	? aList
.. index:: 
	pair: What is new in Ring  1.17; Better RingOpenSSL
Better RingOpenSSL
==================


* Everything evaluates to True except 0 (False), NULL (Empty String), Empty List and Lists that wrap C pointer where the pointer value is NULL.
.. code-block:: ring

	# output = message from the if statement
	if 5 	# 5 evaluates to true because it's not zero (0).
		see "message from the if statement" + nl
	ok


* updateList() function (from fastpro.ring) - support dest. column as six parameter
.. code-block:: ring

	cStr = "Welcome to Ring"
	? reverse(cStr)			# gniR ot emocleW
Example (2):
	aList = 1:3
	add(aList,4:6)			# Add the list as one item
	? len(aList)			# 4 
	aList = 1:3
	add(aList,4:6,True)		# Add each item alone
	? len(aList)			# 6 
	? aList
Example (3):
	aList = 1:10
	cStr  = list2str(aList,6,10)
	? cStr				# 6 7 8 9 10
Example (4):
	aList1 = 4:6
	aList2 = 1:3
	swap(aList1,aList2)
	? aList1			# 1 2 3
	? aList2			# 4 5 6
	aList = [ 4:6 , 1:3 ]
	? aList				# 4 5 6 1 2 3
	swap(aList[1], aList[2])
	? aList				# 1 2 3 4 5 6
	aList = [ 4:6 , 1:3 ]
	? aList				# 4 5 6 1 2 3
	swap(aList,1,2)
	? aList				# 1 2 3 4 5 6
Example (6):
	load "fastpro.ring"
	aList = [
		[10,20,0],
		[30,40,0],
		[50,60,0]
	]
	updateList(aList,:mul,:col,1,10,3)
	? aList		# 10 20 100 30 40 300 50 60 500
.. index:: 
	pair: What is new in Ring  1.20; Enable/Disable Hash Comments
Enable/Disable Hash Comments
============================
We added the next two commands to the Ring Scanner


* Setter/Getter Methods (optional)
.. code-block:: ring

	# Declarative Programming (Nested Structures)
	Screen() 
	{
		point() 
		{ 			
			x = 100 
			y = 200
			z = 300		
		}
		point() 
		{ 			 
			x = 50 
			y = 150
			z = 250		
		}
	}
	# Functions and Classes
	Func screen return new screen
	Class Screen
		content = []
		func point
			content + new point
			return content[len(content)]	
		func braceend
			see "I have " + len(content) + " points!"
	Class point 
		x=10 y=20 z=30
		func braceend		
			see self	
Output:
	x: 100.000000
	y: 200.000000
	z: 300.000000
	x: 50.000000
	y: 150.000000
	z: 250.000000
	I have 2 points!
.. index:: 
	pair: Declarative Programming; More Beautiful Code
More Beautiful Code
===================
We can get better results and a more beautiful code when we can avoid writing () after the method name
when the methods doesn't take parameters.
This feature is not provided directly by the Ring language because there is a difference between object methods
and object attributes. We can get a similar effect on the syntax of the code when we define a getter method for
the object attribute. For example instead of defining the point() method. we will define the point attribute then
the getpoint() method that will be executed once you try to get the value of the point attribute.
since we write the variable name directly without () we can write point instead of point() and the method getpoint()
will create the object and return the object reference for us.
Example:
	new Container 
	{
		Point 
		{ 
			x=10 
			y=20 
			z=30 
		}
	}
	Class Container
		aObjs = []
		point
		func getpoint
			aObjs + new Point
			return aObjs[len(aObjs)]
	Class Point x y z
		func braceend
			see "3D Point" + nl + x + nl + y + nl + z + nl
Output
	3D Point
	10
	20
	30


* The Form Class is updated to support the "target" attribute.
.. code-block:: ring

	BootStrapWebPage() 
	{
		Title = "The Ring Programming Language"
		html(template("main.rhtml",NULL))
		div {
			classname = :container
			div
			{
				id = "div3"
				color = "black"
				backgroundcolor = "white"
				width = "100%"
				form
				{
					method = "POST"
					Action = website  
					Target = "codeoutput"
					input { type="hidden" name="page" value=1 }
					Table
					{ 
						style = stylewidth("100%") +
							stylegradient(3)			
						TR
						{
					
							TD { align="center" 
								WIDTH="10%"
								 text("Code :") 
							}
							TD {
								html(`
								<textarea name = "cCode" 
								rows="5" 
								style="width : 100%; ">
								See "Hello, World!" + nl
								</textarea>`)
							}
						}
					}
					Input { type = "submit" 
						classname="btn btn-primary btn-block" 
							value = "Execute" }
					Table
					{ 
						style = stylewidth("100%") +
							stylegradient(34)			
						TR
						{
					
							TD { align="center"
								WIDTH="10%" 
								text("Output :") 
							}
							TD {
							html(`
							<iframe name="codeoutput" 
							width="100%" 
							style="background-color:white;">
							</iframe>`)
							}
						}
					}
				}
			}
		}
		html(template("footer.rhtml",NULL))
	}
.. index:: 
	pair: What is new in Ring 1.4?; Better RingQt
Better RingQt
=============
The next functions are added to RingQt


* Packages and Classes
.. code-block:: ring

	/* 
		Program Name : My first program using Ring
		Date         : 2015.05.08
	*/
	See "What is your name? " 	# print message on screen
	give cName 			# get input from the user
	see "Hello " + cName		# say hello!
	// See "Bye!"
				
.. index:: 
	pair: Language Design; Data Representation
Data Representation
===================
Ring contains only 4 types that represent the program data
These types are (String, Number, List & Object)
The idea is to have many use cases for each type which increase the flexibility and the ability
to write functions that are more usable in different situations.
The String type is used to represent:


* Local Scope  ---> Object Scope
.. code-block:: ring

	New Account {
		see aFriends
	}
	Class Account
		name = "Mahmoud"
		aFriends = []
		aFriends + new Friend { 
			name = "Gal" 
		}
		aFriends + new Friend { 
			name = "Bert" 
		}
	
	Class Friend
		name
Output:
.. code-block:: none
	name: NULL
	name: NULL
The problem in the previous example is that the Class account contains an attribute called "name"
and the Friend class contains an attribute called "name" also.
If you tried using self.name inside braces you will get the same result!
	New Account {
		see aFriends
	}
	Class Account
		name = "Mahmoud"
		aFriends = []
		aFriends + new Friend { 
			self.name = "Gal" 
		}
		aFriends + new Friend { 
			self.name = "Bert" 
		}
	
	Class Friend
		name
So why using self.name inside braces doesn't solve this conflict?
Because after the class region we have


* Version() function - Optional parameter to display the patch number 
.. code-block:: ring

	? version()
	? version(True)
Output:
.. code-block:: none
	1.21
	1.21.1


