.. index:: 
	single: Ring Package Manager (RingPM); Introduction

=================================
The Ring Package Manager (RingPM)
=================================

In this chapter we will learn about using the Ring Package Manager (RingPM)

RingPM is a tool for discovering, installing and updating Ring packages.


.. index:: 
	pair: Ring Package Manager (RingPM); Features

Features
========

The Package Manager uses Semantic Versioning to check compatibility between packages 

The Package Manager comes with the next options

.. code-block:: none

	Usage   : ringpm [command]
	Command : search  [keywords...]
	Command : refresh : Update the Registry (Packages List)
	Command : install [ <packagename> [from <UserName>] [branch <branchname>] ]
	Command : list    [-u : check updates]
	Command : run     [packagename]
	Command : update  <packagename>
	Command : remove  <packagename>
	Command : format  : Delete All Packages
	Command : new     <packagename>
	Command : package : Create package in the current folder

.. index:: 
	pair: Ring Package Manager (RingPM); Discovering Packages

Discovering Packages
====================

We can discover new packages using the Search command

Using this command we can search in the RingPM Registry (Packages Index)

The RingPM Registry is a local copy of all registered packages.

.. code-block:: none

	ringpm search [keywords...]

Example:

.. code-block:: none

	ringpm search notepad

Output:

.. code-block:: none

	Package : ringnotepad (Ring Notepad)
	Package : notepadppeditorextension (Notepad++ Editor Extension package)

To print all packages in the RingPM Registry, use the search command without keywords.

Example:

.. code-block:: none

	ringpm search

.. index:: 
	pair: Ring Package Manager (RingPM); Updating the RingPM Registry

Updating the RingPM Registry
============================

The RingPM Registry is a local copy of all registered packages.

We can update the local copy using the Refresh command

Example:

.. code-block:: none

	ringpm refresh

Output:

.. code-block:: none

	No updates to the Registry, Nothing to do!

Or

.. code-block:: none

	The Registry is updated from revision 110 (2019/01/13) to revision 112 (2019/01/15)

.. index:: 
	pair: Ring Package Manager (RingPM); Installing Packages

Installing Packages
===================


We can install new packages using the Install command

.. code-block:: none

	ringpm install [ <packagename> [from <UserName>] [branch <branchname>] ]

We can type only the package name to get the package information from the RingPM Registry or
we can determine the user name (GitHub) and the branch name of the github project (optional).

If the current folder is a package folder then we don't need to write the package name. 

Example (1) :

.. code-block:: none

	ringpm install ringnotepad

Example (2) :

.. code-block:: none

	ringpm install goldmagic800

Example (3) :

.. code-block:: none

	ringpm install gameoflife

If the package is not added to the RingPM Registry, We can install it directly from the GitHub user

Example (4) :

.. code-block:: none

	ringpm install firstpackage from mahmoudfayed

To run the package after installation

.. code-block:: none

	ringpm run firstpackage

To install a package in the current folder

Example (5) :

.. code-block:: none

	ringpm install

.. index:: 
	pair: Ring Package Manager (RingPM); Printing List of Installed Packages

Printing List of Installed Packages
===================================

We can know the installed packages using the List command

.. code-block:: none

	ringpm list [-u : check updates]

Example

.. code-block:: none

	ringpm list

Output

.. code-block:: none

	(analogclock)          : The AnalogClock Package [master]         -- (1.0.0)
	(androidringlibsdl)    : The AndroidRingLibSDL Package [master]   -- (1.0.0)
	(androidringqt)        : The AndroidRingQt Package [master]       -- (1.0.0)
	(atomeditorextension)  : The AtomEditorExtension Package [master] -- (1.0.0)
	(bignumber)            : The BigNumber Package [master]           -- (1.0.0)
	(calculator)           : The Calculator Package [master]          -- (1.0.0)
	(cards)                : The Cards Package [master]               -- (1.0.0)
	(checkers)             : The Checkers Package [master]            -- (1.0.0)
	(chess)                : The Chess Package [master]               -- (1.0.0)
	....

To check for new updates

.. code-block:: none

	ringpm list -u

.. index:: 
	pair: Ring Package Manager (RingPM); Run Package

Run Package
===========

After installing a package, we can run it using the Run command.

.. code-block:: none

	ringpm run [packagename]

Example(1):

.. code-block:: none

	ringpm run ringnotepad

Example(2):

.. code-block:: none

	ringpm run goldmagic800

Example(3):

.. code-block:: none

	ringpm run gameoflife

To run a package in the current folder

Example(4):

.. code-block:: none

	ringpm run

.. index:: 
	pair: Ring Package Manager (RingPM); Update Package

Update Package
==============

We can update a package using the Update command

.. code-block:: none

	ringpm update <packagename>

Example:

.. code-block:: none

	ringpm update ringnotepad

.. index:: 
	pair: Ring Package Manager (RingPM); Remove Package

Remove Package
==============

We can remove a package using the Remove command

.. code-block:: none

	ringpm remove <packagename>

Example:

.. code-block:: none

	ringpm remove ringnotepad

.. index:: 
	pair: Ring Package Manager (RingPM); Deleting All Packages

Deleting All Packages
=====================

We can delete all packages using the Format command

Example:

.. code-block:: none

	ringpm format


.. index:: 
	pair: Ring Package Manager (RingPM); Creating New Package

Creating New Package
====================

We can create new package using the New command

.. code-block:: none

	ringpm new <packagename>

Example:

.. code-block:: none

	ringpm new myapp

This will create new folder called my myapp

The new folder will contains the next file

* package.ring : The package description and files 
* main.ring : main program (used by the Run command)
* lib.ring : library file for the package 

File  : main.ring

.. code-block:: ring

	# The Main File

	load "lib.ring"

	func main

		? "Hello, World!"


File : lib.ring

.. code-block:: ring

	# The Library File

File : package.ring

.. code-block:: ring

	aPackageInfo = [
		:name = "The myapp Package",
		:description = "Our myapp package using the Ring programming language",
		:folder = "myapp",
		:developer = "",
		:email = "",
		:license = "MIT License",
		:version = "1.0.0",
		:ringversion = "1.10",
		:versions = 	[
			[
				:version = "1.0.0",
				:branch = "master"
			]
		],
		:libs = 	[
			[
				:name = "",
				:version = "",
				:providerusername = ""
			]
		],
		:files = 	[
			"lib.ring",
			"main.ring"
		],
		:ringfolderfiles = 	[

		],
		:windowsfiles = 	[

		],
		:linuxfiles = 	[

		],
		:ubuntufiles = 	[

		],
		:fedorafiles = 	[

		],
		:macosfiles = 	[

		],
		:windowsringfolderfiles = 	[

		],
		:linuxringfolderfiles = 	[

		],
		:ubunturingfolderfiles = 	[

		],
		:fedoraringfolderfiles = 	[

		],
		:macosringfolderfiles = 	[

		],
		:run = "ring main.ring",
		:windowsrun = "",
		:linuxrun = "",
		:macosrun = "",
		:ubunturun = "",
		:fedorarun = "",
		:setup = "",
		:windowssetup = "",
		:linuxsetup = "",
		:macossetup = "",
		:ubuntusetup = "",
		:fedorasetup = "",
		:remove = "",
		:windowsremove = "",
		:linuxremove = "",
		:macosremove = "",
		:ubunturemove = "",
		:fedoraremove = ""
	]
	
.. index:: 
	pair: Ring Package Manager (RingPM); The Package Description File

The Package Description File
============================

The package description file contains the package information defined in the list aPackageInfo

=======================	=================================================================================
Attribute		Description
=======================	=================================================================================
Name			Package Name
Description		Package Description
Folder			The Folder Name (Will be created in ring/ringpm/packages)
Developer		The Package Developer Name  
Email			The Package Developer Email
License			The Package License
Version			The Current Version of the Package (Latest Release)
RingVersion		The Required Ring Language Version (Minimum Version)
Versions		List of different versions provided by different branches in the GitHub project 
Libs			List of dependencies (Defined by name, version & GitHub user name)
Files			List of files (will be installed in ring/ringpm/packages/[Folder]
RingFolderFiles		List of files (will be installed in ring folder)
WindowsFiles 		Like (Files) but for Microsoft Windows Only
LinuxFiles 		Like (Files) but for Linux Only
MacOSFiles 		Like (Files) but for macOS Only
UbuntuFiles 		Like (Files) but for Ubuntu Only
FedoraFiles 		Like (Files) but for Fedora Only
WindowsRingFolderFiles 	Like (RingFolderFiles) but for Microsoft Windows Only
LinuxRingFolderFiles 	Like (RingFolderFiles) but for Linux Only
MacOSRingFolderFiles 	Like (RingFolderFiles) but for macOS Only
UbuntuRingFolderFiles 	Like (RingFolderFiles) but for Ubuntu Only
FedoraRingFolderFiles 	Like (RingFolderFiles) but for Fedora Only
Run			System Command (Command prompt or Terminal) to run the package
WindowsRun 		Like (Run) but for Microsoft Windows Only
LinuxRun 		Like (Run) but for Linux Only
MacOSRun 		Like (Run) but for macOS Only
UbuntuRun 		Like (Run) but for Ubuntu Only
FedoraRun 		Like (Run) but for Fedora Only
Setup			System Command (Command prompt or Terminal) after downloading the package files
WindowsSetup 		Like (Setup) but for Microsoft Windows Only
LinuxSetup 		Like (Setup) but for Linux Only
MacOSSetup 		Like (Setup) but for macOS Only
UbuntuSetup 		Like (Setup) but for Ubuntu Only
FedoraSetup 		Like (Setup) but for Fedora Only
Remove			System Command (Command prompt or Terminal) before removing the package files
WindowsRemove 		Like (Remove) but for Microsoft Windows Only
LinuxRemove 		Like (Remove) but for Linux Only
MacOSRemove 		Like (Remove) but for macOS Only
UbuntuRemove 		Like (Remove) but for Ubuntu Only
FedoraRemove 		Like (Remove) but for Fedora Only
=======================	=================================================================================



.. index:: 
	pair: Ring Package Manager (RingPM); Create Package in the Current Folder

Create Package in the Current Folder
====================================

To create a package for an application that already exists, go to the application folder then type

.. code-block:: none

	ringpm package

This will create the package definition file (package.ring) and will add all of the application files to the package definition.

Each RingPM package contains the package definition file (package.ring)

The package definition file is a list that describe the package information and files.

Example : 

The package definition file for the Ring Notepad package

.. code-block:: ring

	aPackageInfo = [
		:name = "The RingNotepad Package",
		:description = "Our RingNotepad package using the Ring programming language",
		:folder = "ringnotepad",
		:developer = "Mahmoud Fayed",
		:email = "<EMAIL>",
		:license = "MIT License",
		:version = "1.0.0",
		:ringversion = "1.10",
		:versions = 	[
			[
				:version = "1.0.0",
				:branch = "master"
			]
		],
		:libs = 	[
			[
				:name = "stdlib",
				:version = "1.0",
				:providerusername = ""
			],
			[
				:name = "ringqt",
				:version = "1.0",
				:providerusername = ""
			],
			[
				:name = "findinfiles",
				:version = "1.0",
				:providerusername = ""
			],
			[
				:name = "formdesigner",
				:version = "1.0",
				:providerusername = ""
			],
			[
				:name = "libdepwin_apache",
				:version = "1.0",
				:providerusername = ""
			]
		],
		:files = 	[
			"main.ring",
			"README.md"
		],
		:ringfolderfiles = 	[
			"applications/rnote/batch/killwebserver.bat",
			"applications/rnote/batch/run.bat",
			"applications/rnote/batch/run2.bat",
			"applications/rnote/image/back.jpg",
			"applications/rnote/image/back2.jpg",
			"applications/rnote/image/close.png",
			"applications/rnote/image/colors.png",
			"applications/rnote/image/copy.png",
			"applications/rnote/image/cut.png",
			"applications/rnote/image/debug.png",
			"applications/rnote/image/font.png",
			"applications/rnote/image/formdesigner.png",
			"applications/rnote/image/new.png",
			"applications/rnote/image/notepad.png",
			"applications/rnote/image/open.png",
			"applications/rnote/image/paste.png",
			"applications/rnote/image/print.png",
			"applications/rnote/image/project.png",
			"applications/rnote/image/richtext.png",
			"applications/rnote/image/run.png",
			"applications/rnote/image/rungui.png",
			"applications/rnote/image/save.png",
			"applications/rnote/image/saveas.png",
			"applications/rnote/image/search.png",
			"applications/rnote/image/source.png",
			"applications/rnote/image/undo.png",
			"applications/rnote/image/web.png",
			"applications/rnote/README.md",
			"applications/rnote/rnote.ring",
			"applications/rnote/rnoteactivefile.ring",
			"applications/rnote/rnoteactivefolder.ring",
			"applications/rnote/rnoteautocomplete.ring",
			"applications/rnote/rnotebase.ring",
			"applications/rnote/rnotecontroller.ring",
			"applications/rnote/rnotedistribute.ring",
			"applications/rnote/rnoteeditmenu.ring",
			"applications/rnote/rnoteeditorevents.ring",
			"applications/rnote/rnotefilemenu.ring",
			"applications/rnote/rnotefiles.ring",
			"applications/rnote/rnotefilestabs.ring",
			"applications/rnote/rnotefind.ring",
			"applications/rnote/rnotefindinfiles.ring",
			"applications/rnote/rnoteformdesigner.ring",
			"applications/rnote/rnotegoto.ring",
			"applications/rnote/rnotehelp.ring",
			"applications/rnote/rnotelists.ring",
			"applications/rnote/rnotemainfiletoolbar.ring",
			"applications/rnote/rnotemainwindow.ring",
			"applications/rnote/rnotemode.ring",
			"applications/rnote/rnoteoutputwindow.ring",
			"applications/rnote/rnoteprogrammenu.ring",
			"applications/rnote/rnoterun.ring",
			"applications/rnote/rnotesettings.ring",
			"applications/rnote/rnotestyle.ring",
			"applications/rnote/rnotetabwidth.ring",
			"applications/rnote/rnotetools.ring",
			"applications/rnote/rnotetreeviewevents.ring",
			"applications/rnote/rnoteviewmenu.ring",
			"applications/rnote/rnotewebbrowser.ring"
		],
		:run = "ring main.ring"
	]

.. index:: 
	pair: Ring Package Manager (RingPM); The RingPM Registry File

The RingPM Registry File
========================

The RingPM Registry is a local copy of all registered packages.

.. code-block:: ring

	aPackagesRegistry = [
		[ :name = "ringpresentation",
		  :description = "Powerpoint presentation for the Ring programming language",
		  :ProviderUserName = "ringpackages"
		]
		# ...
	]

Each package is defined using a list that determine 

* Package Name
* Package Description
* Provider User Name (GitHub User Name)

We can register new packages by updating the registry file then sending a Pull Request

URL : https://github.com/ring-lang/ring/blob/master/tools/ringpm/registry/registry.ring

