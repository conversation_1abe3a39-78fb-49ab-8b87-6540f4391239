load "config.ring"
load "routes.ring"
load "middleware/auth.ring"

class APIServer
    config
    router
    app
    
    func init
        config = new APIConfig
        router = new Router
        
        # إعداد التطبيق
        app = new WebApp([
            :host = config.SERVER_HOST,
            :port = config.SERVER_PORT,
            :debug = config.DEBUG_MODE
        ])
        
        # إعداد CORS
        if len(config.CORS_ORIGINS) > 0
            app.use(cors([
                :origins = config.CORS_ORIGINS,
                :methods = ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
                :allowedHeaders = ["Content-Type", "Authorization"]
            ]))
        ok
        
        # إعداد المسارات
        router.setupRoutes(app)
        
        # تفعيل SSL إذا كان مطلوباً
        if config.SSL_ENABLED
            app.useSSL([
                :cert = config.SSL_CERT,
                :key = config.SSL_KEY
            ])
        ok
    
    func start
        try
            ? "بدء تشغيل الخادم على المنفذ " + config.SERVER_PORT
            app.start()
            
        catch
            ? "حدث خطأ أثناء بدء تشغيل الخادم: " + cCatchError
            return false
        done
        
        return true
    
    func stop
        try
            app.stop()
            ? "تم إيقاف الخادم"
            return true
            
        catch
            ? "حدث خطأ أثناء إيقاف الخادم: " + cCatchError
            return false
        done

# بدء تشغيل الخادم
server = new APIServer
server.start()
