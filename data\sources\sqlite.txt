.. index:: 
	single: SQLite; Introduction

================
SQLite Functions
================

In this chapter we will learn about using the SQLite database in the Ring programming language.

Before using the next function load the sqlitelib.ring library

.. code-block:: ring

	load "sqlitelib.ring"
	# Use SQLite functions

.. index:: 
	pair: SQLite; sqlite_init()

sqlite_init() function
======================

Syntax:

.. code-block:: ring

	sqlite_init() ---> SQLite Object

.. index:: 
	pair: SQLite; sqlite_open()

sqlite_open() function
======================

Syntax:

.. code-block:: ring

	sqlite_open(SQLite Object,cFileName)

.. index:: 
	pair: SQLite; sqlite_execute()

sqlite_execute() function
=========================

Syntax:

.. code-block:: ring

	sqlite_execute(SQLite Object,cSQLStatement)

.. index:: 
	pair: SQLite; sqlite_close()

sqlite_close() function
=======================

Syntax:

.. code-block:: ring

	sqlite_close(SQLite Object)

Example
=======

The next code create a SQLite database, add new records then display the data.

.. code-block:: ring

	load "sqlitelib.ring"

	oSQLite = sqlite_init()

	sqlite_open(oSQLite,"mytest.db") 

	sql = "
		 CREATE TABLE COMPANY (
        	 ID INT PRIMARY KEY     NOT NULL,
	         NAME           TEXT    NOT NULL,
        	 AGE            INT     NOT NULL,
	         ADDRESS        CHAR(50),
	         SALARY         REAL );
	"
	sqlite_execute(oSQLite,sql) 

	sql = "
		INSERT INTO COMPANY (ID,NAME,AGE,ADDRESS,SALARY) 
		VALUES  (1, 'Mahmoud' , 29, 'Jeddah', 20000.00 ),
			(2, 'Ahmed'   , 27, 'Jeddah', 15000.00 ),
			(3, 'Mohammed', 31, 'Egypt' , 20000.00 ),
			(4, 'Ibrahim' , 24, 'Egypt ', 65000.00 );
	"

	sqlite_execute(oSQLite,sql)

	aResult =  sqlite_execute(oSQLite,"select * from COMPANY") 
	for x in aResult
		for t in x
			? t[2] + nl
		next
	next
	? copy("*",50) 
	for x in aResult
		? x[:name] 
	next
	sqlite_close(oSQLite) 

Output:

.. code-block:: ring

	1
	Mahmoud
	29
	Jeddah
	20000.0
	2
	Ahmed
	27
	Jeddah
	15000.0
	3
	Mohammed
	31
	Egypt
	20000.0
	4
	Ibrahim
	24
	Egypt
	65000.0
	**************************************************	
	Mahmoud
	Ahmed
	Mohammed
	Ibrahim

