# المشفر (Encoder)
# يقوم بتحويل المدخلات إلى تمثيل عالي المستوى
# يستخدم طبقات الانتباه متعدد الرؤوس والشبكات العصبية الأمامية
Load "stdlibcore.ring"
Load "../utils/math.ring"
Load "attention.ring"

# طبقة الشبكة العصبية الأمامية
Class FeedForward {
    # المتغيرات
    nDModel      # البعد الأساسي للنموذج
    nDFF         # البعد الداخلي للشبكة
    W1           # مصفوفة الأوزان للطبقة الأولى
    b1           # متجه الانحياز للطبقة الأولى
    W2           # مصفوفة الأوزان للطبقة الثانية
    b2           # متجه الانحياز للطبقة الثانية
    
    # دالة التهيئة
    # المدخلات:
    #   d_model: البعد الأساسي للنموذج
    #   d_ff: البعد الداخلي للشبكة
    func init(d_model, d_ff) {
        nDModel = d_model
        nDFF = d_ff
        
        # تهيئة الأوزان والانحيازات بقيم عشوائية صغيرة
        W1 = []
        b1 = []
        W2 = []
        b2 = []
        
        # الطبقة الأولى: d_model -> d_ff
        for i = 1 to d_ff
            W1_row = []
            for j = 1 to d_model
                add(W1_row, random_normal(0, 0.02))
            next
            add(W1, W1_row)
            add(b1, random_normal(0, 0.02))
        next
        
        # الطبقة الثانية: d_ff -> d_model
        for i = 1 to d_model
            W2_row = []
            for j = 1 to d_ff
                add(W2_row, random_normal(0, 0.02))
            next
            add(W2, W2_row)
            add(b2, random_normal(0, 0.02))
        next
    }
    
    # دالة التقدم الأمامي
    # المدخلات:
    #   x: المدخلات
    # المخرجات:
    #   المخرجات بعد تطبيق الشبكة العصبية
    func forward(x) {
        # الطبقة الأولى مع دالة GELU
        hidden = []
        for i = 1 to len(x)
            row = matrixMultiply([x[i]], W1)
            for j = 1 to len(row)
                row[j] += b1[j]
                row[j] = gelu(row[j])
            next
            add(hidden, row)
        next
        
        # الطبقة الثانية
        output = []
        for i = 1 to len(hidden)
            row = matrixMultiply([hidden[i]], W2)
            for j = 1 to len(row)
                row[j] += b2[j]
            next
            add(output, row)
        next
        
        return output
    }
}

# طبقة المشفر
Class EncoderLayer {
    # المتغيرات
    oAttention   # طبقة الانتباه متعدد الرؤوس
    oFFN         # الشبكة العصبية الأمامية
    nDModel      # البعد الأساسي للنموذج
    
    # دالة التهيئة
    # المدخلات:
    #   d_model: البعد الأساسي للنموذج
    #   num_heads: عدد رؤوس الانتباه
    #   d_ff: البعد الداخلي للشبكة العصبية
    func init(d_model, num_heads, d_ff) {
        nDModel = d_model
        oAttention = new MultiHeadAttention(num_heads, d_model)
        oFFN = new FeedForward(d_model, d_ff)
    }
    
    # دالة التقدم الأمامي
    # المدخلات:
    #   x: المدخلات
    #   mask: قناع اختياري
    # المخرجات:
    #   المخرجات بعد تطبيق طبقة المشفر
    func forward(x, mask = null) {
        # طبقة الانتباه مع التطبيع والاتصال المتخطي
        attn_output = oAttention.forward(x, mask)
        x1 = []
        for i = 1 to len(x)
            row = []
            for j = 1 to len(x[i])
                add(row, x[i][j] + attn_output[i][j])
            next
            add(x1, layer_norm(row))
        next
        
        # طبقة الشبكة العصبية مع التطبيع والاتصال المتخطي
        ffn_output = oFFN.forward(x1)
        output = []
        for i = 1 to len(x1)
            row = []
            for j = 1 to len(x1[i])
                add(row, x1[i][j] + ffn_output[i][j])
            next
            add(output, layer_norm(row))
        next
        
        return output
    }
}

# المشفر الكامل
Class Encoder {
    # المتغيرات
    aLayers      # مصفوفة من طبقات المشفر
    nNumLayers   # عدد الطبقات
    nDModel      # البعد الأساسي للنموذج
    
    # دالة التهيئة
    # المدخلات:
    #   num_layers: عدد طبقات المشفر
    #   d_model: البعد الأساسي للنموذج
    #   num_heads: عدد رؤوس الانتباه
    #   d_ff: البعد الداخلي للشبكة العصبية
    func init(num_layers, d_model, num_heads, d_ff) {
        nNumLayers = num_layers
        nDModel = d_model
        aLayers = []
        
        # إنشاء طبقات المشفر
        for i = 1 to num_layers
            add(aLayers, new EncoderLayer(d_model, num_heads, d_ff))
        next
    }
    
    # دالة التقدم الأمامي
    # المدخلات:
    #   x: المدخلات
    #   mask: قناع اختياري
    # المخرجات:
    #   المخرجات النهائية للمشفر
    func forward(x, mask = null) {
        # تطبيق كل طبقات المشفر بالتتابع
        output = x
        for layer in aLayers
            output = layer.forward(output, mask)
        next
        return output
    }
}
