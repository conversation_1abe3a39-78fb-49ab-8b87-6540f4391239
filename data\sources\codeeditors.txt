.. index:: 
	single: Using Other Code Editors; Introduction

========================
Using Other Code Editors
========================

We have extensions for the next editors:

* Notepad++
* Geany
* nano
* Atom
* Sublime Text 2
* Visual Studio IDE
* Emacs
* Visual Studio Code (VSCode)
* SpaceVim
* Lite XL

.. index:: 
	pair: Using Other Code Editors; Using Notepad++


Using Notepad++
===============

Folder : ring/tools/editors/notepad_plus_plus

* Open Notepad++
* Open the "Language" menu
* Select "Define your language..."
* Click "Import..."
* select `Ring.xml`
* Select "OK" on the "Import successful" dialog and close the "User Defined Language" dialog/panel
* You may need to restart notepad++

.. image:: editor1.png
	:alt: Using Notepad++

.. index:: 
	pair: Using Other Code Editors; Using Geany

Using Geany
===========

Folder : ring/tools/editors/geany

* Run Geany editor
* Click on "Tools -> configuration files -> filetypes_extensions.conf"  menu
* Add this line "Ring=*.ring;" without quotes after [Extensions]
* In Ubuntu copy file "filetypes.Ring.conf" to folder "/home/<USER>/filetypes.Ring.conf"
* You can run your files by pressing F5 button

.. image:: editor2.png
	:alt: Using Geany

.. index:: 
	pair: Using Other Code Editors; Using nano

Using nano
===========

Folder : ring/tools/editors/nano

Check the ReadMe file for installation instructions.

.. image:: nano.png
	:alt: Using nano


.. index:: 
	pair: Using Other Code Editors; Using Atom

Using Atom
==========

Folder : ring/tools/editors/atom

Just Copy the folder atom-language-ring to the next path

.. code-block:: none

	"C:\Users\<USER>\.atom\Packages"

.. image:: editor3.jpg
	:alt: Using Atom

.. index:: 
	pair: Using Other Code Editors; Using Sublime Text 2

Using Sublime Text 2
====================

Folder : ring/tools/editors/sublime text 2

In the folder Sublime_Text_2 you will find the next three files

1 - ring.json-tmlanguage

2 - ring.sublime-build

3 - ring.tmlanguage

Just Copy the files to the next path

.. code-block:: none

	"C:\Users\<USER>\AppData\Roaming\Sublime Text 2\Packages\User\"

The file ring.sublime-build includes the next line

.. code-block:: none

	"cmd": ["B:\\ring\\bin\\ring.exe","$file"],

You can modify it according to the ring.exe path in your machine 


.. image:: editor4.jpg
	:alt: Using Sublime Text 2


.. index:: 
	pair: Using Other Code Editors; Using Visual Studio IDE

Using Visual Studio IDE
=======================

Folder : ring/tools/editors/visualstudio

Check the ReadMe file for installation instructions.	

.. image:: editor5.png
	:alt: Using Visual Studio

.. index:: 
	pair: Using Other Code Editors; Using Emacs Editor

Using Emacs Editor
==================

Folder : ring/tools/editors/emacs

Check the ReadMe file for installation instructions.	

Screen Shot:

.. image:: ringemacs.png
	:alt: Ring mode for Emacs Editor


.. index:: 
	pair: Using Other Code Editors; Visual Studio Code

Visual Studio Code
==================

Folder : ring/tools/editors/vscode

Check the ReadMe file for installation instructions.	

Screen Shot:

.. image:: ringinvscode.png
	:alt: Ring in Visual Studio Code

.. index:: 
	pair: Using Other Code Editors; SpaceVim

SpaceVim
========

URL: https://github.com/SpaceVim/SpaceVim

Screen Shot:

.. image:: ringinvim.png
	:alt: Ring in SpaceVim


Lite XL
=======

Folder: ring/tools/editors/lite-xl

Screen Shot:

.. image:: litexl1.png
	:alt: Ring in Lite XL