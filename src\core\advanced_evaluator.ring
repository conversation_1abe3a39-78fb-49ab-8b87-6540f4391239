# تقييم متقدم للكود
# يوفر خوارزميات متقدمة لتقييم وتحسين الكود المولد

Load "stdlibcore.ring"
Load "../utils/math.ring"

# مثال على الاستخدام
if ismainsourcefile() {
    evaluator = new AdvancedEvaluator
    if evaluator.loadModel(config.cModelPath + "latest_model.bin") {
        evaluator.evaluateOnTestData()
    }
}


# فئة التقييم المتقدم للكود
Class AdvancedCodeEvaluator {
    # المتغيرات
    aMetrics         # مقاييس التقييم
    nWeights        # أوزان المقاييس
    oCache          # ذاكرة مؤقتة للتقييمات
    
    func init
        aMetrics = [
            "complexity"= method(:evaluateComplexity), 
            "readability"= method(:evaluateReadability),
            "efficiency"= method(:evaluateEfficiency),
            "security"= method(:evaluateSecurity),
            "maintainability"= method(:evaluateMaintainability)
        ]
        
        # أوزان المقاييس المختلفة
        nWeights = [
            "complexity"= 0.25,
            "readability"= 0.2,
            "efficiency"= 0.25,
            "security"= 0.15,
            "maintainability"= 0.15
        ]
        
        oCache = new Map
    
    # تقييم شامل للكود
    func evaluateCode(code, lang)
        # التحقق من الذاكرة المؤقتة
        cacheKey = generateCacheKey(code, lang)
        if exists(oCache, cacheKey)
            return oCache[cacheKey]
        ok
        
        totalScore = 0
        for metric in aMetrics.keys()
            score = aMetrics[metric].call(code, lang)
            totalScore += score * nWeights[metric]
        next
        
        # تخزين النتيجة في الذاكرة المؤقتة
        oCache[cacheKey] = totalScore
        return totalScore
    
    # تقييم التعقيد
    func evaluateComplexity(code, lang)
        score = 100
        
        # حساب تعقيد McCabe الدوري
        cyclomaticComplexity = calculateCyclomaticComplexity(code, lang)
        if cyclomaticComplexity > 10
            score -= (cyclomaticComplexity - 10) * 5
        ok
        
        # تحليل تداخل الكود
        nestingDepth = calculateNestingDepth(code)
        if nestingDepth > 3
            score -= (nestingDepth - 3) * 10
        ok
        
        return max(0, score) / 100
    
    # تقييم القابلية للقراءة
    func evaluateReadability(code, lang)
        score = 100
        
        # طول الأسطر
        avgLineLength = calculateAverageLineLength(code)
        if avgLineLength > 80
            score -= (avgLineLength - 80) * 2
        ok
        
        # التوثيق
        docScore = evaluateDocumentation(code, lang)
        score += docScore * 20
        
        # تناسق التنسيق
        formatScore = evaluateFormatting(code, lang)
        score += formatScore * 20
        
        return max(0, score) / 100
    
    # تقييم الكفاءة
    func evaluateEfficiency(code, lang)
        score = 100
        
        # تحليل الخوارزميات
        algorithmScore = analyzeAlgorithms(code, lang)
        score *= algorithmScore
        
        # استخدام الذاكرة
        memoryScore = analyzeMemoryUsage(code, lang)
        score *= memoryScore
        
        # تكرار الكود
        duplicationScore = analyzeDuplication(code)
        if duplicationScore < 0.8
            score *= duplicationScore
        ok
        
        return max(0, score) / 100
    
    # تقييم الأمان
    func evaluateSecurity(code, lang)
        score = 100
        
        # فحص الثغرات المعروفة
        vulnerabilities = findVulnerabilities(code, lang)
        score -= len(vulnerabilities) * 20
        
        # التحقق من المدخلات
        inputValidation = checkInputValidation(code, lang)
        if not inputValidation
            score -= 30
        ok
        
        return max(0, score) / 100
    
    # تقييم قابلية الصيانة
    func evaluateMaintainability(code, lang)
        score = 100
        
        # حجم الوظائف
        functionSizes = analyzeFunctionSizes(code, lang)
        for size in functionSizes
            if size > 30  # أكثر من 30 سطر
                score -= 5
            ok
        next
        
        # الاعتماديات
        dependencies = analyzeDependencies(code, lang)
        if len(dependencies) > 5
            score -= (len(dependencies) - 5) * 10
        ok
        
        return max(0, score) / 100
    
    private
    # توليد مفتاح للذاكرة المؤقتة
    func generateCacheKey(code, lang)
        return lang + "_" + hash(code)
    
    # حساب تعقيد McCabe الدوري
    func calculateCyclomaticComplexity(code, lang)
        # تحليل بنية التحكم في الكود
        controlStructures = findControlStructures(code, lang)
        return 1 + len(controlStructures)
    
    # حساب عمق التداخل
    func calculateNestingDepth(code)
        maxDepth = 0
        currentDepth = 0
        
        for line in code.split("\n")
            if isBlockStart(line)
                currentDepth++
                maxDepth = max(maxDepth, currentDepth)
            ok
            if isBlockEnd(line)
                currentDepth--
            ok
        next
        
        return maxDepth
}

# فئة لتحسين الكود
Class CodeOptimizer {
    # المتغيرات
    oEvaluator       # مقيم الكود
    nMinThreshold    # الحد الأدنى للقبول
    
    func init
        oEvaluator = new AdvancedCodeEvaluator
        nMinThreshold = 0.7  # 70% كحد أدنى للقبول
    
    # تحسين الكود
    func optimizeCode(code, lang)
        score = oEvaluator.evaluateCode(code, lang)
        if score >= nMinThreshold
            return code
        ok
        
        # محاولات التحسين
        optimized = code
        improvements = [
            method("improveReadability"),
            method("improveEfficiency"),
            method("improveComplexity")
        ]
        
        for improvement in improvements
            newCode = improvement.call(optimized, lang)
            newScore = oEvaluator.evaluateCode(newCode, lang)
            if newScore > score
                optimized = newCode
                score = newScore
            ok
            
            if score >= nMinThreshold
                break
            ok
        next
        
        return optimized
    
    # تحسين القابلية للقراءة
    func improveReadability(code, lang)
        # تنسيق الكود
        code = formatCode(code, lang)
        
        # إضافة تعليقات توضيحية
        code = addComments(code, lang)
        
        # تحسين أسماء المتغيرات
        code = improveVariableNames(code, lang)
        
        return code
    
    # تحسين الكفاءة
    func improveEfficiency(code, lang)
        # تحسين الخوارزميات
        code = optimizeAlgorithms(code, lang)
        
        # تحسين استخدام الذاكرة
        code = optimizeMemoryUsage(code, lang)
        
        # إزالة الكود المكرر
        code = removeDuplication(code, lang)
        
        return code
    
    # تحسين التعقيد
    func improveComplexity(code, lang)
        # تبسيط بنية التحكم
        code = simplifyControlStructures(code, lang)
        
        # تقسيم الوظائف الكبيرة
        code = splitLargeFunctions(code, lang)
        
        # تحسين التداخل
        code = reduceNesting(code, lang)
        
        return code
}

# فئة مقيم متقدم للنموذج
Class AdvancedEvaluator {
    # المتغيرات
    transformer
    config
    utils
    metrics
    
    func init {
        config = new TransformerConfig
        utils = new DataUtils
        initMetrics()
    }
    
    # تهيئة المقاييس
    func initMetrics {
        metrics = {
            "bleu": 0,        # مقياس BLEU للترجمة
            "accuracy": 0,    # دقة التنبؤ
            "f1": 0,         # مقياس F1
            "precision": 0,   # الدقة
            "recall": 0       # الاسترجاع
        }
    }
    
    # تحميل النموذج المدرب
    func loadModel(modelPath) {
        transformer = new SmartTransformer
        if fexists(modelPath) {
            transformer.load(modelPath)
            utils.debug("تم تحميل النموذج من: " + modelPath)
        else
            utils.debug("خطأ: النموذج غير موجود في: " + modelPath)
            return false
        }
        return true
    }
    
    # تقييم النموذج على بيانات الاختبار
    func evaluateOnTestData {
        utils.debug("بدء التقييم على بيانات الاختبار...")
        
        # تحميل بيانات الاختبار
        translationsPath = "../../data/processed/test/translations.txt"
        codePath = "../../data/processed/test/code_examples.txt"
        
        # تقييم الترجمات
        if fexists(translationsPath) {
            evaluateTranslations(translationsPath)
        }
        
        # تقييم توليد الكود
        if fexists(codePath) {
            evaluateCodeGeneration(codePath)
        }
        
        # طباعة النتائج النهائية
        printResults()
    }
    
    # تقييم الترجمات
    func evaluateTranslations(path) {
        utils.debug("تقييم الترجمات...")
        
        content = read(path)
        pairs = str2list(content)
        totalBleu = 0
        count = 0
        
        for pair in pairs {
            if pair != "" {
                parts = split(pair, char(9))
                if len(parts) = 2 {
                    source = parts[1]
                    reference = parts[2]
                    
                    # توليد الترجمة
                    generated = transformer.translate(source)
                    
                    # حساب درجة BLEU
                    bleu = calculateBLEU(generated, reference)
                    totalBleu += bleu
                    count++
                }
            }
        }
        
        if count > 0 {
            metrics["bleu"] = totalBleu / count
        }
    }
    
    # تقييم توليد الكود
    func evaluateCodeGeneration(path) {
        utils.debug("تقييم توليد الكود...")
        
        content = read(path)
        examples = str2list(content)
        correct = 0
        total = 0
        
        for example in examples {
            if example != "" {
                parts = split(example, char(9))
                if len(parts) = 2 {
                    prompt = parts[1]
                    expected = parts[2]
                    
                    # توليد الكود
                    generated = transformer.generateCode(prompt)
                    
                    # تقييم الدقة
                    if compareCode(generated, expected) {
                        correct++
                    }
                    total++
                }
            }
        }
        
        if total > 0 {
            metrics["accuracy"] = correct / total
        }
    }
    
    # حساب درجة BLEU
    func calculateBLEU(generated, reference) {
        # تقسيم النصوص إلى كلمات
        genWords = split(generated, " ")
        refWords = split(reference, " ")
        
        # حساب التطابق بين الكلمات
        matches = 0
        for word in genWords {
            if find(refWords, word) {
                matches++
            }
        }
        
        # حساب الدقة
        precision = matches / len(genWords)
        
        # حساب معامل الطول
        brevityPenalty = 1
        if len(genWords) < len(refWords) {
            brevityPenalty = exp(1 - len(refWords)/len(genWords))
        }
        
        return brevityPenalty * precision
    }
    
    # مقارنة الكود المولد مع المتوقع
    func compareCode(generated, expected) {
        # إزالة المسافات والتعليقات للمقارنة
        generated = cleanCode(generated)
        expected = cleanCode(expected)
        return generated = expected
    }
    
    # تنظيف الكود للمقارنة
    func cleanCode(code) {
        # إزالة التعليقات
        code = regex_replace("#.*$", "", code)
        
        # إزالة المسافات الزائدة
        code = trim(code)
        code = regex_replace("\s+", " ", code)
        
        return code
    }
    
    # طباعة نتائج التقييم
    func printResults {
        utils.debug("=== نتائج التقييم ===")
        utils.debug("درجة BLEU للترجمة: " + metrics["bleu"])
        utils.debug("دقة توليد الكود: " + metrics["accuracy"])
        
        # حفظ النتائج في ملف
        saveResults()
    }
    
    # حفظ نتائج التقييم
    func saveResults {
        resultPath = config.cModelPath + "evaluation_results.txt"
        content = "نتائج التقييم\n" +
                 "=============\n" +
                 "درجة BLEU للترجمة: " + metrics["bleu"] + "\n" +
                 "دقة توليد الكود: " + metrics["accuracy"] + "\n" +
                 "تاريخ التقييم: " + date() + "\n"
        
        write(resultPath, content)
        utils.debug("تم حفظ النتائج في: " + resultPath)
    }
}

