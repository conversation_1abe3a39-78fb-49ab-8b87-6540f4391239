load "stdlibcore.ring"

# دوال مساعدة للتعامل مع البيانات
Class DataUtils {
    # التحقق من وجود مجلد
    func isDirectory(path) {
        try {
            if not dirExists(path) return false ok
            return true
        catch
            return false
        }
    }
    
    # دالة للتتبع مع التحكم في الطباعة
    func debug(msg) {
        see "[UTILS-DEBUG] " + msg + nl
    }
    
    # معالجة دفعة من البيانات
    func processBatch(data, batchSize, processFunc) {
        result = []
        currentBatch = []
        
        for item in data {
            currentBatch + item
            if len(currentBatch) >= batchSize {
                result + processFunc(currentBatch)
                currentBatch = []
            }
        }
        
        # معالجة الدفعة الأخيرة
        if len(currentBatch) > 0 {
            result + processFunc(currentBatch)
        }
        
        return result
    }
    
    # إضافة رموز خاصة للترانسفورمر
    func addSpecialTokens(text, type, config) {
        switch type
            case "translation"
                return config.cBOS + " " + config.cTRANS + " " + text + " " + config.cEOS
            case "code"
                return config.cBOS + " " + config.cCODE + " " + text + " " + config.cEOS
            other
                return config.cBOS + " " + text + " " + config.cEOS
        off
    }
    
    # التحقق من صحة المسار وقراءة الملف
    func readFile(filePath) {
        if !fexists(filePath) {
            debug("خطأ: الملف غير موجود - " + filePath)
            return "" 
        }
        return read(filePath)
    }
}



class Map
    data = []

    func init
        data = []

    func setkey(key, value)
        for pair in data
            if pair[1] = key
                pair[2] = value
                return
            ok
        next
        add(data, [key, value])

    func getkey(key)
        for pair in data
            if pair[1] = key
                return pair[2]
            ok
        next
        return null

    func removekey(key)
        for i = 1 to len(data)
            if data[i][1] = key
                del(data, i)
                return
            ok
        next

    func containsKey(key)
        for pair in data
            if pair[1] = key
                return true
            ok
        next
        return false

    func print
        see "Map contents =" + nl
        for pair in data
            see "Key = " + pair[1] + ", Value = " + pair[2] + nl
        next
        see "==================" + nl
