.. index:: 
	single: Date and Time; Introduction

=============
Date and Time
=============

In this chapter we are going to learn about the date and time functions.

.. index:: 
	pair: Date and Time; clock()


Clock() Function
================

Syntax:

.. code-block:: ring

	 Clock() ---> The number of clock ticks from program start

Example:

.. code-block:: ring
	
	See "Calculate performance" + nl
	t1 = clock()
	for x = 1 to 1000000 next
	see clock() - t1 

.. index:: 
	pair: Date and Time; ClosPerSecond()

ClocksPerSecond() Function
==========================

Return how many clocks in one second

Syntax:

.. code-block:: ring

	clockspersecond() ---> Number of clocks in one second

Example:

.. code-block:: ring

	# Wait 1 second
	t = clock()
	while clock() - t <= clockspersecond() end

.. index:: 
	pair: Date and Time; Time()

Time() Function
===============

We can get the system time using the Time() function.

Example:

.. code-block:: ring
	
	See "Time : " + time()


.. index:: 
	pair: Date and Time; Date()

Date() Function
===============

We can get the date using the Date() function.

Syntax:

.. code-block:: ring

	 Date() ---> String represent the date "dd/mm/yyyy"

Example:

.. code-block:: ring

	See "Date : " + date()  # Date : 24/05/2015


.. index:: 
	pair: Date and Time; TimeList()

TimeList() Function
===================

We can print the date and the time information using the TimeList() function.

Syntax:

.. code-block:: ring

	TimeList() ---> List contains the time and date information.

The next table presents the list items

======  ===================================
index	value
======	===================================
1		abbreviated weekday name 
2		full weekday name
3		abbreviated month name
4		full month name
5		Date & Time
6		Day of the month
7		Hour (24)
8		Hour (12)
9		Day of the year
10		Month of the year
11		Minutes after hour
12		AM or PM
13		Seconds after the hour
14		Week of the year (sun-sat)
15		day of the week
16		date
17		time
18		year of the century		
19		year
20		time zone
21		percent sign
======	===================================

Example:

.. code-block:: ring

	/* Output:
	** Sun			abbreviated weekday name 
	** Sunday		full weekday name
	** May			abbreviated month name
	** May			full month name
	** 05/24/15 09:58:38	Date & Time
	** 24			Day of the month
	** 09			Hour (24)
	** 09			Hour (12)
	** 144			Day of the year
	** 05			Month of the year
	** 58			Minutes after hour
	** AM			AM or PM
	** 38			Seconds after the hour
	** 21			Week of the year (sun-sat)
	** 0			day of the week
	** 05/24/15		date
	** 09:58:38		time
	** 15			year of the century		
	** 2015			year
	** Arab Standard Time	time zone
	** %			percent sign
	*/
	
	See TimeList() 

Example:

.. code-block:: ring

	See "Day Name : " + TimeList()[2]	# Sunday

Example:

.. code-block:: ring

	See "Month Name : " + TimeList()[4]	# May

.. index:: 
	pair: Date and Time; AddDays()

AddDays() Function
==================

Syntax:

.. code-block:: ring

	AddDays(cDate,nDays) ---> Date from cDate and after nDays

Example:

.. code-block:: ring

	cDate = date()
	see cDate + nl			# 24/05/2015
	cDate = adddays(cDate,10)
	see cDate + nl			# 03/06/2015

.. index:: 
	pair: Date and Time; DiffDays()

DiffDays() Function
===================

Syntax:

.. code-block:: ring

	DiffDays(cDate1,cDate2) ---> number of days (Date1 - Date2)

Example:

.. code-block:: ring

	cDate1 = date()
	see cDate1 + nl						# 24/05/2015
	cDate2 = adddays(cDate1,10)
	see cDate2 + nl						# 03/06/2015
	see "DiffDays = " + diffdays(cDate1,cDate2) + nl	# -10
	see "DiffDays = " + diffdays(cDate2,cDate1) + nl	# 10
	
.. index:: 
	pair: Date and Time; EpochTime()

EpochTime() Function
====================

Syntax:

.. code-block:: ring

	EpochTime( cDate, cTime ) ---> Epoch Seconds

Example:

.. code-block:: ring

	###-------------------------------------------------------------
	# EpochTime()
	# Example ---  EpochSec = EpochTime( Date(), Time() )
	# Call Format: EpochSec = EpochTime( "15/07/2016", "10:15:30" )
	#              EpochSec = 1468577730
	#---------------------------------------------------------------

	Func EpochTime(Date, Time)

	    arrayDate = split(Date, "/")
	    arrayTime = split(Time, ":")

	    Year = arrayDate[3] ; Month  = arrayDate[2] ; Day    = arrayDate[1]
	    Hour = arrayTime[1] ; Minute = arrayTime[2] ; Second = arrayTime[3]

	    cDate1    = Day +"/"+ Month +"/"+ Year
	    cDate2    = "01/01/" + Year
	    DayOfYear = DiffDays( cDate1, cDate2)

	    ### Formula
	    tm_sec  = Second    * 1
	    tm_min  = Minute    * 60
	    tm_hour = Hour      * 3600
	    tm_yday = DayOfYear * 86400
	    tm_year = Year      - 1900

	    tm_year1 =         ( tm_year -  70)          * 31536000
	    tm_year2 = ( floor(( tm_year -  69) /   4 )) * 86400
	    tm_year3 = ( floor(( tm_year -   1) / 100 )) * 86400
	    tm_year4 = ( floor(( tm_year + 299) / 400 )) * 86400

	    ### Result
	    EpochSec = tm_sec + tm_min + tm_hour + tm_yday +
		       tm_year1 + tm_year2 - tm_year3 + tm_year4

	return EpochSec
	
