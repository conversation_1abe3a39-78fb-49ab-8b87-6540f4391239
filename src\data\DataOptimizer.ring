load "ziplib.ring"

Class DataOptimizer {
    

    func init {
        oZip = new Zip
    }

    func optimize(data) {
        if isString(data) {
            return compressString(data)
        }
        if isList(data) {
            if len(data) = 0 return [] ok
            
            # إذا كانت قائمة من الأزواج
            if isList(data[1]) {
                return sortByLength(data)
            }
            
            # إذا كان زوج واحد
            return data
        }
        return data
    }

    # ضغط ملف
    func compressFile(sourceFile, targetZip) {
        if !fexists(sourceFile) return null ok
        
        try {
            oZip{
                SetFileName(targetZip)
                Open("w")
                AddFile(sourceFile)
                Close()
            }
            return targetZip
        catch
            return null
        }
    }

    # ضغط مجلد
    func compressDirectory(sourceDir, targetZip) {
        if !dirExists(sourceDir) return null ok
        
        try {
            oZip{
                SetFileName(targetZip)
                Open("w")
                AddFile(sourceDir)
                Close()
            }
            return targetZip
        catch
            return null
        }
    }

    # فك ضغط ملف
    func extractFile(zipFile, targetDir) {
        if !fexists(zipFile) return null ok
        if !dirExists(targetDir) System('mkdir "' + targetDir + '"') ok
        
        try {
            oZip{
                SetFileName(zipFile)
                ExtractAllFiles(targetDir)
            }
            return targetDir
        catch
            return null
        }
    }

    # ضغط مجموعة ملفات
    func compressFiles(filesList, targetZip) {
        if len(filesList) = 0 return null ok
        
        try {
            oZip{
                SetFileName(targetZip)
                Open("w")
                for file in filesList {
                    if fexists(file) {
                        AddFile(file)
                    }
                }
                Close()
            }
            return targetZip
        catch
            return null
        }
    }

    # فحص محتويات ملف مضغوط
    func listZipContents(zipFile) {
        if !fexists(zipFile) return [] ok
        listContents = []
        try {
            oZip{
                SetFileName(zipFile)
                Open("r")
                for x = 1 to filescount()
                       add( listContents ,GetFileNameByIndex(x) )
                next
                Close()
            }
            return listContents
        catch
            return []
        }
    }

    private 

    oZip
    
    # ضغط نص
    func compressString(str) {
        if !isString(str) return str ok
        if len(str) = 0 return str ok
        
        try {
            tempFile = currentdir() + "/temp.txt"
            write(tempFile, str)
            zipFile = currentdir() + "/temp.zip"
            result = compressFile(tempFile, zipFile)
            remove(tempFile)
            return result
        catch
            return str
        }
    }

    # فرز حسب الطول
    func sortByLength(list) {
        for i = 1 to len(list)-1 {
            for j = i+1 to len(list) {
                if len(list[i][1]) + len(list[i][2]) < len(list[j][1]) + len(list[j][2]) {
                    temp = list[i]
                    list[i] = list[j]
                    list[j] = temp
                }
            }
        }
        return list
    }
    
}
