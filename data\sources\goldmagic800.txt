.. index:: 
	single: The Gold Magic 800 Game; Introduction

=======================
The Gold Magic 800 Game
=======================

In this chapter we will learn about the Gold Magic 800 Game

The game is developed using Ring, RingAllegro and RingOpenGL

After installing the package:

.. code-block:: none

	ringpm install goldmagic800

You will find the game in ring/applications/goldmagic800 folder

.. index:: 
	pair: The Gold Magic 800 Game; The Game Story

The Game Story
==============

Your friend discovered a unique and special box, created by the greatest wizard in the world 7,000 years ago. 
When you close the box and move it, you will find new gold underneath, making it an infinite source of wealth. 
The box resides in a special environment full of puzzles, protected by powerful magic that prevents anyone from entering. 
Your friend has designed a new robot using nanotechnology that can move the box with a remote control device. 
Your mission is to solve all the puzzles and secure this box forever, making you the richest person in the world.

.. index:: 
	pair: The Gold Magic 800 Game; How to play?

How to play?
============

The Gold Magic 800 is a puzzle game about moving your box through the right way.

The game is based on moving your box around to get gold score (=800) to open the 
First Door (Box Number 1) Then directly put your box on the Door (this will open the next door), 
Then continue to put your box on all of the next doors in the level, You need the score (800) only
for the first door, The next doors doesn't require this condition, but your way of gold will be
converted to a wall once you put the Box on any door, so select your path carefully. 

.. index:: 
	pair: The Gold Magic 800 Game; What will you learn?

What will you learn?
====================

1- Plan First

2- Move to your target directly then go to get the required resources

3- Look to the future when you evaluate the different solutions

4- Try to avoid mistakes, Also learn from them

5- Respect the Cost (800)

6- Focus and be careful

7- Be patient and Enjoy!

.. index:: 
	pair: The Gold Magic 800 Game; Screen Shots

Screen Shots
============

We can select the level

.. image:: gmshot2.png
	:alt: Gold Magic 800 - Screen Shot 2

The next screen shot for level (31)

.. image:: gmshot3.png
	:alt: Gold Magic 800 - Screen Shot 3

The Gold Magic 800 Level Editor 

.. image:: gmleveleditor.png
	:alt: Gold Magic 800 Level Editor


.. index:: 
	pair: The Gold Magic 800 Game; Source Code

Source Code
===========

You will find the Level Editor source code in this folder

https://github.com/ring-lang/ring/tree/master/applications/goldmagic800/editor

The user interface of the Level Editor is designed using the Ring Form Designer.

.. image:: gm800lefd.png
	:alt: Gold Magic 800 Level Editor

The next file contains the Level Editor Controller Class

https://github.com/ring-lang/ring/blob/master/applications/goldmagic800/editor/editorController.ring

You will find the Game Engine source code in this folder

https://github.com/ring-lang/ring/tree/master/applications/goldmagic800


