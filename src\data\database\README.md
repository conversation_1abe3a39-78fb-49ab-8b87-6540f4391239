# نظام إدارة قواعد البيانات

هذا المجلد يحتوي على مكونات نظام إدارة قواعد البيانات للمشروع.

## المكونات
- `DatabaseManager.ring`: المدير الرئيسي لقاعدة البيانات
- `QueryBuilder.ring`: منشئ الاستعلامات
- `Migrations.ring`: إدارة ترحيلات قاعدة البيانات
- `Models.ring`: نماذج البيانات

## الجداول الرئيسية
1. `translations`: سجلات الترجمة
2. `code_generations`: سجلات توليد الكود
3. `performance_metrics`: مقاييس الأداء
4. `users`: معلومات المستخدمين
5. `settings`: إعدادات النظام

## كيفية الاستخدام

### 1. تهيئة قاعدة البيانات
```ring
load "Models.ring"
# سيتم إنشاء قاعدة البيانات تلقائياً عند إنشاء أول نموذج
translation = new Translation
```

### 2. التعامل مع الترجمات
```ring
# إنشاء ترجمة جديدة
translation = new Translation
id = translation.create("Hello", "مرحبا", "en", "ar")

# البحث عن ترجمة
result = translation.find(id)
# أو البحث بالنص المصدر
results = translation.findBySourceText("Hello")

# جلب كل الترجمات
allTranslations = translation.all()
```

### 3. إدارة توليد الكود
```ring
# تسجيل كود تم توليده
codeGen = new CodeGeneration
id = codeGen.create("طباعة رسالة", 'See("Hello")', "Ring")

# البحث عن كود تم توليده
code = codeGen.find(id)

# البحث عن كل الأكواد بلغة معينة
ringCodes = codeGen.findByLanguage("Ring")
```

### 4. تسجيل وتتبع الأداء
```ring
# تسجيل مقاييس الأداء
metric = new PerformanceMetric
metric.create("translation", 0.5, 100.5, 0.95)

# الحصول على إحصائيات الأداء
stats = metric.getStats("translation")
```

### 5. استخدام منشئ الاستعلامات
```ring
query = new QueryBuilder("translations")
query.select(["source_text", "target_text"])
    .where("source_lang", "=", "en")
    .orderByDesc("created_at")
    .limit(10)

# الحصول على الاستعلام SQL
sqlQuery = query.build()
```

### 6. استعلامات متقدمة
```ring
# استخدام JOIN
query = new QueryBuilder("translations") {
    select(["translations.*", "users.name as translator"])
    join("users", "translations.user_id", "=", "users.id")
    where("translations.source_lang", "=", "en")
}

# استخدام WHERE IN
query = new QueryBuilder("translations") {
    whereIn("source_lang", ["en", "fr", "es"])
}

# استخدام BETWEEN
query = new QueryBuilder("performance_metrics")
query.whereBetween("execution_time", "0.1", "0.5")

# استخدام GROUP BY مع HAVING
query = new QueryBuilder("translations") {
    select(["source_lang", "COUNT(*) as count"])
    groupBy("source_lang")
    having("count", ">", "100")
}
# استخدام UNION
query1 = new QueryBuilder("translations")
query1.where("source_lang", "=", "en")

query2 = new QueryBuilder("translations")
query2.where("source_lang", "=", "ar")

query1.union(query2)

# استخدام LEFT JOIN
query = new QueryBuilder("translations") {
    select(["translations.*", "performance_metrics.execution_time"])
    leftJoin("performance_metrics", 
              "translations.id", "=", 
              "performance_metrics.translation_id")
}

# استخدام عدة شروط WHERE
query = new QueryBuilder("translations"){
    where("source_lang", "=", "en")
    whereNull("error_message")
    whereNotNull("completed_at")
    orWhere("priority", "=", "high")
}

# ترتيب متقدم
query = new QueryBuilder("translations")
query.orderByRaw("CASE WHEN priority = 'high' THEN 1 
                       WHEN priority = 'medium' THEN 2 
                       ELSE 3 END")
```

### 7. أمثلة متقدمة للاستخدام

#### تحليل أداء الترجمة
```ring
query = new QueryBuilder("translations") {
    select([
        "source_lang",
        "target_lang",
        "AVG(execution_time) as avg_time",
        "COUNT(*) as total_translations",
        "SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as successful"
    ])
    leftJoin("performance_metrics", 
            "translations.id", "=", 
            "performance_metrics.translation_id")
    groupBy(["source_lang", "target_lang"])
    having("total_translations", ">", "1000")
    orderByDesc("avg_time")
}
```

#### تتبع نشاط المستخدمين
```ring
query = new QueryBuilder("translations") {
    select([
        "users.name",
        "COUNT(*) as translations_count",
        "AVG(performance_metrics.execution_time) as avg_performance"
    ])
    join("users", "translations.user_id", "=", "users.id")
    leftJoin("performance_metrics", 
            "translations.id", "=", 
            "performance_metrics.translation_id")
    whereBetween("translations.created_at", 
                "DATE('now', '-30 days')", 
                "DATE('now')")
    groupBy("users.id")
    having("translations_count", ">", "50")
    orderByDesc("translations_count")
}
```

### 8. تنظيف وتحضير البيانات
```ring
# إنشاء منظف البيانات
cleaner = new DataCleaner

# تنظيف نص
cleanText = cleaner.cleanText("  مرحبا  بكم\t\n  ")

# تنظيف جداول قاعدة البيانات
cleaner.cleanAllTables()

# تنظيف جدول الترجمات فقط
cleaner.cleanTranslationsTable()

# إزالة السجلات المكررة
cleaner.removeDuplicateTranslations()

# التحقق من صحة مقاييس الأداء
cleaner.validatePerformanceMetrics()
```

### 9. تحديث وإدارة البيانات
```ring
# إنشاء محدث البيانات
updater = new DataUpdater

# تحديث مجموعة من الترجمات
translations = [
    ["Hello", "مرحبا", "en", "ar"],
    ["Good morning", "صباح الخير", "english", "arabic"]
]
updater.batchUpdateTranslations(translations)

# تحديث مقاييس الأداء
metrics = [
    ["translation", 0.5, 100.5, 0.95],
    ["code_generation", 1.2, 150.3, 0.88]
]
updater.updatePerformanceMetrics(metrics)

# أرشفة البيانات القديمة
updater.archiveOldData(30)  # أرشفة البيانات الأقدم من 30 يوم

# تحسين قاعدة البيانات
updater.optimizeTables()

# إنشاء نسخة احتياطية
updater.createBackup("backup.db")

# استعادة من نسخة احتياطية
updater.restoreFromBackup("backup.db")
```

### 10. أفضل ممارسات تنظيف وتحديث البيانات

1. **تنظيف البيانات بشكل دوري**
   ```ring
   # تشغيل عملية التنظيف الشاملة أسبوعياً
   cleaner = new DataCleaner
   cleaner.cleanAllTables()
   ```

2. **التحقق من صحة البيانات قبل الإدخال**
   ```ring
   if cleaner.validateTranslationData(sourceText, targetText, sourceLang, targetLang)
       # إدخال البيانات
   ok
   ```

3. **النسخ الاحتياطي المنتظم**
   ```ring
   updater = new DataUpdater
   backupPath = "backups/backup_" + date("YYYY_MM_DD") + ".db"
   updater.createBackup(backupPath)
   ```

4. **تحسين الأداء**
   ```ring
   # تحسين قاعدة البيانات بعد العمليات الكبيرة
   updater.optimizeTables()
   ```

5. **استخدام المعاملات للعمليات المتعددة**
   ```ring
   db.sqlite_execute(db.db, "BEGIN TRANSACTION")
   try
       # عمليات متعددة
       db.sqlite_execute(db.db, "COMMIT")
   catch
       db.sqlite_execute(db.db, "ROLLBACK")
   done
   ```

## أفضل الممارسات
1. **إغلاق الاتصال**: تأكد من إغلاق اتصال قاعدة البيانات بعد الانتهاء
   ```ring
   db.close()
   ```

2. **التعامل مع الأخطاء**: استخدم try/catch للتعامل مع أخطاء قاعدة البيانات
   ```ring
   try
       translation.create("Hello", "مرحبا", "en", "ar")
   catch
       ? "حدث خطأ: " + cCatchError
   done
   ```

3. **الأداء**: استخدم `limit` عند التعامل مع كميات كبيرة من البيانات
   ```ring
   # جلب أحدث 100 ترجمة فقط
   query = new QueryBuilder("translations")
   query.orderByDesc("created_at").limit(100)
   ```

## ملاحظات هامة
- قاعدة البيانات تُنشأ تلقائياً في نفس مجلد التطبيق باسم `database.db`
- جميع الجداول تحتوي على حقل `created_at` يُملأ تلقائياً
- استخدم `QueryBuilder` لبناء استعلامات معقدة
- النماذج توفر واجهة بسيطة للعمليات الشائعة

## ملاحظات إضافية
- نظام تنظيف البيانات يعمل تلقائياً على تنظيف النصوص وتوحيد رموز اللغات
- نظام التحديث يدعم العمليات المجمعة لتحسين الأداء
- يتم التحقق من صحة البيانات قبل إدخالها
- النظام يدعم الأرشفة التلقائية للبيانات القديمة
- يوفر النظام آليات للنسخ الاحتياطي والاستعادة
