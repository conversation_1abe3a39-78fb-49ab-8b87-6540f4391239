.. index:: 
     single: Create your first extension; Introduction

===========================
Create your first extension
===========================

In this chapter we will create RingBeep

RingBeep is a simple extension for the beep() function in Windows API

Just a simple example about creating extensions

.. index:: 
     pair: Create your first extension; Location

Location
========

You will find this extension in the ring/extensions/ringbeep folder

.. index:: 
     pair: Create your first extension; Steps to create the extension

Steps to create the extension
=============================

At first we write the configuration file in : ringbeep.cf

.. code-block:: none

	notepad ringbeep.cf

The file ringbeep.cf contains

.. code-block:: none

	<code>
	#include "windows.h"
	</code>

	int Beep(int dwFreq,int dwDuration)

Then we run : gencode.bat to generate ringbeep.c

.. code-block:: none

	gencode.bat

The gencode.bat contains the next command to call Ring Extensions Generator

.. code-block:: none

	ring ..\codegen\parsec.ring ringbeep.cf ringbeep.c

Then we build the extension using :

.. code-block:: none

	buildvc.bat

The file buildvc.bat contains the next commands to build the extension using Visual C/C++

.. code-block:: none

	cls
	call ../../src/locatevc.bat
	cl /c /DEBUG ringbeep.c -I"..\..\include"
	link /DEBUG ringbeep.obj  ..\..\lib\ring.lib kernel32.lib /DLL /OUT:ringbeep.dll ^
								/SUBSYSTEM:CONSOLE,"5.01" 
	del ringbeep.obj

.. index:: 
     pair: Create your first extension; Testing the extension

Testing the extension
=====================

Then we test the function using

.. code-block:: none

	ring test.ring

The file test.ring contains

.. code-block:: ring

	loadlib("ringbeep.dll")

	for f = 750 to 1000 step 50
		beep(f,300)
	next

