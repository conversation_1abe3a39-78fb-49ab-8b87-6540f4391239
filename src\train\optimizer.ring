load "stdlibcore.ring"

# محسن Adam للتدريب
Class AdamOptimizer {
    # المعلمات
    learningRate = 0.001          # معدل التعلم
    beta1        = 0.9            # معامل الزخم الأول
    beta2        = 0.999          # معامل الزخم الثاني
    epsilon      = 0.00000001    # قيمة صغيرة لتجنب القسمة على صفر
    
    # متغيرات الحالة
    m             # المتوسط المتحرك للتدرج
    v             # المتوسط المتحرك لمربع التدرج
    t             # خطوة الزمن
    
    func init(lr, b1, b2, eps) {
        learningRate = lr
        beta1 = b1
        beta2 = b2
        epsilon = eps
        
        m = []
        v = []
        t = 0
    }
    
    # تحديث معلمات النموذج
    func update(model, gradients) {
        t = t + 1
        
        # تهيئة m و v إذا كانت فارغة
        if len(m) = 0 {
            for grad in gradients {
                m + 0
                v + 0
            }
        }
        
        # تحديث كل معلمة
        for i = 1 to len(gradients) {
            # تحديث المتوسطات المتحركة
            m[i] = beta1 * m[i] + (1 - beta1) * gradients[i]
            v[i] = beta2 * v[i] + (1 - beta2) * gradients[i] * gradients[i]
            
            # تصحيح التحيز
            mHat = m[i] / (1 - pow(beta1, t))
            vHat = v[i] / (1 - pow(beta2, t))
            
            # تحديث المعلمات
            model.parameters[i] -= learningRate * mHat / (sqrt(vHat) + epsilon)
        }
    }
    
    # إعادة تعيين الحالة
    func reset() {
        m = []
        v = []
        t = 0
    }
}
