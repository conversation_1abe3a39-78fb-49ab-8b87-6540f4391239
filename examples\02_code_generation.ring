load "src/core/transformer.ring"

# إنشاء كائن الترانسفورمر
transformer = new Transformer

# أمثلة لتوليد الكود من وصف نصي
descriptions = [
    "برنامج يطبع مرحباً بالعالم",
    "دالة لجمع رقمين وإرجاع النتيجة",
    "صنف يمثل حساب بنكي مع دوال للإيداع والسحب",
    "برنامج يقرأ ملف نصي ويعد عدد الكلمات فيه"
]

? "توليد الكود من الوصف النصي:"
? "=========================="
for desc in descriptions
    ? "الوصف: " + desc
    ? "الكود المولد:"
    ? "------------"
    generatedCode = transformer.generateCode(desc)
    ? generatedCode
    ? "============================================="
    ? ""
next

# مثال لتوليد كود مع معاملات محددة
customDesc = "دالة تأخذ مصفوفة وتقوم بترتيبها تصاعدياً"
parameters = [
    "algorithm": "bubble",
    "dataType": "number",
    "includeComments": true
]

? "توليد كود مع معاملات مخصصة:"
? "============================"
? "الوصف: " + customDesc
? "المعاملات:"
for key, value in parameters
    ? "- " + key + ": " + value
next
? "الكود المولد:"
? "------------"
customCode = transformer.generateCodeWithParams(customDesc, parameters)
? customCode
