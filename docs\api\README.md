# توثيق واجهة برمجة التطبيقات (API)

## نظرة عامة
Smart Transformer API هي واجهة برمجة تطبيقات RESTful تقدم خدمات الترجمة وتوليد الكود. تم تصميمها لتكون سهلة الاستخدام وقوية وقابلة للتطوير.

## المتطلبات الأساسية
- Ring Programming Language
- SQLite3
- المكتبات المطلوبة:
  ```ring
  load "stdlib.ring"
  load "weblib.ring"
  load "jsonlib.ring"
  load "sqlitelib.ring"
  ```

## التثبيت والإعداد
1. تثبيت Ring:
   ```bash
   # تثبيت Ring من الموقع الرسمي
   https://ring-lang.github.io/download.html
   ```

2. تثبيت المكتبات:
   ```ring
   ringpm install stdlib
   ringpm install weblib
   ringpm install jsonlib
   ringpm install sqlitelib
   ```

3. تكوين الإعدادات:
   - نسخ `config.example.ring` إلى `config.ring`
   - تعديل الإعدادات حسب بيئتك

## النقاط النهائية (Endpoints)

### المصادقة
#### تسجيل الدخول
```http
POST /api/v1/auth/login
```
المعاملات:
```json
{
    "username": "string",
    "password": "string"
}
```
الاستجابة:
```json
{
    "token": "string",
    "user": {
        "id": "integer",
        "username": "string",
        "role": "string"
    }
}
```

### الترجمة
#### ترجمة نص
```http
POST /api/v1/translate
```
المعاملات:
```json
{
    "text": "string",
    "source_lang": "string (optional, default: en)",
    "target_lang": "string (optional, default: ar)"
}
```
الاستجابة:
```json
{
    "original": "string",
    "translated": "string",
    "source_lang": "string",
    "target_lang": "string"
}
```

#### ترجمة مجموعة نصوص
```http
POST /api/v1/translate/batch
```
المعاملات:
```json
{
    "texts": ["string"],
    "source_lang": "string",
    "target_lang": "string"
}
```

### توليد الكود
#### توليد كود من وصف
```http
POST /api/v1/code/generate
```
المعاملات:
```json
{
    "description": "string",
    "language": "string (optional)",
    "with_tests": "boolean (optional)",
    "with_docs": "boolean (optional)"
}
```

## المصادقة
- يستخدم النظام JWT للمصادقة
- يجب إرسال التوكن في ترويسة `Authorization`
- مثال:
  ```http
  Authorization: Bearer your-jwt-token
  ```

## معالجة الأخطاء
الأخطاء القياسية:
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 500: Internal Server Error

## أفضل الممارسات
1. استخدام HTTPS دائماً
2. تخزين التوكن بشكل آمن
3. معالجة الأخطاء بشكل صحيح
4. استخدام التخزين المؤقت عند الحاجة
