class CacheService
    cache
    
    func init
        cache = []
    
    func get key
        if cache[key] and cache[key][:expiry] > time()
            return cache[key][:value]
        ok
        return null
    
    func set key, value, timeout
        cache[key] = [
            :value = value,
            :expiry = time() + timeout
        ]
    
    func delete key
        delete(cache, key)
    
    func clear
        cache = []
    
    func cleanup
        for key in cache
            if cache[key][:expiry] <= time()
                delete(cache, key)
            ok
        next
