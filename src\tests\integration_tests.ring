load "stdlibcore.ring"
load "unit_tests.ring"
load "../core/transformer.ring"
load "../core/parallel_processor.ring"
load "../train/trainer.ring"

# اختبارات التكامل
Class IntegrationTests {
    # المتغيرات
    transformer
    processor
    trainer
    tests
    
    func init {
        transformer = new SmartTransformer()
        processor = new ParallelProcessor(4)
        trainer = new TransformerTrainer()
        tests = new UnitTests()
    }
    
    # تشغيل جميع اختبارات التكامل
    func runAllTests {
        ? "=== بدء اختبارات التكامل ==="
        
        # اختبار التدريب والتقييم
        testTrainingWorkflow()
        
        # اختبار الترجمة
        testTranslationPipeline()
        
        # اختبار توليد الكود
        testCodeGenerationPipeline()
        
        # اختبار المعالجة المتوازية
        testParallelProcessing()
        
        ? "=== اكتملت اختبارات التكامل ==="
    }
    
    # اختبار سير عمل التدريب
    func testTrainingWorkflow {
        ? "اختبار سير عمل التدريب..."
        
        try {
            # تحميل بيانات الاختبار
            trainer.loadTrainingData()
            
            # تدريب لحلقة واحدة
            trainer.trainOneEpoch()
            
            # تقييم النموذج
            results = trainer.evaluate()
            
            assert(results != NULL, "فشل التقييم")
            ? "✓ نجح اختبار سير عمل التدريب"
        catch
            ? "✗ فشل اختبار سير عمل التدريب: " + cCatchError
        }
    }
    
    # اختبار خط أنابيب الترجمة
    func testTranslationPipeline {
        ? "اختبار خط أنابيب الترجمة..."
        
        try {
            # إنشاء مهمة ترجمة
            task = new Task {
                type = "translation"
                data = "مرحباً بالعالم"
                direction = "ar-en"
            }
            
            # إضافة المهمة للمعالج
            processor.addTask(task)
            
            # انتظار النتيجة
            sleep(1000)
            
            # التحقق من النتيجة
            assert(task.result != NULL, "لم يتم إكمال الترجمة")
            ? "✓ نجح اختبار خط أنابيب الترجمة"
        catch
            ? "✗ فشل اختبار خط أنابيب الترجمة: " + cCatchError
        }
    }
    
    # اختبار خط أنابيب توليد الكود
    func testCodeGenerationPipeline {
        ? "اختبار خط أنابيب توليد الكود..."
        
        try {
            # إنشاء مهمة توليد كود
            task = new Task {
                type = "code_generation"
                data = "create a function to calculate factorial"
                language = "ring"
            }
            
            # إضافة المهمة للمعالج
            processor.addTask(task)
            
            # انتظار النتيجة
            sleep(1000)
            
            # التحقق من النتيجة
            assert(task.result != NULL, "لم يتم توليد الكود")
            ? "✓ نجح اختبار خط أنابيب توليد الكود"
        catch
            ? "✗ فشل اختبار خط أنابيب توليد الكود: " + cCatchError
        }
    }
    
    # اختبار المعالجة المتوازية
    func testParallelProcessing {
        ? "اختبار المعالجة المتوازية..."
        
        try {
            # إنشاء مجموعة مهام
            tasks = []
            for i = 1 to 10 {
                tasks + new Task {
                    type = "test"
                    data = "task " + i
                }
            }
            
            # إضافة المهام للمعالج
            for task in tasks {
                processor.addTask(task)
            }
            
            # انتظار اكتمال جميع المهام
            sleep(2000)
            
            # التحقق من النتائج
            completed = 0
            for task in tasks {
                if task.result != NULL {
                    completed++
                }
            }
            
            assert(completed = 10, "لم تكتمل جميع المهام")
            ? "✓ نجح اختبار المعالجة المتوازية"
        catch
            ? "✗ فشل اختبار المعالجة المتوازية: " + cCatchError
        }
    }
    
    # وظائف مساعدة
    func assert(condition, message) {
        if not condition {
            raise(message)
        }
    }
}
