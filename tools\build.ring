load "stdlibcore.ring"

# نظام البناء والتشغيل الآلي
Class BuildSystem {
    # المتغيرات
    sourceDir
    buildDir
    testsDir
    
    func init {
        sourceDir = "../src"
        buildDir = "../build"
        testsDir = "../src/tests"
    }
    
    # بناء المشروع
    func build {
        ? "=== بدء عملية البناء ==="
        
        # إنشاء مجلد البناء
        if not isDirectory(buildDir) {
            System("mkdir " + buildDir)
        }
        
        # نسخ الملفات المصدرية
        copySourceFiles()
        
        # تجميع المكتبات
        compileLibraries()
        
        # تشغيل الاختبارات
        runTests()
        
        ? "=== اكتملت عملية البناء ==="
    }
    
    # نسخ الملفات المصدرية
    func copySourceFiles {
        ? "نسخ الملفات المصدرية..."
        System("xcopy /E /I " + sourceDir + " " + buildDir)
    }
    
    # تجميع المكتبات
    func compileLibraries {
        ? "تجميع المكتبات..."
        
        # تجميع المكتبات الأساسية
        compileCore()
        
        # تجميع واجهة المستخدم
        compileGUI()
        
        # تجميع الأدوات المساعدة
        compileUtils()
    }
    
    # تجميع النواة
    func compileCore {
        files = dir(sourceDir + "/core")
        for file in files {
            if right(file.name, 5) = ".ring" {
                ? "تجميع: " + file.name
                System("ring " + sourceDir + "/core/" + file.name)
            }
        }
    }
    
    # تجميع واجهة المستخدم
    func compileGUI {
        files = dir(sourceDir + "/gui")
        for file in files {
            if right(file.name, 5) = ".ring" {
                ? "تجميع: " + file.name
                System("ring " + sourceDir + "/gui/" + file.name)
            }
        }
    }
    
    # تجميع الأدوات المساعدة
    func compileUtils {
        files = dir(sourceDir + "/utils")
        for file in files {
            if right(file.name, 5) = ".ring" {
                ? "تجميع: " + file.name
                System("ring " + sourceDir + "/utils/" + file.name)
            }
        }
    }
    
    # تشغيل الاختبارات
    func runTests {
        ? "تشغيل الاختبارات..."
        System("ring " + testsDir + "/unit_tests.ring")
        System("ring " + testsDir + "/integration_tests.ring")
        System("ring " + testsDir + "/performance_tests.ring")
    }
    
    # تنظيف مجلد البناء
    func clean {
        ? "تنظيف مجلد البناء..."
        if isDirectory(buildDir) {
            System("rmdir /S /Q " + buildDir)
        }
    }
    
    # التحقق من وجود مجلد
    func isDirectory(path) {
        try {
            return isDirectory(path)
        catch
            return false
        }
    }
}
