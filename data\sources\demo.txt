.. index:: 
	single: Demo Programs; Introduction

=============
Demo Programs
=============

In this chapter we will see simple demo programs

* Language Shell
* Main Menu

.. index:: 
	pair: Demo Programs; Language Shell

Language Shell
==============

We can create simple interactive programming environment using the next program

.. code-block:: ring

	while true 
		see nl + "code:> "
		give cCode 
		try
			eval(cCode) 
		catch
			see cCatchError
		done
	end

Output:

.. code-block:: ring

	code:> see "hello world"
	hello world
	code:> for x = 1 to 10 see x + nl next
	1
	2
	3
	4
	5
	6
	7
	8
	9
	10

	code:> func test see "Hello from test" + nl

	code:> test()
	Hello from test

	code:> bye

.. index:: 
	pair: Demo Programs; Main Menu

Main Menu
=========

Example:

.. code-block:: ring

	# Demo Program

	while true

		see "

		Main Menu
		===========
		[1] Say Hello
		[2] Sum two numbers
		[3] Stars
		[4] Fact
		[5] Exit

		" give nMenu see nl

		# we can use Switch-ON-Other-OFF instead of IF-BUT-ELSE-OK

		Switch nMenu
		On 1 sayhello() 
		On 2 Sum()
		On 3 Stars()
		On 4 
			see "Enter Number : " give x
			see "Output : " 

			Try	
				see Fact(number(x))
			Catch
				see "Error in parameters!" + nl
			Done

		On "5" return 
		Other see "bad option" + nl
		Off

	end

	func sayhello
		see "Enter your name ? " give fname
		see "Hello " + fname + nl

	func sum
		see "number 1 : " give num1 see "number 2 : " give num2
		see "Sum : " see 0 + num1 + num2

	func stars
		for x = 1 to 10
			see space(8)
			for y = 1 to x see "*" next see nl
		next

	func fact x if x = 0 return 1 else return x * fact(x-1) ok

	func space x y = "" for t=1 to x y += " " next return y

Output:

.. code-block:: ring


	        Main Menu
        	===========
	        [1] Say Hello
        	[2] Sum two numbers
	        [3] Stars
        	[4] Fact
	        [5] Exit

	        1

	Enter your name ? Mahmoud Fayed
	Hello Mahmoud Fayed


	        Main Menu
        	===========
	        [1] Say Hello
        	[2] Sum two numbers
	        [3] Stars
        	[4] Fact
	        [5] Exit

	        2

	number 1 : 3
	number 2 : 4
	Sum : 7

	        Main Menu
        	===========
	        [1] Say Hello
        	[2] Sum two numbers
	        [3] Stars
        	[4] Fact
	        [5] Exit

	        3

	        *
        	**
	        ***
        	****
	        *****
        	******
	        *******
	        ********
        	*********
	        **********


	        Main Menu
        	===========
	        [1] Say Hello
	        [2] Sum two numbers
        	[3] Stars
	        [4] Fact
	        [5] Exit

	        4

	Enter Number : 5
	Output : 120

	        Main Menu
        	===========
	        [1] Say Hello
        	[2] Sum two numbers
	        [3] Stars
        	[4] Fact
	        [5] Exit

	        5
