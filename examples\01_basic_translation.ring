load "src/core/transformer.ring"

# إنشاء كائن الترانسفورمر
transformer = new Transformer

# أمثلة للترجمة من الإنجليزية إلى العربية
englishTexts = [
    "Hello, how are you?",
    "I love programming in Ring",
    "The weather is beautiful today"
]

? "الترجمة من الإنجليزية إلى العربية:"
? "================================="
for text in englishTexts
    translated = transformer.translate(text, "en", "ar")
    ? "النص الأصلي: " + text
    ? "الترجمة: " + translated
    ? ""
next

# أمثلة للترجمة من العربية إلى الإنجليزية
arabicTexts = [
    "مرحباً، كيف حالك؟",
    "أحب البرمجة بلغة رينج",
    "الطقس جميل اليوم"
]

? "الترجمة من العربية إلى الإنجليزية:"
? "================================="
for text in arabicTexts
    translated = transformer.translate(text, "ar", "en")
    ? "النص الأصلي: " + text
    ? "الترجمة: " + translated
    ? ""
next
