class APIConfig
    # إعدادات الخادم
    SERVER_HOST = "0.0.0.0"
    SERVER_PORT = 8080
    DEBUG_MODE = true
    
    # إعدادات قاعدة البيانات
    DB_HOST = "localhost"
    DB_NAME = "smart_transformer"
    DB_USER = "admin"
    DB_PASS = "secure_password"
    
    # إعدادات JWT
    JWT_SECRET = "your-secret-key"
    JWT_EXPIRY = 3600  # ساعة واحدة
    
    # حدود الطلبات
    RATE_LIMIT = 100  # طلب في الدقيقة
    MAX_CONTENT_LENGTH = 10 * 1024 * 1024  # 10 ميجابايت
    
    # إعدادات التحسين
    CACHE_ENABLED = true
    CACHE_TIMEOUT = 300  # 5 دقائق
    
    # إعدادات الأمان
    CORS_ORIGINS = ["http://localhost:3000", "https://yourdomain.com"]
    SSL_ENABLED = true
    SSL_CERT = "path/to/cert.pem"
    SSL_KEY = "path/to/key.pem"
    
    # إعدادات التسجيل
    LOG_LEVEL = "info"
    LOG_FILE = "api.log"
    
    # إعدادات الترجمة
    DEFAULT_SOURCE_LANG = "en"
    DEFAULT_TARGET_LANG = "ar"
    SUPPORTED_LANGUAGES = ["ar", "en", "fr", "es"]
    
    # إعدادات توليد الكود
    CODE_GEN_TIMEOUT = 30  # ثانية
    MAX_CODE_LENGTH = 1000000  # حرف
