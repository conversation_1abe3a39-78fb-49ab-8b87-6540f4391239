load "stdlibcore.ring"
load "../utils/secfunc.ring"

class JWT
    func sign payload, secret
        # Simple implementation using HMAC-SHA256
        header = [ :alg = "HS256", :typ = "JWT" ]
        encodedHeader = base64_encode(json_encode(header))
        encodedPayload = base64_encode(json_encode(payload))
        signatureInput = encodedHeader + "." + encodedPayload
        signature = hash_hmac_sha256(signatureInput, secret)
        encodedSignature = base64_encode(signature)
        return encodedHeader + "." + encodedPayload + "." + encodedSignature

    func verify token, secret
        parts = split(token, ".")
        if len(parts) != 3
            return false
        ok

        encodedHeader = parts[1]
        encodedPayload = parts[2]
        encodedSignature = parts[3]

        signatureInput = encodedHeader + "." + encodedPayload
        expectedSignature = base64_encode(hash_hmac_sha256(signatureInput, secret))

        if encodedSignature != expectedSignature
            return false
        ok

        payload = json_decode(base64_decode(encodedPayload))
        return payload