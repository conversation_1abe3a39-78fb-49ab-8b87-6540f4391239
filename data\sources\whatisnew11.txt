.. index:: 
	single: What is new in Ring 1.11?; Introduction

=========================
What is new in Ring 1.11?
=========================

In this chapter we will learn about the changes and new features in Ring 1.11 release.

.. index:: 
	pair: What is new in Ring 1.11?; List of changes and new features

List of changes and new features
================================

Ring 1.11 comes with the next features!

* More 3D Samples 
* Checkers Game
* Sokoban Game
* Maze Game
* Snake Game
* Sudoku Game
* Desktop Screen Shot Application
* Text To Speech Application
* RingRayLib Extension
* ZeroLib Library
* StdLib - More Functions
* Better RingQt
* Better Performance
* Better Documentation
* More Improvements

.. index:: 
	pair: What is new in Ring 1.11?; More 3D Samples

More 3D Samples
===============

Ring 1.11 comes with more 3D samples based on Qt3D 

* Folder : ring/samples/other/UsingQt3D (Contains 18 samples)

.. image:: qt3dex18.png
	:alt: Qt3D Example 18

.. index:: 
	pair: What is new in Ring 1.11?; Checkers Game

Checkers Game
=============

It plays the - Must Jump - version of Checkers, The International Rules 

It handles various invalid moves, invalid jumps, and must jumps. 

The squares are colored to reflect errors.

The squares are colored to show source and destination of the move or jump.

.. image:: checkersgame.png
	:alt: Checkers Game

.. index:: 
	pair: What is new in Ring 1.11?; Sokoban Game

Sokoban Game
============

A quick implementation for the Sokoban Game

Developed using Ring Game Engine for 2D Games in 2 hours (Less than 300 lines of code)

.. image:: sokoban.jpg
	:alt: Sokoban Game


.. index:: 
	pair: What is new in Ring 1.11?; Maze Game

Maze Game
=========

A quick implementation for the Maze Game

Developed using Ring Game Engine for 2D Games (Around 100 lines of code)

Also the game comes with a level designer (Developed in 10 minutes, 37 Lines of code)

.. image:: maze.png
	:alt: Maze Game

.. index:: 
	pair: What is new in Ring 1.11?; Snake Game

Snake Game
==========

A quick implementation for the Snake Game

Developed using Ring Game Engine for 2D Games (Around 200 lines of code)

.. image:: snake.png
	:alt: Snake Game

.. index:: 
	pair: What is new in Ring 1.11?; Sudoku Game

Sudoku Game
===========

Fill a 9 x 9 grid with digits so that each column, each row, and each of the nine 3 x 3 subgrids that
compose the grid contain all of the digits from 1 to 9.

.. image:: sudoku.jpg
	:alt: Sudoku Game

.. index:: 
	pair: What is new in Ring 1.11?; Desktop Screen Shot Application

Desktop Screen Shot Application
===============================

.. image:: dssapp.png
	:alt: Desktop Screen Shot Application

.. index:: 
	pair: What is new in Ring 1.11?; Text To Speech Application 

Text To Speech Application
==========================

.. image:: ttsapp.png
	:alt: Text To Speech Application


.. index:: 
	pair: What is new in Ring 1.11?; RingRayLib Extension

RingRayLib Extension
====================

Ring 1.11 comes with new extension to support the RayLib game programming library

Example:

.. code-block:: ring

	load "raylib.ring"

	screenWidth 	= 800
	screenHeight 	= 450

	InitWindow(screenWidth, screenHeight, "raylib [core] example - basic window")

	SetTargetFPS(60)

	while !WindowShouldClose() 
		BeginDrawing()
		ClearBackground(RED)
		DrawText("Congrats! You created your first window!", 190, 200, 20, WHITE)
	        EndDrawing()
	end

	CloseWindow()

Output:

.. image:: raylib_ex1.png
	:alt: RayLib Example

Example:

.. code-block:: ring

	load "raylib.ring"

	screenWidth = 800
	screenHeight = 450

	InitWindow(screenWidth, screenHeight, "raylib [shapes] example - basic shapes drawing")

	SetTargetFPS(60)

	while !WindowShouldClose()

		BeginDrawing()

		ClearBackground(RAYWHITE)

		DrawText("some basic shapes available on raylib", 20, 20, 20, DARKGRAY)
		DrawCircle(screenWidth/4, 120, 35, DARKBLUE)
		DrawRectangle(screenWidth/4*2 - 60, 100, 120, 60, RED)
		DrawRectangleLines(screenWidth/4*2 - 40, 320, 80, 60, ORANGE)  
		DrawRectangleGradientH(screenWidth/4*2 - 90, 170, 180, 130, MAROON, GOLD)

		DrawTriangle(Vector2(screenWidth/4*3, 80),
				 Vector2(screenWidth/4*3 - 60, 150),
				 Vector2(screenWidth/4*3 + 60, 150), VIOLET)

		DrawPoly(Vector2(screenWidth/4*3, 320), 6, 80, 0, BROWN)

		DrawCircleGradient(screenWidth/4, 220, 60, GREEN, SKYBLUE)

		DrawLine(18, 42, screenWidth - 18, 42, BLACK)
		DrawCircleLines(screenWidth/4, 340, 80, DARKBLUE)
		DrawTriangleLines(Vector2(screenWidth/4*3, 160),
				  Vector2(screenWidth/4*3 - 20, 230),
				  Vector2(screenWidth/4*3 + 20, 230), DARKBLUE)
		EndDrawing()

	end

	CloseWindow()

Output:

.. image:: ex1_basicshapes.png
	:alt: RayLib Example

.. index:: 
	pair: What is new in Ring 1.11?; ZeroLib Library

ZeroLib Library
===============

Ring 1.11 comes with the ZeroLib library that contains classes for Lists and Strings where the index starts from 0.

Example:

.. code-block:: ring

	load "zerolib.ring"

	? "Using List - Index start from 0"
	List = Z( [1,2,3] )
	List.Add(4)
	List.Add(5)
	? List[0]
	? List[1]
	? List[2]
	? List[3]
	? List[4]
	nIndex = List.find(2)
	? "Find(2) = " + nIndex
	List.delete(0)
	? "After deleting the first item : List[0]" 
	? "Now List[0] = " + List[0] 

	? "Using String - Index start from 0"
	String = Z( "Welcome" )
	? String[0]
	? String[1]
	? String[2]
	? String[3]
	? String[4]
	? String[5]
	? String[6]

Output:

.. code-block:: ring 

	Using List - Index start from 0
	1
	2
	3
	4
	5
	Find(2) = 1
	After deleting the first item : List[0]
	Now List[0] = 2
	Using String - Index start from 0
	W
	e
	l
	c
	o
	m
	e

	
.. index:: 
	pair: What is new in Ring 1.11?; StdLib - More Functions

StdLib - More Functions
=======================

The next functions are added to the StdLib

* IsListContainsItems(aParent,aChild)
* IsBetween(nNumber,nMin,nMax)
* TimeInfo(cInformation)

Example:

.. code-block:: ring

	load "stdlibcore.ring"
	? "Using the IsListContainsItems() function" 
	aList1 = "a":"z"
	aList2 = [:h,:l,:p,:u]
	? IsListContainsItems(aList1,aList2)
	? "Using the IsBetween() function"
	? isBetween(1,3,4)
	? isBetween(4,1,6)
	? "Using the TimeInfo() function"
	? timeInfo(:date)
	? timeInfo(:year)
	? timeInfo(:time)
	? timeInfo(:hour_12)

Output:

.. code-block:: ring

	Using the IsListContainsItems() function
	1
	Using the IsBetween() function
	0
	1
	Using the TimeInfo() function
	05/24/19
	2019
	15:30:33
	03

For more information about these functions, see the StdLib functions chapter.

.. index:: 
	pair: What is new in Ring 1.11?; Better RingQt 

Better RingQt
=============

* Building RingQt using Qt 5.12.3

* RingQt for Android - project file is updated to include webview module when we have it

* QString Class - New Methods

    	* compare()
	* contains()
	* indexOf()
	* insert()
	* isRightToLeft()
	* remove()
	* repeated()
	* replace()
	* startsWith()
	* endsWith()
	* toHtmlEscaped()
	* clear()
	* isnull()
	* resize()
	* fill()

* QAxBase & QVariant - Better API 

* The next classes are added to RingQt 

	* QQuickView Class
	* QPrintDialog Class
	* QAxWidget2 Class
	* QTextToSpeech Class 
	* QGraphicsView Class
	* QAbstractAspect Class
	* QNode Class
	* QEntity Class
	* QTransform Class
	* QAspectEngine Class
	* QTorusMesh Class
	* QConeMesh Class
	* QCylinderMesh Class
	* QCuboidMesh Class
	* QPlaneMesh Class
	* QSphereMesh Class
	* QPhongMaterial Class
	* QForwardRenderer Class
	* Qt3DWindow Class
	* QAbstractCameraController Class
	* QFirstPersonCameraController Class
	* QTextureMaterial Class
	* QExtrudedTextMesh Class
	* QText2DEntity Class
	* QSkyBoxEntity Class
	* QConeGeometry Class
	* QOrbitCameraController Class
	* QDiffuseSpecularMaterial Class
	* QGoochMaterial Class
	* QMetalroughMaterial Class
	* MorphPhongMaterial Class
	* QPervertexColorMaterial Class
	* QInputAspect Class
	* QFrameAction Class
	* QLogicAspect Class
	* QCamera Class
	* QCameraLens Class
	* QMesh Class
	* QTechnique Class
	* QMaterial Class
	* QEffect Class
	* QRenderPass Class
	* QSceneLoader Class
	* QPointLight Class
	* QRenderAspect Class
	* QTextureLoader Class
	* QObjectPicker Class
	* QCameraSelector Class
	* QCullFace Class
	* QDepthTest Class
	* QViewPort Class

.. index:: 
	pair: What is new in Ring 1.11?; Better Performance

Better Performance
==================

Ring 1.11 is faster than Ring 1.10

The performance gain is between 10% and 30% based on the application.

.. index:: 
	pair: What is new in Ring 1.11?; Better Documentation

Better Documentation
====================

The next chapters are added to the documentation

* Tutorial : Ring Extensions in C/C++
* Using Qt3D 
* Using ZeroLib 
* Using RingRayLib

.. index:: 
	pair: What is new in Ring 1.11?; More Improvements

More Improvements
=================

* New Samples 
	* samples/other/ModuloTimesTableCircle folder
	* samples/other/saveimage folder
	* samples/other/UsingQML folder
	* samples/other/myguicontrol.ring 
	* samples/other/qcalendarwidget.ring
	* samples/other/qcalendarwidget2.ring
	* samples/other/sudoku-KL02.ring
	* samples/other/sudoku-KL02-longproblem.ring
	* samples/other/zerobasedlist.ring
	* ringlibs/gameengine/lesson17.ring (Using Buttons)
	* samples/other/SQLTutorial/SQL-Tutorial.ring
	* samples/other/DrawFourier/AA-Draw-Fourier.ring
	* samples/other/SmartPhoneEmulator/ejemploKey.ring
	* samples/other/DiscreteFourierTransform/DiscreteFourierTransform.ring
	* samples/other/phonedatabase/PhoneDatabase.ring
* Gold Magic 800 - More levels (44 Levels)
* Fifteen Puzzle Game 3D - Better Code (Animation Speed)
* Flappy Bird 3000 - change the Bird direction to be looking down when we have game over
* Ring Notepad - Keyboard shortcuts for the Dockable Windows Mode
* Ring Notepad - When displaying functions list, don't avoid functions that contains the "_" character
* Ring Notepad - Output Window - Send Data - Better Code
* Ring Notepad - Find and Replace Window - Better Code
* Ring Notepad - Edit Menu - Insert Text Window
* Ring Notepad - Edit Menu - Lower Case and Upper Case options
* Ring Notepad - Edit Menu - Capitalize option
* Ring Notepad - Edit Menu - Comment Lines and Comment Block of lines
* Ring Notepad - File Tabs - Context Menu (Close other files, Close Active file and Close All files)
* RingPM - When updating a package - don't reinstall the dependency again
* Ring Game Engine for 2D Games - Added : GE_FULLSCREEN, GE_SCREEN_W and GE_SCREEN_H
* Ring Game Engine for 2D Games - Added : name property to game objects
* Ring Game Engine for 2D Games - Added : find() method to game class (find an object by name)
* Ring Game Engine for 2D Games - Support oGame[:ObjectName] to access an object
* Natural Library - Better Performance
* FoxRing - Added: frCTOD() function
* Code generator for extensions - generate functions that use managed pointers to new structures
* Ring VM - Error codes for Ring Object File errors
* Ring VM - Eval() function - Better Code (Better Performance)
* Ring VM - State Management - Better Code
* Ring VM - the ">" operator and operator overloading - Better Code
* Ring VM - Assignment and calling object methods - Better Code
* Ring VM - OOP - Getter Methods - Better Performance
* Ring API - Supporting RING_API_ISLIST() in C Extensions
* Ring Compiler - Supporting new lines after numbers and literals when writing conditions
* Ring Compiler - Supporting semi-colon (;) in the start of the line
* Ring Compiler - Prevent using Loop and Exit commands from outside loops 
