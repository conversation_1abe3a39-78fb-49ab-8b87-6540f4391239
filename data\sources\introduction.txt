.. index:: 
	single: Introduction; Introduction

============
Introduction
============

.. image:: thering.jpg
	:alt: The Ring Programming Language

Welcome to the Ring programming language!

Ring is an Innovative and practical general-purpose multi-paradigm language
that can be embedded in C/C++ projects, extended using C/C++ code and/or used as standalone language.
The supported programming paradigms are Imperative, Procedural, Object-Oriented, Functional,
Meta programming, Declarative programming using nested structures, and Natural programming.
The language is portable (MS-DOS, Windows, Linux, macOS, Android, WebAssembly, etc.) and can be used to create
Console, GUI, Web, Games and Mobile applications. The language is designed to be Simple, Small and 
Flexible. Also, It's fast enough for many applications. Its Dynamic Language that compile the source
code to byte code then execute it by the Ring Virtual Machine, which is integrated with the Ring
Compiler in one program.

In this chapter we are going to discuss the goals behind the language design and implementation.

.. index:: 
	pair: Introduction; Motivation (1)

Motivation (1)
==============

In Nov. 2011, I started to think about creating a new version of the 
`Programming Without Coding Technology (PWCT) <https://doublesvsoop.sourceforge.net/>`_ software from scratch.

I was interested in creating multi-platform edition of the software beside adding support for
Web & Mobile development.

What I was looking for is a programming language that can be used to build the development 
environment, provides multi-platform support, more productivity, better performance, can be used for components scripting and developing different kinds of applications.

Instead of using a mix of programming languages, I decided to use one programming language. 

I looked at many programming languages, but I discovered that I need 
a different language that comes with new ideas and intelligent implementation.


.. note:: The importance of the PWCT2 project to the Ring language design is similar to the importance of the Unix operating system to the C language design. Having a specific project in the mind of a programming language designer help a lot in taking the right decisions towards a clear goal. For example, We know that the PWCT2 visual programming language will provide readability, this let us focus in Ring design on writability. So, we can get maximum readability and maximum writability at the same time since both languages are designed together. Also, PWCT2 as a visual language requires powerful GUI library and fast graphics. That's why Ring comes with support for the Qt framework as it's primary GUI toolkit.   

.. index:: 
	pair: Introduction; Motivation (2)

Motivation (2)
==============

In 2009-2010, I developed a small domain-specific programming language for GUI development called Supernova.
I invested many months of my time to create this language and get something that I can use for small programs.
After creating Supernova, I realized that if the programming language have some new features on the top of object-oriented programming, it could help me create a domain-specific language like Supernova in days instead of many months. So Ring as a language provide these features, and using Ring we can create Supernova and enjoy its features in real-world applications development. Ring is designed to be a superset of Supernova and enable us to create many domain-specific languages.


.. note:: The role that Supernova language played for the Ring language design is similar to the role played by the ABC language for the Python language design. When we create a programming language and try to use it in practical, we will learn a lot of lessons that help us when designing the next language.

.. index:: 
	pair: Introduction; Motivation (3)

Motivation (3)
==============

We can think of a programming language as a user interface between the user and the machine. The user could be a programmer have deep knowledge about the machine architecture, a developer interested in the application and the requirements of his/her users, or a child wants to discover programming. Also, we have many types of other users like researchers, people with deep knowledge about specific domains and so on.

Providing a programming language that uses just a specific syntax doesn't match what we learned about user interfaces. Ring is a language designed to bring translation, and customization at the level of the language syntax. It's not about providing multiple versions of the software where each version uses a different human language. It's about the ability to change the syntax at the project level, where using one version of Ring, we can switch between different human languages, and different customization of syntax. Instead of developing different programming languages that uses the same VM, Ring uses one compiler that support changing the syntax and this can be done many times in the same project.

.. note:: Choosing the programming language syntax is a huge power and provide a great joy. Using Ring we transfer this power from the programming language designer to the language users.

.. index:: 
	pair: Introduction; Motivation (4)

Motivation (4)
==============

Many programming languages for application development come and go. i.e., Becomes no longer under development!

I remember having a good time while using Clipper, Classic Visual Basic, Visual FoxPro, etc.

Ring is designed to be a language for application development that can stay for many decades in the future.

To achieve this, Ring is a free and open-source project that works on different platforms and has a small implementation that other programmers could understand and improve. It comes with a visual implementation  that could help in this process. Ring is designed and implemented in a way that enables other programmers to easily understand its design and implementation in a short time.

The selection of data types, How the Garbage Collector works, How does the language support threads? All of these decisions are made in a specific way to keep the language very portable and ready for the future (As much as we can, Since we don't know the future, but we can try to invent/predict it!).

.. note:: We can run Ring on a very old operating system like MS-DOS. Also, we can run it using a modern platform like WebAssembly. 


.. index:: 
	pair: Introduction; Motivation (5)

Motivation (5)
==============

I love programming languages, and I have used a lot of them during my work. When I think about a programming language, I feel something. It's not about the language features; it's about the language spirit and the unique things that can be done using this language. I am not talking about the final result, where we can use many languages in the same group to achieve the same goal. I am talking about the solution itself. the design and the code. How does it look like? Is it beautiful? What about the code size? Is it simple or complex? and things like that, which have a direct effect on source code comprehension and software development productivity.

Ring is designed to include the spirit of many other languages. We don't do this by copying features or mixing things that don't match together! Our technique is different. It's similar to how a child could look like his or her parents. You get the feeling, not a copy!

 
.. note:: Ring is designed to be small in size, like Lua, but it comes with standard support for many programming paradigms, like Python and Ruby. This is a challenge when we talk about implementation and development. 


.. index:: 
	pair: Introduction; Ring and other languages

Ring and other languages
========================

Ring is a programming language that comes with better support for Natural Language 
Programming and Declarative Programming.
The innovation comes in supporting these paradigms with new practical techniques on the top of 
Object-Oriented Programming and Functional Programming.

Also, Ring is influenced by the next programming languages

* Lua
* Python
* Ruby
* C
* C#
* BASIC
* QML
* xBase
* Supernova


.. index:: 
	pair: Introduction ; History

History
=======

In Sept. 2013 I started the design and the implementation of the Ring programming language.
After 21 months of development, In May 2015 the language Compiler & Virtual Machine were ready for use!

After that I spent three months testing the language again, trying to discover any bug to fix, writing better tests,
by the end of August 2015, all know bugs were fixed, Writing many tests and testing automation helped a lot in 
getting a stable product.

In September 12, 2015, most of the documentation was written. Before releasing the language I started the marketing by
writing a post in Arabic language about it to my facebook profile page asking for contributors interested in the language 
idea after reading a short description, In the same day I got a lot of emails from developers and friends interested to contribute!

Ring 1.0 is released on January 25, 2016

Ring 1.1 is released on October 6, 2016

Ring 1.2 is released on January 25, 2017

Ring 1.3 is released on May 15, 2017

Ring 1.4 is released on June 29, 2017

Ring 1.5 is released on August 21, 2017

Ring 1.6 is released on November 30, 2017

Ring 1.7 is released on January 25, 2018

Ring 1.8 is released on June 25, 2018

Ring 1.9 is released on October 6, 2018

Ring 1.10 is released on January 25, 2019

Ring 1.11 is released on September 15, 2019

Ring 1.12 is released on January 25, 2020

Ring 1.13 is released on September 15, 2020 

Ring 1.14 is released on January 25, 2021

Ring 1.15 is released on September 24, 2021 

Ring 1.16 is released on October 23, 2021 

Ring 1.17 is released on May 14, 2022

Ring 1.18 is released on July 12, 2023

Ring 1.19 is released on December 31, 2023

Ring 1.20 is released on April 11, 2024

Ring 1.21 is released on September 1, 2024

Ring 1.22 is released on December 1, 2024

.. index:: 
	pair: Introduction; Features

Features
========

The Ring language comes with the next features

.. tip:: The language is ready for production!

* Free Open Source (MIT License)
* Hybrid Implementation (Compiler + Virtual Machine) 
* Declarative programming on the top of Object-Oriented programming
* Natural Language Programming on the top of Object-Oriented programming
* Natural Language Programming Library
* Three different styles for writing the code and you can create your style 
* Syntax Flexibility (You can change the language keywords and operators)
* The language keywords can be translated from English to other languages (Arabic, French, etc)
* Compact Syntax, No explicit end for statements (No ; or ENTER is required)
* Using braces { } we can access objects and use attributes/methods as variables/functions
* Using lists/objects during definition
* Transparent Implementation (See the Tokens, Grammar, and Byte Code for each program) 
* Visual Implementation - Developed using Visual Programming (PWCT) 
* Written in ANSI C (The code is generated + Looks identical to Handwritten Code)
* A small language
	* The Compiler + The Virtual Machine + Standard Functions are less than 25,000 lines of C code
	* The other 500,000 lines of code are related to libraries!
* Portable (MS-DOS, Windows, Linux, macOS, Android, WebAssembly, 32-bit Microcontrollers, etc)
* Comments (One line & Multi-lines)
* Not Case-Sensitive
* Dynamic Typing
* Weakly typed (Automatic conversion between numbers and strings only)
* Lexical Scoping (Global, Local & Object State)
* Default scope for variables inside functions (Local)
* Default scope for variables outside functions (global)
* We can have separate global scope for each library or sub project
* Garbage Collector - Automatic Memory Management (Escape Analysis and Reference Counting)
* In most cases (90%) Ring VM uses Escape Analysis an no need to run the Garbage Collector (Faster)
* Structure Programming
* Rich control structures & Operators
* For in get item by reference not value, you can read/edit the item
* Use exit to go outside from more than one loop (Use it for programming in the small only)
* Procedures/Functions
* Main Function (Optional - To avoid using the Global Scope)
* Call Function before the definition (Top-Down Programming)
* Recursion
* Multi-line literals
* Access (read/write) string letter by index
* The list index start by 1
* No keyword to end Functions/Classes/Packages
* Range operator ex: 1:10 and "a":"z"
* First Class Variables, Lists, Objects and Functions
* Store/Copy Lists/Objects by value (Deep Copy)
* Pass Lists/Objects by reference
* Create references at any time using Ref() function
* Native Object-Oriented Support

	* Encapsulation
	* Setter/Getter (optional)
	* private state (optional)
	* Instantiation
	* Polymorphism 
	* Composition 
	* Inheritance (Single Inheritance)
	* Operator Overloading
	* Packages

* Reflection and Meta-programming
* Clear program structure (Statements then functions then packages & classes)
* Exception Handling
* Eval() to execute code during run-time
* 8-bit clean, work on binary data directly
* I/O commands
* Math functions
* String functions
* List functions
* File processing functions
* Database support (ODBC, SQLite, MySQL & PostgreSQL)
* Security Functions (OpenSSL)
* Internet Functions (LibCurl)
* Zip Functions
* CSV Functions
* JSON Functions
* CGI Library (Written in Ring)

	* HTTP Get
	* HTTP Post
	* File upload
	* Cookies
	* URL Encode
	* HTML Templates
	* HTML Special Characters
	* HTML Generation using Functions
	* HTML Generation using Classes
	* CRUD Example (using MVC)
	* Users Example (Register, Login and Check)

* Deploying web applications in the Cloud
* Extension using C/C++ (Simple API)
* Embedding the language in C/C++ programs
* Embedding Ring in Ring (Support Pause/Resume)
* Comes with code generator (Written in Ring) to quickly wrap C/C++ Libraries
	* Used to Support Allegro by creating RingAllegro
	* Used to Support LibSDL by creating RingLibSDL
	* Used to Support Qt by creating RingQt
* Create 2D Games for Desktop and Mobile (Using the Allegro Library)
* RingLibSDL Extension
* Comes with simple Game Engine for 2D Games
* RingOpenGL Extension
* RingFreeGLUT Extension
* RingRayLib Extension
* RingTilengine Extension
* Create GUI Applications for Desktop, WebAssembly and Mobile (Using the Qt Framework)
* Comes with IDE contains the Code Editor (Ring Notepad) and the Form Designer
* RingREPL (Read-Eval-Print-Loop)
* Tracing and Debugging
* Type Hints Library
* Comes with Ring2EXE to distribute applications
* RingLibuv Extension
* RingLibui Extension
* RingSockets Extension
* RingThreads Extension
* RingHTTPLib Extension
* RingFastPro Extension
* RingRogueUtil Extension
* RingPDFGen
* RingPico - Support Raspberry Pi Pico Microcontroller
* No Global Interpreter (VM) lock (No GIL) - Better for threads and concurrency (Faster)
* Comes with RingPM (Package Manager)
* Many Samples and Applications
* Complete Documentation.

License
=======

The Ring Programming Language

Version 1.22.0

The MIT License (MIT)

Copyright (c) Mahmoud Fayed

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
