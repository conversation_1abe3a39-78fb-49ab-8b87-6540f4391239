load "stdlibcore.ring"

# مستويات التسجيل
LEVEL_DEBUG = 1
LEVEL_INFO = 2
LEVEL_WARNING = 3
LEVEL_ERROR = 4
LEVEL_CRITICAL = 5

# نظام التسجيل
Class Logger {
    # المتغيرات
    logFile
    logLevel
    logFormat
    maxFileSize
    maxBackupCount
    
    func init(filename) {
        logFile = filename
        logLevel = LEVEL_INFO
        logFormat = "[%s] [%s] %s"
        maxFileSize = 10 * 1024 * 1024  # 10 ميجابايت
        maxBackupCount = 5
        
        # إنشاء مجلد السجلات إذا لم يكن موجوداً
        dir = substring(filename, 1, lastIndexOf(filename, "/"))
        if not isDirectory(dir) {
            System("mkdir " + dir)
        }
    }
    
    # تسجيل رسالة تصحيح
    func debug(message) {
        if logLevel <= LEVEL_DEBUG {
            writeLog("DEBUG", message)
        }
    }
    
    # تسجيل رسالة معلومات
    func info(message) {
        if logLevel <= LEVEL_INFO {
            writeLog("INFO", message)
        }
    }
    
    # تسجيل رسالة تحذير
    func warning(message) {
        if logLevel <= LEVEL_WARNING {
            writeLog("WARNING", message)
        }
    }
    
    # تسجيل رسالة خطأ
    func error(message) {
        if logLevel <= LEVEL_ERROR {
            writeLog("ERROR", message)
        }
    }
    
    # تسجيل رسالة حرجة
    func critical(message) {
        if logLevel <= LEVEL_CRITICAL {
            writeLog("CRITICAL", message)
        }
    }
    
    # كتابة رسالة في السجل
    func writeLog(level, message) {
        # التحقق من حجم الملف
        checkFileSize()
        
        # تنسيق الرسالة
        timestamp = date() + " " + time()
        logMessage = sprintf(logFormat, [timestamp, level, message])
        
        # كتابة الرسالة في الملف
        try {
            fp = fopen(logFile, "a+")
            fwrite(fp, logMessage + nl)
            fclose(fp)
        catch
            ? "خطأ في كتابة السجل: " + cCatchError
        }
    }
    
    # التحقق من حجم ملف السجل
    func checkFileSize {
        if isFile(logFile) {
            size = fileSize(logFile)
            if size >= maxFileSize {
                rotateLogFiles()
            }
        }
    }
    
    # تدوير ملفات السجل
    func rotateLogFiles {
        # حذف أقدم نسخة احتياطية
        if isFile(logFile + "." + maxBackupCount) {
            remove(logFile + "." + maxBackupCount)
        }
        
        # تحريك الملفات القديمة
        for i = maxBackupCount-1 to 1 step -1 {
            if isFile(logFile + "." + i) {
                rename(logFile + "." + i, logFile + "." + (i+1))
            }
        }
        
        # نسخ الملف الحالي
        rename(logFile, logFile + ".1")
    }
    
    # تعيين مستوى التسجيل
    func setLevel(level) {
        logLevel = level
    }
    
    # تعيين تنسيق السجل
    func setFormat(format) {
        logFormat = format
    }
    
    # تعيين الحد الأقصى لحجم الملف
    func setMaxFileSize(size) {
        maxFileSize = size
    }
    
    # تعيين عدد النسخ الاحتياطية
    func setMaxBackupCount(count) {
        maxBackupCount = count
    }
    
    # التحقق من وجود مجلد
    func isDirectory(path) {
        try {
            return isDirectory(path)
        catch
            return false
        }
    }
    
    # الحصول على حجم الملف
    func fileSize(path) {
        try {
            fp = fopen(path, "r")
            fseek(fp, 0, 2)  # الانتقال إلى نهاية الملف
            size = ftell(fp)
            fclose(fp)
            return size
        catch
            return 0
        }
    }
}
