load "stdlibcore.ring"

# أداة مراقبة الأداء
Class Profiler {
    # المتغيرات
    startTimes
    endTimes
    memoryUsage
    callCounts
    
    func init {
        startTimes = []
        endTimes = []
        memoryUsage = []
        callCounts = []
    }
    
    # بدء قياس أداء دالة
    func startProfile(funcName) {
        startTimes[funcName] = time()
        if not callCounts[funcName] {
            callCounts[funcName] = 0
        }
        callCounts[funcName] = callCounts[funcName] + 1
        memoryUsage[funcName] = memory()
    }
    
    # إنهاء قياس أداء دالة
    func endProfile(funcName) {
        endTimes[funcName] = time()
        memoryUsage[funcName] = memory() - memoryUsage[funcName]
    }
    
    # الحصول على وقت التنفيذ
    func getExecutionTime(funcName) {
        if startTimes[funcName] and endTimes[funcName] {
            return endTimes[funcName] - startTimes[funcName]
        }
        return 0
    }
    
    # الحصول على عدد مرات الاستدعاء
    func getCallCount(funcName) {
        return callCounts[funcName] ? callCounts[funcName] : 0
    }
    
    # الحصول على استخدام الذاكرة
    func getMemoryUsage(funcName) {
        return memoryUsage[funcName] ? memoryUsage[funcName] : 0
    }
    
    # طباعة تقرير الأداء
    func printReport {
        ? "=== تقرير الأداء ==="
        ? "الدالة | الوقت | عدد الاستدعاءات | استخدام الذاكرة"
        ? "----------------------------------------"
        
        for funcName in startTimes {
            time = getExecutionTime(funcName)
            calls = getCallCount(funcName)
            memory = getMemoryUsage(funcName)
            
            ? sprintf("%-20s | %8.3f | %8d | %8d", [
                funcName,
                time,
                calls,
                memory
            ])
        }
        
        ? "----------------------------------------"
    }
    
    # تصدير التقرير إلى ملف
    func exportReport(filename) {
        file = fopen(filename, "w")
        
        write(file, "تقرير الأداء\n")
        write(file, "==========\n\n")
        
        for funcName in startTimes {
            time = getExecutionTime(funcName)
            calls = getCallCount(funcName)
            memory = getMemoryUsage(funcName)
            
            write(file, sprintf("الدالة: %s\n", funcName))
            write(file, sprintf("وقت التنفيذ: %.3f ثانية\n", time))
            write(file, sprintf("عدد الاستدعاءات: %d\n", calls))
            write(file, sprintf("استخدام الذاكرة: %d بايت\n\n", memory))
        }
        
        fclose(file)
    }
    
    # إعادة تعيين البيانات
    func reset {
        startTimes = []
        endTimes = []
        memoryUsage = []
        callCounts = []
    }
    
    # قياس أداء كتلة من الكود
    func profile(funcName, code) {
        startProfile(funcName)
        eval(code)
        endProfile(funcName)
    }
}
