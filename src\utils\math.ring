# مكتبة الدوال الرياضية المساعدة للترانسفورمر
# تحتوي على الدوال الأساسية المستخدمة في حسابات النموذج
Load "stdlib.ring"

# دالة الجذر التربيعي
# المدخلات:
#   x: العدد المراد حساب جذره التربيعي
# المخرجات:
#   الجذر التربيعي للعدد
func sqrt(x)
    return pow(x, 0.5)
end

# دالة التنعيم (Softmax)
# تحول المصفوفة إلى توزيع احتمالي
# المدخلات:
#   matrix: مصفوفة من الأعداد
# المخرجات:
#   مصفوفة من الاحتمالات (مجموع كل صف = 1)
func softmax(matrix)
    aResult = []
    for row in matrix
        max_val = max(row)
        exp_row = []
        sum = 0
        for val in row
            exp_val = exp(val - max_val)
            add(exp_row, exp_val)
            sum += exp_val
        next
        normalized = []
        for val in exp_row
            add(normalized, val / sum)
        next
        add(aResult, normalized)
    next
    return aResult
end

# دالة التصحيح الخطي (ReLU)
# تحول الأعداد السالبة إلى صفر
# المدخلات:
#   x: عدد أو مصفوفة من الأعداد
# المخرجات:
#   نفس المدخلات مع تحويل كل الأعداد السالبة إلى صفر
func relu(x)
    if type(x) = "NUMBER"
        return max(0, x)
    elseif type(x) = "LIST"
        result = []
        for val in x
            add(result, relu(val))
        next
        return result
    ok
end

# دالة GELU (Gaussian Error Linear Unit)
# دالة تنشيط غير خطية أكثر سلاسة من ReLU
# المدخلات:
#   x: العدد المراد تطبيق الدالة عليه
# المخرجات:
#   قيمة GELU للعدد
func gelu(x)
    # تقريب لدالة GELU
    return x * 0.5 * (1 + tanh(sqrt(2/3.14159265359) * (x + 0.044715 * pow(x, 3))))
end

# دالة التطبيع الطبقي (Layer Normalization)
# تقوم بتطبيع القيم في المصفوفة
# المدخلات:
#   x: المصفوفة المراد تطبيعها
#   gamma: معامل التحجيم (اختياري)
#   beta: معامل الإزاحة (اختياري)
#   eps: قيمة صغيرة لتجنب القسمة على صفر
# المخرجات:
#   المصفوفة بعد التطبيع
func layer_norm(x, gamma , beta, eps)
    mean = 0
    for val in x mean += val next
    mean = mean / len(x)
    
    var = 0
    for val in x
        var += pow(val - mean, 2)
    next
    var = var / len(x)
    
    normalized = []
    for val in x
        norm = (val - mean) / sqrt(var + eps)
        add(normalized, gamma * norm + beta)
    next
    return normalized
end

# دالة توليد أعداد عشوائية بتوزيع طبيعي
# تستخدم تحويل Box-Muller لتوليد الأعداد
# المدخلات:
#   mean: المتوسط (اختياري)
#   std: الانحراف المعياري (اختياري)
# المخرجات:
#   عدد عشوائي يتبع التوزيع الطبيعي
func random_normal(mean, std) 
    # Box-Muller transform
    u1 = random(10)/10
    u2 = random(10)/10
    z0 = sqrt(-2 * log(u1)) * cos(2 * 3.14159265359 * u2)
    return mean + z0 * std
end

