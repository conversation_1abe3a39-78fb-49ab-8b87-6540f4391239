load "stdlibcore.ring"
load "../utils/config.ring"
load "../core/transformer.ring"
load "../utils/data_utils.ring"


# إنشاء وتشغيل المدرب
trainer = new TransformerTrainer
trainer.loadTrainingData()
trainer.train()

# مدرب نموذج الترانسفورمر
Class TransformerTrainer {
    # المتغيرات
    transformer     # نموذج الترانسفورمر
    config         # إعدادات التدريب
    utils          # أدوات مساعدة
    optimizer      # محسن النموذج
    
    # بيانات التدريب
    trainData      # بيانات التدريب
    valData        # بيانات التحقق
    
    # إحصائيات التدريب
    trainLoss      # خسارة التدريب
    valLoss        # خسارة التحقق
    
    func init {
        transformer = new SmartTransformer
        config = new TransformerConfig
        utils = new DataUtils
        optimizer = new AdamOptimizer(config.fLearningRate, 0.9, 0.999, 0.00000001)
        
        trainLoss = []
        valLoss = []
    }
    
    # تحميل بيانات التدريب
    func loadTrainingData {
        utils.debug("تحميل بيانات التدريب...")
        
        # تحميل الترجمات
        translationsPath = "../../data/processed/train/translations.txt"
        translations = loadDataFromFile(translationsPath, "translation")
        
        # تحميل أمثلة الكود
        codePath = "../../data/processed/train/code_examples.txt"
        codeExamples = loadDataFromFile(codePath, "code")
        
        # تقسيم البيانات إلى تدريب وتحقق
        trainData = []
        valData = []
        
        # تقسيم الترجمات (90% تدريب، 10% تحقق)
        splitData(translations, 0.9)
        
        # تقسيم أمثلة الكود
        splitData(codeExamples, 0.9)
        
        utils.debug("تم تحميل " + len(trainData) + " عينة تدريب و " + len(valData) + " عينة تحقق")
    }
    
    # تحميل البيانات من ملف
    func loadDataFromFile(path, type) {
        if !fexists(path) {
            utils.debug("خطأ: الملف غير موجود - " + path)
            return []
        }
        
        data = []
        content = read(path)
        lines = str2list(content)
        
        for line in lines {
            if line != "" {
                parts = split(line, char(9))
                if len(parts) = 2 {
                    data + [parts[1], parts[2], type]
                }
            }
        }
        
        return data
    }
    
    # تقسيم البيانات إلى تدريب وتحقق
    func splitData(data, trainRatio) {
        numSamples = len(data)
        trainSize = floor(numSamples * trainRatio)
        
        # خلط البيانات عشوائياً
        for i = 1 to numSamples {
            j = random(numSamples)
            temp = data[i]
            data[i] = data[j]
            data[j] = temp
        }
        
        # تقسيم البيانات
        for i = 1 to trainSize {
            trainData + data[i]
        }
        
        for i = trainSize + 1 to numSamples {
            valData + data[i]
        }
    }
    
    # تدريب النموذج
    func train {
        utils.debug("بدء التدريب...")
        
        for epoch = 1 to config.nEpochs {
            epochLoss = 0
            
            # تدريب على دفعات
            numBatches = ceil(len(trainData) / config.nBatchSize)
            for batch = 1 to numBatches {
                start = (batch - 1) * config.nBatchSize + 1
                end = min(batch * config.nBatchSize, len(trainData))
                batchData = slice(trainData, start, end)
                
                # حساب الخسارة وتحديث النموذج
                loss = trainBatch(batchData)
                epochLoss += loss
            }
            
            # حساب متوسط الخسارة
            epochLoss = epochLoss / numBatches
            trainLoss + epochLoss
            
            # التحقق من أداء النموذج
            valLoss + validate()
            
            # طباعة تقدم التدريب
            utils.debug("Epoch " + epoch + "/" + config.nEpochs + 
                       " - Loss: " + epochLoss + 
                       " - Val Loss: " + valLoss[len(valLoss)])
            
            # حفظ النموذج
            if epoch % 5 = 0 {
                saveModel("model_epoch_" + epoch + ".bin")
            }
        }
    }
    
    # تدريب دفعة واحدة
    func trainBatch(batchData) {
        loss = 0
        
        for sample in batchData {
            # الحصول على المدخلات والمخرجات المتوقعة
            input = transformer.tokenize(sample[1])
            expected = transformer.tokenize(sample[2])
            
            # التنبؤ والحصول على الخسارة
            output = transformer.forward(input)
            sampleLoss = transformer.calculateLoss(output, expected)
            
            # حساب التدرجات وتحديث النموذج
            gradients = transformer.backward(sampleLoss)
            optimizer.update(transformer, gradients)
            
            loss += sampleLoss
        }
        
        return loss / len(batchData)
    }
    
    # التحقق من أداء النموذج
    func validate {
        totalLoss = 0
        
        for sample in valData {
            input = transformer.tokenize(sample[1])
            expected = transformer.tokenize(sample[2])
            
            output = transformer.forward(input)
            loss = transformer.calculateLoss(output, expected)
            totalLoss += loss
        }
        
        return totalLoss / len(valData)
    }
    
    # حفظ النموذج
    func saveModel(filename) {
        path = config.cModelPath + filename
        transformer.save(path)
        utils.debug("تم حفظ النموذج في: " + path)
    }
}


