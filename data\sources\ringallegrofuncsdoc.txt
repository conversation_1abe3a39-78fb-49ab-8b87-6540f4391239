.. index:: 
     single: RingAllegro Functions Reference; Introduction

===============================
RingAllegro Functions Reference
===============================


Introduction
============

In this chapter we have a list of the supported functions by this extension 

Reference
=========

* void al_exit(void)
* void al_run_main(void)
* int al_init(void)
* ALLEGRO_CONFIG *al_create_config(void)
* void al_destroy_config(ALLEGRO_CONFIG *config)
* ALLEGRO_CONFIG *al_load_config_file(const char *filename)
* ALLEGRO_CONFIG *al_load_config_file_f(ALLEGRO_FILE *file)
* bool al_save_config_file(const char *filename, const ALLEGRO_CONFIG *config)
* bool al_save_config_file_f(ALLEGRO_FILE *file, const ALLEGRO_CONFIG *config)
* void al_add_config_section(ALLEGRO_CONFIG *config, const char *name)
* void al_add_config_comment(ALLEGRO_CONFIG *config, const char *section, const char *comment)
* const char *al_get_config_value(const ALLEGRO_CONFIG *config,const char *section, const char *key)
* void al_set_config_value(ALLEGRO_CONFIG *config,const char *section, const char *key, const char *value)
* char const *al_get_first_config_section(ALLEGRO_CONFIG const *config,ALLEGRO_CONFIG_SECTION **iterator)
* char const *al_get_next_config_section(ALLEGRO_CONFIG_SECTION **iterator)
* char const *al_get_first_config_entry(ALLEGRO_CONFIG const *config,char const *section, ALLEGRO_CONFIG_ENTRY **iterator)
* char const *al_get_next_config_entry(ALLEGRO_CONFIG_ENTRY **iterator)
* ALLEGRO_CONFIG *al_merge_config(const ALLEGRO_CONFIG *cfg1,const ALLEGRO_CONFIG *cfg2)
* void al_merge_config_into(ALLEGRO_CONFIG *master, const ALLEGRO_CONFIG *add)
* ALLEGRO_DISPLAY *al_create_display(int w, int h)
* void al_destroy_display(ALLEGRO_DISPLAY *display)
* int al_get_new_display_flags(void)
* void al_set_new_display_flags(int flags)
* int al_get_new_display_option(int option, int *importance)
* void al_set_new_display_option(int option, int value, int importance)
* void al_reset_new_display_options(void)
* void al_get_new_window_position(int *x, int *y)
* void al_set_new_window_position(int x, int y)
* int al_get_new_display_refresh_rate(void)
* void al_set_new_display_refresh_rate(int refresh_rate)
* ALLEGRO_EVENT_SOURCE *al_get_display_event_source(ALLEGRO_DISPLAY *display)
* ALLEGRO_BITMAP *al_get_backbuffer(ALLEGRO_DISPLAY *display)
* void al_flip_display(void)
* void al_update_display_region(int x, int y, int width, int height)
* bool al_wait_for_vsync(void)
* int al_get_display_width(ALLEGRO_DISPLAY *display)
* int al_get_display_height(ALLEGRO_DISPLAY *display)
* bool al_resize_display(ALLEGRO_DISPLAY *display, int width, int height)
* bool al_acknowledge_resize(ALLEGRO_DISPLAY *display)
* void al_get_window_position(ALLEGRO_DISPLAY *display, int *x, int *y)
* void al_set_window_position(ALLEGRO_DISPLAY *display, int x, int y)
* int al_get_display_flags(ALLEGRO_DISPLAY *display)
* bool al_set_display_flag(ALLEGRO_DISPLAY *display, int flag, bool onoff)
* int al_get_display_option(ALLEGRO_DISPLAY *display, int option)
* int al_get_display_format(ALLEGRO_DISPLAY *display)
* int al_get_display_refresh_rate(ALLEGRO_DISPLAY *display)
* void al_set_window_title(ALLEGRO_DISPLAY *display, const char *title)
* void al_set_display_icon(ALLEGRO_DISPLAY *display, ALLEGRO_BITMAP *icon)
* void al_set_display_icons(ALLEGRO_DISPLAY *display,int num_icons, ALLEGRO_BITMAP *icons[])
* bool al_inhibit_screensaver(bool inhibit)
* void al_acknowledge_drawing_halt(ALLEGRO_DISPLAY *display)
* void al_acknowledge_drawing_resume(ALLEGRO_DISPLAY *display)
* int al_get_display_orientation(ALLEGRO_DISPLAY* display)
* void al_set_display_option(ALLEGRO_DISPLAY *display, int option, int value)
* bool al_get_window_constraints(ALLEGRO_DISPLAY *display,int *min_w, int *min_h, int *max_w, int *max_h)
* bool al_set_window_constraints(ALLEGRO_DISPLAY *display,int min_w, int min_h, int max_w, int max_h)
* ALLEGRO_EVENT_QUEUE *al_create_event_queue(void)
* void al_destroy_event_queue(ALLEGRO_EVENT_QUEUE *queue)
* void al_register_event_source(ALLEGRO_EVENT_QUEUE *queue,ALLEGRO_EVENT_SOURCE *source)
* void al_unregister_event_source(ALLEGRO_EVENT_QUEUE *queue,ALLEGRO_EVENT_SOURCE *source)
* bool al_is_event_queue_empty(ALLEGRO_EVENT_QUEUE *queue)
* bool al_get_next_event(ALLEGRO_EVENT_QUEUE *queue, ALLEGRO_EVENT *ret_event)
* bool al_peek_next_event(ALLEGRO_EVENT_QUEUE *queue, ALLEGRO_EVENT *ret_event)
* bool al_drop_next_event(ALLEGRO_EVENT_QUEUE *queue)
* void al_flush_event_queue(ALLEGRO_EVENT_QUEUE *queue)
* void al_wait_for_event(ALLEGRO_EVENT_QUEUE *queue, ALLEGRO_EVENT *ret_event)
* bool al_wait_for_event_timed(ALLEGRO_EVENT_QUEUE *queue,ALLEGRO_EVENT *ret_event, float secs)
* bool al_wait_for_event_until(ALLEGRO_EVENT_QUEUE *queue,ALLEGRO_EVENT *ret_event, ALLEGRO_TIMEOUT *timeout)
* void al_init_user_event_source(ALLEGRO_EVENT_SOURCE *src)
* void al_destroy_user_event_source(ALLEGRO_EVENT_SOURCE *src)
* intptr_t al_get_event_source_data(const ALLEGRO_EVENT_SOURCE *source)
* void al_set_event_source_data(ALLEGRO_EVENT_SOURCE *source, intptr_t data)
* ALLEGRO_FILE *al_fopen(const char *path, const char *mode)
* ALLEGRO_FILE *al_fopen_interface(const ALLEGRO_FILE_INTERFACE *drv,const char *path, const char *mode)
* ALLEGRO_FILE *al_fopen_slice(ALLEGRO_FILE *fp, size_t initial_size, const char *mode)
* void al_fclose(ALLEGRO_FILE *f)
* size_t al_fread(ALLEGRO_FILE *f, void *ptr, size_t size)
* size_t al_fwrite(ALLEGRO_FILE *f, const void *ptr, size_t size)
* bool al_fflush(ALLEGRO_FILE *f)
* int64_t al_ftell(ALLEGRO_FILE *f)
* bool al_fseek(ALLEGRO_FILE *f, int64_t offset, int whence)
* bool al_feof(ALLEGRO_FILE *f)
* bool al_ferror(ALLEGRO_FILE *f)
* void al_fclearerr(ALLEGRO_FILE *f)
* int al_fungetc(ALLEGRO_FILE *f, int c)
* int64_t al_fsize(ALLEGRO_FILE *f)
* int al_fgetc(ALLEGRO_FILE *f)
* int al_fputc(ALLEGRO_FILE *f, int c)
* int16_t al_fread16le(ALLEGRO_FILE *f)
* int16_t al_fread16be(ALLEGRO_FILE *f)
* size_t al_fwrite16le(ALLEGRO_FILE *f, int16_t w)
* size_t al_fwrite16be(ALLEGRO_FILE *f, int16_t w)
* int32_t al_fread32le(ALLEGRO_FILE *f)
* int32_t al_fread32be(ALLEGRO_FILE *f)
* size_t al_fwrite32le(ALLEGRO_FILE *f, int32_t l)
* size_t al_fwrite32be(ALLEGRO_FILE *f, int32_t l)
* char *al_fgets(ALLEGRO_FILE *f, char * const buf, size_t max)
* ALLEGRO_USTR *al_fget_ustr(ALLEGRO_FILE *f)
* int al_fputs(ALLEGRO_FILE *f, char const *p)
* ALLEGRO_FILE *al_fopen_fd(int fd, const char *mode)
* ALLEGRO_FILE *al_make_temp_file(const char *template, ALLEGRO_PATH **ret_path)
* void al_set_new_file_interface(const ALLEGRO_FILE_INTERFACE *file_interface
* void al_set_standard_file_interface(void)
* const ALLEGRO_FILE_INTERFACE *al_get_new_file_interface(void)
* ALLEGRO_FILE *al_create_file_handle(const ALLEGRO_FILE_INTERFACE *drv,void *userdata)
* void *al_get_file_userdata(ALLEGRO_FILE *f)
* ALLEGRO_FS_ENTRY *al_create_fs_entry(const char *path)
* void al_destroy_fs_entry(ALLEGRO_FS_ENTRY *fh)
* const char *al_get_fs_entry_name(ALLEGRO_FS_ENTRY *e)
* bool al_update_fs_entry(ALLEGRO_FS_ENTRY *e)
* uint32_t al_get_fs_entry_mode(ALLEGRO_FS_ENTRY *e)
* time_t al_get_fs_entry_atime(ALLEGRO_FS_ENTRY *e)
* time_t al_get_fs_entry_ctime(ALLEGRO_FS_ENTRY *e)
* time_t al_get_fs_entry_mtime(ALLEGRO_FS_ENTRY *e)
* off_t al_get_fs_entry_size(ALLEGRO_FS_ENTRY *e)
* bool al_fs_entry_exists(ALLEGRO_FS_ENTRY *e)
* bool al_remove_fs_entry(ALLEGRO_FS_ENTRY *e)
* bool al_filename_exists(const char *path)
* bool al_remove_filename(const char *path)
* bool al_open_directory(ALLEGRO_FS_ENTRY *e)
* ALLEGRO_FS_ENTRY *al_read_directory(ALLEGRO_FS_ENTRY *e)
* bool al_close_directory(ALLEGRO_FS_ENTRY *e)
* char *al_get_current_directory(void)
* bool al_change_directory(const char *path)
* bool al_make_directory(const char *path)
* ALLEGRO_FILE *al_open_fs_entry(ALLEGRO_FS_ENTRY *e, const char *mode)
* void al_set_fs_interface(const ALLEGRO_FS_INTERFACE *fs_interface)
* void al_set_standard_fs_interface(void)
* const ALLEGRO_FS_INTERFACE *al_get_fs_interface(void)
* al_fixed al_itofix(int x);
* int al_fixtoi(al_fixed x);
* int al_fixfloor(al_fixed x);
* int al_fixceil(al_fixed x);
* al_fixed al_ftofix(double x);
* double al_fixtof(al_fixed x);
* al_fixed al_fixmul(al_fixed x, al_fixed y);
* al_fixed al_fixdiv(al_fixed x, al_fixed y);
* al_fixed al_fixadd(al_fixed x, al_fixed y);
* al_fixed al_fixsub(al_fixed x, al_fixed y);
* al_fixed al_fixsin(al_fixed x);
* al_fixed al_fixcos(al_fixed x);
* al_fixed al_fixtan(al_fixed x);
* al_fixed al_fixasin(al_fixed x);
* al_fixed al_fixacos(al_fixed x);
* al_fixed al_fixatan(al_fixed x)
* al_fixed al_fixatan2(al_fixed y, al_fixed x)
* al_fixed al_fixsqrt(al_fixed x)
* al_fixed al_fixhypot(al_fixed x, al_fixed y)
* ALLEGRO_DISPLAY_MODE *al_get_display_mode(int index, ALLEGRO_DISPLAY_MODE *mode)
* int al_get_num_display_modes(void)
* ALLEGRO_COLOR al_map_rgb(unsigned char r, unsigned char g, unsigned char b)
* ALLEGRO_COLOR al_map_rgb_f(float r, float g, float b)
* ALLEGRO_COLOR al_map_rgba(unsigned char r, unsigned char g, unsigned char b, unsigned char a)
* ALLEGRO_COLOR al_map_rgba_f(float r, float g, float b, float a)
* void al_unmap_rgb(ALLEGRO_COLOR color,unsigned char *r, unsigned char *g, unsigned char *b)
* void al_unmap_rgb_f(ALLEGRO_COLOR color, float *r, float *g, float *b)
* void al_unmap_rgba(ALLEGRO_COLOR color,unsigned char *r, unsigned char *g, unsigned char *b, unsigned char *a)
* void al_unmap_rgba_f(ALLEGRO_COLOR color,float *r, float *g, float *b, float *a)
* int al_get_pixel_size(int format)
* int al_get_pixel_format_bits(int format)
* ALLEGRO_LOCKED_REGION *al_lock_bitmap(ALLEGRO_BITMAP *bitmap,int format, int flags)
* ALLEGRO_LOCKED_REGION *al_lock_bitmap_region(ALLEGRO_BITMAP *bitmap,int x, int y, int width, int height, int format, int flags)
* void al_unlock_bitmap(ALLEGRO_BITMAP *bitmap)
* ALLEGRO_BITMAP *al_create_bitmap(int w, int h)
* ALLEGRO_BITMAP *al_create_sub_bitmap(ALLEGRO_BITMAP *parent,int x, int y, int w, int h)
* ALLEGRO_BITMAP *al_clone_bitmap(ALLEGRO_BITMAP *bitmap)
* void al_destroy_bitmap(ALLEGRO_BITMAP *bitmap)
* int al_get_new_bitmap_flags(void)
* int al_get_new_bitmap_format(void)
* void al_set_new_bitmap_flags(int flags)
* void al_add_new_bitmap_flag(int flag)
* void al_set_new_bitmap_format(int format)
* int al_get_bitmap_flags(ALLEGRO_BITMAP *bitmap)
* int al_get_bitmap_format(ALLEGRO_BITMAP *bitmap)
* int al_get_bitmap_height(ALLEGRO_BITMAP *bitmap)
* int al_get_bitmap_width(ALLEGRO_BITMAP *bitmap)
* ALLEGRO_COLOR al_get_pixel(ALLEGRO_BITMAP *bitmap, int x, int y)
* bool al_is_bitmap_locked(ALLEGRO_BITMAP *bitmap)
* bool al_is_compatible_bitmap(ALLEGRO_BITMAP *bitmap)
* bool al_is_sub_bitmap(ALLEGRO_BITMAP *bitmap)
* ALLEGRO_BITMAP *al_get_parent_bitmap(ALLEGRO_BITMAP *bitmap)
* void al_clear_to_color(ALLEGRO_COLOR color)
* void al_draw_bitmap(ALLEGRO_BITMAP *bitmap, float dx, float dy, int flags)
* void al_draw_tinted_bitmap(ALLEGRO_BITMAP *bitmap, ALLEGRO_COLOR tint,float dx, float dy, int flags)
* void al_draw_bitmap_region(ALLEGRO_BITMAP *bitmap,float sx, float sy, float sw, float sh, float dx, float dy, int flags)
* void al_draw_tinted_bitmap_region(ALLEGRO_BITMAP *bitmap,ALLEGRO_COLOR tint,float sx, float sy, float sw, float sh, float dx, float dy,int flags)
* void al_draw_pixel(float x, float y, ALLEGRO_COLOR color)
* void al_draw_rotated_bitmap(ALLEGRO_BITMAP *bitmap,float cx, float cy, float dx, float dy, float angle, int flags)
* void al_draw_tinted_rotated_bitmap(ALLEGRO_BITMAP *bitmap,ALLEGRO_COLOR tint,float cx, float cy, float dx, float dy, float angle, int flags)
* void al_draw_scaled_rotated_bitmap(ALLEGRO_BITMAP *bitmap,float cx, float cy, float dx, float dy, float xscale, float yscale,float angle, int flags)
* void al_draw_tinted_scaled_rotated_bitmap(ALLEGRO_BITMAP *bitmap,ALLEGRO_COLOR tint,float cx, float cy, float dx, float dy, float xscale, float yscale,float angle, int flags)
* void al_draw_tinted_scaled_rotated_bitmap_region(ALLEGRO_BITMAP *bitmap,float sx, float sy, float sw, float sh,ALLEGRO_COLOR tint,float cx, float cy, float dx, float dy, float xscale, float yscale,float angle, int flags)
* void al_draw_scaled_bitmap(ALLEGRO_BITMAP *bitmap,float sx, float sy, float sw, float sh,float dx, float dy, float dw, float dh, int flags)
* void al_draw_tinted_scaled_bitmap(ALLEGRO_BITMAP *bitmap,ALLEGRO_COLOR tint,float sx, float sy, float sw, float sh,float dx, float dy, float dw, float dh, int flags)
* ALLEGRO_BITMAP *al_get_target_bitmap(void)
* void al_put_pixel(int x, int y, ALLEGRO_COLOR color)
* void al_put_blended_pixel(int x, int y, ALLEGRO_COLOR color)
* void al_set_target_bitmap(ALLEGRO_BITMAP *bitmap)
* void al_set_target_backbuffer(ALLEGRO_DISPLAY *display)
* ALLEGRO_DISPLAY *al_get_current_display(void)
* void al_get_blender(int *op, int *src, int *dst)
* void al_get_separate_blender(int *op, int *src, int *dst,int *alpha_op, int *alpha_src, int *alpha_dst)
* void al_set_blender(int op, int src, int dst)
* void al_set_separate_blender(int op, int src, int dst,int alpha_op, int alpha_src, int alpha_dst)
* void al_get_clipping_rectangle(int *x, int *y, int *w, int *h)
* void al_set_clipping_rectangle(int x, int y, int width, int height)
* void al_reset_clipping_rectangle(void)
* void al_convert_mask_to_alpha(ALLEGRO_BITMAP *bitmap, ALLEGRO_COLOR mask_color)
* void al_hold_bitmap_drawing(bool hold)
* bool al_is_bitmap_drawing_held(void)
* ALLEGRO_BITMAP *al_load_bitmap_f(ALLEGRO_FILE *fp, const char *ident)
* bool al_save_bitmap(const char *filename, ALLEGRO_BITMAP *bitmap)
* bool al_save_bitmap_f(ALLEGRO_FILE *fp, const char *ident,ALLEGRO_BITMAP *bitmap)
* bool al_install_joystick(void)
* void al_uninstall_joystick(void)
* bool al_is_joystick_installed(void)
* bool al_reconfigure_joysticks(void)
* int al_get_num_joysticks(void)
* ALLEGRO_JOYSTICK * al_get_joystick(int num)
* void al_release_joystick(ALLEGRO_JOYSTICK *joy)
* bool al_get_joystick_active(ALLEGRO_JOYSTICK *joy)
* const char *al_get_joystick_name(ALLEGRO_JOYSTICK *joy)
* const char *al_get_joystick_stick_name(ALLEGRO_JOYSTICK *joy, int stick)
* const char *al_get_joystick_axis_name(ALLEGRO_JOYSTICK *joy, int stick, int axis)
* const char *al_get_joystick_button_name(ALLEGRO_JOYSTICK *joy, int button)
* int al_get_joystick_stick_flags(ALLEGRO_JOYSTICK *joy, int stick)
* int al_get_joystick_num_sticks(ALLEGRO_JOYSTICK *joy)
* int al_get_joystick_num_axes(ALLEGRO_JOYSTICK *joy, int stick)
* int al_get_joystick_num_buttons(ALLEGRO_JOYSTICK *joy)
* void al_get_joystick_state(ALLEGRO_JOYSTICK *joy, ALLEGRO_JOYSTICK_STATE *ret_state)
* ALLEGRO_EVENT_SOURCE *al_get_joystick_event_source(void)
* bool al_install_keyboard(void)
* bool al_is_keyboard_installed(void)
* void al_uninstall_keyboard(void)
* void al_get_keyboard_state(ALLEGRO_KEYBOARD_STATE *ret_state)
* bool al_key_down(const ALLEGRO_KEYBOARD_STATE *state, int keycode)
* const char *al_keycode_to_name(int keycode)
* bool al_set_keyboard_leds(int leds)
* ALLEGRO_EVENT_SOURCE *al_get_keyboard_event_source(void)
* void *al_malloc_with_context(size_t n,int line, const char *file, const char *func)
* void al_free_with_context(void *ptr, int line, const char *file, const char *func)
* void *al_realloc_with_context(void *ptr, size_t n,int line, const char *file, const char *func)
* void *al_calloc_with_context(size_t count, size_t n,int line, const char *file, const char *func)
* void al_set_memory_interface(ALLEGRO_MEMORY_INTERFACE *memory_interface)
* int al_get_new_display_adapter(void)
* void al_set_new_display_adapter(int adapter)
* bool al_get_monitor_info(int adapter, ALLEGRO_MONITOR_INFO *info)
* int al_get_num_video_adapters(void)
* bool al_install_mouse(void)
* bool al_is_mouse_installed(void)
* void al_uninstall_mouse(void)
* unsigned int al_get_mouse_num_axes(void)
* unsigned int al_get_mouse_num_buttons(void)
* void al_get_mouse_state(ALLEGRO_MOUSE_STATE *ret_state)
* int al_get_mouse_state_axis(const ALLEGRO_MOUSE_STATE *state, int axis)
* bool al_mouse_button_down(const ALLEGRO_MOUSE_STATE *state, int button)
* bool al_set_mouse_xy(ALLEGRO_DISPLAY *display, int x, int y)
* bool al_set_mouse_z(int z)
* bool al_set_mouse_w(int w)
* bool al_set_mouse_axis(int which, int value)
* ALLEGRO_EVENT_SOURCE *al_get_mouse_event_source(void)
* ALLEGRO_MOUSE_CURSOR *al_create_mouse_cursor(ALLEGRO_BITMAP *bmp,int x_focus, int y_focus)
* void al_destroy_mouse_cursor(ALLEGRO_MOUSE_CURSOR *cursor)
* bool al_set_mouse_cursor(ALLEGRO_DISPLAY *display, ALLEGRO_MOUSE_CURSOR *cursor)
* bool al_set_system_mouse_cursor(ALLEGRO_DISPLAY *display,ALLEGRO_SYSTEM_MOUSE_CURSOR cursor_id)
* bool al_get_mouse_cursor_position(int *ret_x, int *ret_y)
* bool al_hide_mouse_cursor(ALLEGRO_DISPLAY *display)
* bool al_show_mouse_cursor(ALLEGRO_DISPLAY *display)
* bool al_grab_mouse(ALLEGRO_DISPLAY *display)
* bool al_ungrab_mouse(void)
* ALLEGRO_PATH *al_create_path(const char *str)
* ALLEGRO_PATH *al_create_path_for_directory(const char *str)
* void al_destroy_path(ALLEGRO_PATH *path)
* ALLEGRO_PATH *al_clone_path(const ALLEGRO_PATH *path)
* bool al_join_paths(ALLEGRO_PATH *path, const ALLEGRO_PATH *tail)
* bool al_rebase_path(const ALLEGRO_PATH *head, ALLEGRO_PATH *tail)
* const char *al_get_path_drive(const ALLEGRO_PATH *path)
* int al_get_path_num_components(const ALLEGRO_PATH *path)
* const char *al_get_path_component(const ALLEGRO_PATH *path, int i)
* const char *al_get_path_tail(const ALLEGRO_PATH *path)
* const char *al_get_path_filename(const ALLEGRO_PATH *path)
* const char *al_get_path_basename(const ALLEGRO_PATH *path)
* const char *al_get_path_extension(const ALLEGRO_PATH *path)
* void al_set_path_drive(ALLEGRO_PATH *path, const char *drive)
* void al_append_path_component(ALLEGRO_PATH *path, const char *s)
* void al_insert_path_component(ALLEGRO_PATH *path, int i, const char *s)
* void al_replace_path_component(ALLEGRO_PATH *path, int i, const char *s)
* void al_remove_path_component(ALLEGRO_PATH *path, int i)
* void al_drop_path_tail(ALLEGRO_PATH *path)
* void al_set_path_filename(ALLEGRO_PATH *path, const char *filename)
* bool al_set_path_extension(ALLEGRO_PATH *path, char const *extension)
* const char *al_path_cstr(const ALLEGRO_PATH *path, char delim)
* bool al_make_path_canonical(ALLEGRO_PATH *path)
* void al_restore_state(ALLEGRO_STATE const *state)
* void al_store_state(ALLEGRO_STATE *state, int flags
* int al_get_errno(void)
* void al_set_errno(int errnum)
* void al_uninstall_system(void)
* bool al_is_system_installed(void)
* uint32_t al_get_allegro_version(void)
* ALLEGRO_PATH *al_get_standard_path(int id)
* void al_set_exe_name(char const *path)
* void al_set_app_name(const char *app_name)
* void al_set_org_name(const char *org_name)
* const char *al_get_app_name(void)
* const char *al_get_org_name(void)
* ALLEGRO_CONFIG *al_get_system_config(void)
* ALLEGRO_THREAD *al_create_thread(void)
* void al_run_detached_thread(void)
* void al_start_thread(ALLEGRO_THREAD *thread)
* void al_join_thread(ALLEGRO_THREAD *thread, void **ret_value)
* void al_set_thread_should_stop(ALLEGRO_THREAD *thread)
* bool al_get_thread_should_stop(ALLEGRO_THREAD *thread)
* void al_destroy_thread(ALLEGRO_THREAD *thread)
* ALLEGRO_MUTEX *al_create_mutex(void)
* ALLEGRO_MUTEX *al_create_mutex_recursive(void)
* void al_lock_mutex(ALLEGRO_MUTEX *mutex)
* void al_unlock_mutex(ALLEGRO_MUTEX *mutex)
* void al_destroy_mutex(ALLEGRO_MUTEX *mutex)
* ALLEGRO_COND *al_create_cond(void)
* void al_destroy_cond(ALLEGRO_COND *cond)
* void al_wait_cond(ALLEGRO_COND *cond, ALLEGRO_MUTEX *mutex)
* int al_wait_cond_until(ALLEGRO_COND *cond, ALLEGRO_MUTEX *mutex,const ALLEGRO_TIMEOUT *timeout)
* void al_broadcast_cond(ALLEGRO_COND *cond)
* void al_signal_cond(ALLEGRO_COND *cond)
* double al_get_time(void)
* void al_init_timeout(ALLEGRO_TIMEOUT *timeout, double seconds)
* void al_rest(double seconds)
* ALLEGRO_TIMER *al_create_timer(double speed_secs)
* void al_start_timer(ALLEGRO_TIMER *timer)
* void al_stop_timer(ALLEGRO_TIMER *timer)
* bool al_get_timer_started(const ALLEGRO_TIMER *timer)
* void al_destroy_timer(ALLEGRO_TIMER *timer)
* int64_t al_get_timer_count(const ALLEGRO_TIMER *timer)
* void al_set_timer_count(ALLEGRO_TIMER *timer, int64_t new_count)
* void al_add_timer_count(ALLEGRO_TIMER *timer, int64_t diff)
* double al_get_timer_speed(const ALLEGRO_TIMER *timer)
* void al_set_timer_speed(ALLEGRO_TIMER *timer, double new_speed_secs)
* ALLEGRO_EVENT_SOURCE *al_get_timer_event_source(ALLEGRO_TIMER *timer)
* void al_copy_transform(ALLEGRO_TRANSFORM *dest, const ALLEGRO_TRANSFORM *src)
* void al_use_transform(const ALLEGRO_TRANSFORM *trans)
* const ALLEGRO_TRANSFORM *al_get_current_transform(void)
* void al_invert_transform(ALLEGRO_TRANSFORM *trans)
* int al_check_inverse(const ALLEGRO_TRANSFORM *trans, float tol)
* void al_identity_transform(ALLEGRO_TRANSFORM *trans)
* void al_build_transform(ALLEGRO_TRANSFORM *trans, float x, float y,float sx, float sy, float theta)
* void al_translate_transform(ALLEGRO_TRANSFORM *trans, float x, float y)
* void al_rotate_transform(ALLEGRO_TRANSFORM *trans, float theta)
* void al_scale_transform(ALLEGRO_TRANSFORM *trans, float sx, float sy)
* void al_transform_coordinates(const ALLEGRO_TRANSFORM *trans, float *x, float *y)
* void al_compose_transform(ALLEGRO_TRANSFORM *trans, const ALLEGRO_TRANSFORM *other)
* ALLEGRO_USTR *al_ustr_new(const char *s)
* ALLEGRO_USTR *al_ustr_new_from_buffer(const char *s, size_t size)
* void al_ustr_free(ALLEGRO_USTR *us)
* const char *al_cstr(const ALLEGRO_USTR *us)
* void al_ustr_to_buffer(const ALLEGRO_USTR *us, char *buffer, int size)
* char *al_cstr_dup(const ALLEGRO_USTR *us)
* ALLEGRO_USTR *al_ustr_dup(const ALLEGRO_USTR *us)
* ALLEGRO_USTR *al_ustr_dup_substr(const ALLEGRO_USTR *us, int start_pos,int end_pos)
* const ALLEGRO_USTR *al_ustr_empty_string(void)
* const ALLEGRO_USTR *al_ref_cstr(ALLEGRO_USTR_INFO *info, const char *s)
* const ALLEGRO_USTR *al_ref_buffer(ALLEGRO_USTR_INFO *info, const char *s, size_t size)
* const ALLEGRO_USTR *al_ref_ustr(ALLEGRO_USTR_INFO *info, const ALLEGRO_USTR *us,int start_pos, int end_pos)
* size_t al_ustr_size(const ALLEGRO_USTR *us)
* size_t al_ustr_length(const ALLEGRO_USTR *us)
* int al_ustr_offset(const ALLEGRO_USTR *us, int index)
* bool al_ustr_next(const ALLEGRO_USTR *us, int *pos)
* bool al_ustr_prev(const ALLEGRO_USTR *us, int *pos)
* int32_t al_ustr_get(const ALLEGRO_USTR *ub, int pos)
* int32_t al_ustr_get_next(const ALLEGRO_USTR *us, int *pos)
* int32_t al_ustr_prev_get(const ALLEGRO_USTR *us, int *pos)
* bool al_ustr_insert(ALLEGRO_USTR *us1, int pos, const ALLEGRO_USTR *us2)
* bool al_ustr_insert_cstr(ALLEGRO_USTR *us, int pos, const char *s)
* size_t al_ustr_insert_chr(ALLEGRO_USTR *us, int pos, int32_t c)
* bool al_ustr_append(ALLEGRO_USTR *us1, const ALLEGRO_USTR *us2)
* bool al_ustr_append_cstr(ALLEGRO_USTR *us, const char *s)
* size_t al_ustr_append_chr(ALLEGRO_USTR *us, int32_t c)
* bool al_ustr_remove_chr(ALLEGRO_USTR *us, int pos)
* bool al_ustr_remove_range(ALLEGRO_USTR *us, int start_pos, int end_pos)
* bool al_ustr_truncate(ALLEGRO_USTR *us, int start_pos)
* bool al_ustr_ltrim_ws(ALLEGRO_USTR *us)
* bool al_ustr_rtrim_ws(ALLEGRO_USTR *us)
* bool al_ustr_trim_ws(ALLEGRO_USTR *us)
* bool al_ustr_assign(ALLEGRO_USTR *us1, const ALLEGRO_USTR *us2)
* bool al_ustr_assign_substr(ALLEGRO_USTR *us1, const ALLEGRO_USTR *us2,int start_pos, int end_pos)
* bool al_ustr_assign_cstr(ALLEGRO_USTR *us1, const char *s)
* size_t al_ustr_set_chr(ALLEGRO_USTR *us, int start_pos, int32_t c)
* bool al_ustr_replace_range(ALLEGRO_USTR *us1, int start_pos1, int end_pos1,const ALLEGRO_USTR *us2)
* int al_ustr_find_chr(const ALLEGRO_USTR *us, int start_pos, int32_t c)
* int al_ustr_rfind_chr(const ALLEGRO_USTR *us, int end_pos, int32_t c)
* int al_ustr_find_set(const ALLEGRO_USTR *us, int start_pos,const ALLEGRO_USTR *accept)
* int al_ustr_find_set_cstr(const ALLEGRO_USTR *us, int start_pos,const char *accept)
* int al_ustr_find_cset(const ALLEGRO_USTR *us, int start_pos,const ALLEGRO_USTR *reject)
* int al_ustr_find_cset_cstr(const ALLEGRO_USTR *us, int start_pos,const char *reject)
* int al_ustr_find_str(const ALLEGRO_USTR *haystack, int start_pos,const ALLEGRO_USTR *needle)
* int al_ustr_find_cstr(const ALLEGRO_USTR *haystack, int start_pos,const char *needle)
* int al_ustr_rfind_str(const ALLEGRO_USTR *haystack, int end_pos,const ALLEGRO_USTR *needle)
* int al_ustr_rfind_cstr(const ALLEGRO_USTR *haystack, int end_pos,const char *needle)
* bool al_ustr_find_replace(ALLEGRO_USTR *us, int start_pos,const ALLEGRO_USTR *find, const ALLEGRO_USTR *replace)
* bool al_ustr_find_replace_cstr(ALLEGRO_USTR *us, int start_pos,const char *find, const char *replace)
* int al_ustr_compare(const ALLEGRO_USTR *us1, const ALLEGRO_USTR *us2)
* int al_ustr_ncompare(const ALLEGRO_USTR *us1, const ALLEGRO_USTR *us2, int n)
* bool al_ustr_equal(const ALLEGRO_USTR *us1, const ALLEGRO_USTR *us2)
* bool al_ustr_has_prefix(const ALLEGRO_USTR *us1, const ALLEGRO_USTR *us2)
* bool al_ustr_has_prefix_cstr(const ALLEGRO_USTR *us1, const char *s2)
* bool al_ustr_has_suffix(const ALLEGRO_USTR *us1, const ALLEGRO_USTR *us2)
* bool al_ustr_has_suffix_cstr(const ALLEGRO_USTR *us1, const char *s2)
* ALLEGRO_USTR *al_ustr_new_from_utf16(uint16_t const *s)
* size_t al_ustr_size_utf16(const ALLEGRO_USTR *us)
* size_t al_ustr_encode_utf16(const ALLEGRO_USTR *us, uint16_t *s,size_t n)
* size_t al_utf8_width(int c)
* size_t al_utf8_encode(char s[], int32_t c)
* size_t al_utf16_width(int c)
* LPDIRECT3DDEVICE9 al_get_d3d_device(ALLEGRO_DISPLAY *display)
* LPDIRECT3DTEXTURE9 al_get_d3d_system_texture(ALLEGRO_BITMAP *bitmap)
* LPDIRECT3DTEXTURE9 al_get_d3d_video_texture(ALLEGRO_BITMAP *bitmap)
* bool al_have_d3d_non_pow2_texture_support(void)
* bool al_have_d3d_non_square_texture_support(void)
* void al_get_d3d_texture_position(ALLEGRO_BITMAP *bitmap, int *u, int *v)
* bool al_is_d3d_device_lost(ALLEGRO_DISPLAY *display)
* ALLEGRO_OGL_EXT_LIST *al_get_opengl_extension_list(void)
* void *al_get_opengl_proc_address(const char *name)
* GLuint al_get_opengl_texture(ALLEGRO_BITMAP *bitmap)
* void al_get_opengl_texture_size(ALLEGRO_BITMAP *bitmap, int *w, int *h)
* void al_get_opengl_texture_position(ALLEGRO_BITMAP *bitmap, int *u, int *v)
* GLuint al_get_opengl_fbo(ALLEGRO_BITMAP *bitmap)
* void al_remove_opengl_fbo(ALLEGRO_BITMAP *bitmap)
* bool al_have_opengl_extension(const char *extension);
* uint32_t al_get_opengl_version(void)
* int al_get_opengl_variant(void)
* void al_set_current_opengl_context(ALLEGRO_DISPLAY *display)
* bool al_install_audio(void)
* void al_uninstall_audio(void)
* bool al_is_audio_installed(void)
* bool al_reserve_samples(int reserve_samples)
* uint32_t al_get_allegro_audio_version(void)
* size_t al_get_audio_depth_size(ALLEGRO_AUDIO_DEPTH depth)
* size_t al_get_channel_count(ALLEGRO_CHANNEL_CONF conf)
* ALLEGRO_VOICE *al_create_voice(unsigned int freq,ALLEGRO_AUDIO_DEPTH depth, ALLEGRO_CHANNEL_CONF chan_conf)
* void al_destroy_voice(ALLEGRO_VOICE *voice)
* void al_detach_voice(ALLEGRO_VOICE *voice)
* bool al_attach_audio_stream_to_voice(ALLEGRO_AUDIO_STREAM *stream,ALLEGRO_VOICE *voice)
* bool al_attach_mixer_to_voice(ALLEGRO_MIXER *mixer, ALLEGRO_VOICE *voice)
* bool al_attach_sample_instance_to_voice(ALLEGRO_SAMPLE_INSTANCE *spl,ALLEGRO_VOICE *voice)
* unsigned int al_get_voice_frequency(const ALLEGRO_VOICE *voice)
* ALLEGRO_CHANNEL_CONF al_get_voice_channels(const ALLEGRO_VOICE *voice)
* ALLEGRO_AUDIO_DEPTH al_get_voice_depth(const ALLEGRO_VOICE *voice)
* bool al_get_voice_playing(const ALLEGRO_VOICE *voice)
* bool al_set_voice_playing(ALLEGRO_VOICE *voice, bool val)
* unsigned int al_get_voice_position(const ALLEGRO_VOICE *voice)
* bool al_set_voice_position(ALLEGRO_VOICE *voice, unsigned int val)
* ALLEGRO_SAMPLE *al_create_sample(void *buf, unsigned int samples,unsigned int freq, ALLEGRO_AUDIO_DEPTH depth,ALLEGRO_CHANNEL_CONF chan_conf, bool free_buf)
* void al_destroy_sample(ALLEGRO_SAMPLE *spl)
* bool al_play_sample(ALLEGRO_SAMPLE *spl, float gain, float pan, float speed,int loop, ALLEGRO_SAMPLE_ID *ret_id)
* void al_stop_sample(ALLEGRO_SAMPLE_ID *spl_id)
* void al_stop_samples(void)
* ALLEGRO_CHANNEL_CONF al_get_sample_channels(const ALLEGRO_SAMPLE *spl)
* ALLEGRO_AUDIO_DEPTH al_get_sample_depth(const ALLEGRO_SAMPLE *spl)
* unsigned int al_get_sample_frequency(const ALLEGRO_SAMPLE *spl)
* unsigned int al_get_sample_length(const ALLEGRO_SAMPLE *spl)
* void *al_get_sample_data(const ALLEGRO_SAMPLE *spl)
* ALLEGRO_SAMPLE_INSTANCE *al_create_sample_instance(ALLEGRO_SAMPLE *sample_data)
* void al_destroy_sample_instance(ALLEGRO_SAMPLE_INSTANCE *spl)
* bool al_play_sample_instance(ALLEGRO_SAMPLE_INSTANCE *spl)
* bool al_stop_sample_instance(ALLEGRO_SAMPLE_INSTANCE *spl)
* ALLEGRO_CHANNEL_CONF al_get_sample_instance_channels(const ALLEGRO_SAMPLE_INSTANCE *spl)
* ALLEGRO_AUDIO_DEPTH al_get_sample_instance_depth(const ALLEGRO_SAMPLE_INSTANCE *spl)
* unsigned int al_get_sample_instance_frequency(const ALLEGRO_SAMPLE_INSTANCE *spl)
* unsigned int al_get_sample_instance_length(const ALLEGRO_SAMPLE_INSTANCE *spl)
* bool al_set_sample_instance_length(ALLEGRO_SAMPLE_INSTANCE *spl,unsigned int val)
* unsigned int al_get_sample_instance_position(const ALLEGRO_SAMPLE_INSTANCE *spl)
* bool al_set_sample_instance_position(ALLEGRO_SAMPLE_INSTANCE *spl,unsigned int val)
* float al_get_sample_instance_speed(const ALLEGRO_SAMPLE_INSTANCE *spl)
* bool al_set_sample_instance_speed(ALLEGRO_SAMPLE_INSTANCE *spl, float val)
* float al_get_sample_instance_gain(const ALLEGRO_SAMPLE_INSTANCE *spl)
* bool al_set_sample_instance_gain(ALLEGRO_SAMPLE_INSTANCE *spl, float val)
* float al_get_sample_instance_pan(const ALLEGRO_SAMPLE_INSTANCE *spl)
* bool al_set_sample_instance_pan(ALLEGRO_SAMPLE_INSTANCE *spl, float val)
* float al_get_sample_instance_time(const ALLEGRO_SAMPLE_INSTANCE *spl)
* ALLEGRO_PLAYMODE al_get_sample_instance_playmode(const ALLEGRO_SAMPLE_INSTANCE *spl)
* bool al_set_sample_instance_playmode(ALLEGRO_SAMPLE_INSTANCE *spl,ALLEGRO_PLAYMODE val)
* bool al_get_sample_instance_playing(const ALLEGRO_SAMPLE_INSTANCE *spl)
* bool al_set_sample_instance_playing(ALLEGRO_SAMPLE_INSTANCE *spl, bool val)
* bool al_get_sample_instance_attached(const ALLEGRO_SAMPLE_INSTANCE *spl)
* bool al_detach_sample_instance(ALLEGRO_SAMPLE_INSTANCE *spl)
* ALLEGRO_SAMPLE *al_get_sample(ALLEGRO_SAMPLE_INSTANCE *spl)
* bool al_set_sample(ALLEGRO_SAMPLE_INSTANCE *spl, ALLEGRO_SAMPLE *data)
* ALLEGRO_MIXER *al_create_mixer(unsigned int freq,ALLEGRO_AUDIO_DEPTH depth, ALLEGRO_CHANNEL_CONF chan_conf)
* void al_destroy_mixer(ALLEGRO_MIXER *mixer)
* ALLEGRO_MIXER *al_get_default_mixer(void)
* bool al_set_default_mixer(ALLEGRO_MIXER *mixer)
* bool al_restore_default_mixer(void)
* bool al_attach_mixer_to_mixer(ALLEGRO_MIXER *stream, ALLEGRO_MIXER *mixer)
* bool al_attach_sample_instance_to_mixer(ALLEGRO_SAMPLE_INSTANCE *spl,ALLEGRO_MIXER *mixer)
* bool al_attach_audio_stream_to_mixer(ALLEGRO_AUDIO_STREAM *stream, ALLEGRO_MIXER *mixer)
* unsigned int al_get_mixer_frequency(const ALLEGRO_MIXER *mixer)
* bool al_set_mixer_frequency(ALLEGRO_MIXER *mixer, unsigned int val)
* ALLEGRO_CHANNEL_CONF al_get_mixer_channels(const ALLEGRO_MIXER *mixer)
* ALLEGRO_AUDIO_DEPTH al_get_mixer_depth(const ALLEGRO_MIXER *mixer)
* float al_get_mixer_gain(const ALLEGRO_MIXER *mixer)
* bool al_set_mixer_gain(ALLEGRO_MIXER *mixer, float new_gain)
* ALLEGRO_MIXER_QUALITY al_get_mixer_quality(const ALLEGRO_MIXER *mixer)
* bool al_set_mixer_quality(ALLEGRO_MIXER *mixer, ALLEGRO_MIXER_QUALITY new_quality)
* bool al_get_mixer_playing(const ALLEGRO_MIXER *mixer)
* bool al_set_mixer_playing(ALLEGRO_MIXER *mixer, bool val)
* bool al_get_mixer_attached(const ALLEGRO_MIXER *mixer)
* bool al_detach_mixer(ALLEGRO_MIXER *mixer)
* void al_destroy_audio_stream(ALLEGRO_AUDIO_STREAM *stream)
* ALLEGRO_EVENT_SOURCE *al_get_audio_stream_event_source(ALLEGRO_AUDIO_STREAM *stream)
* void al_drain_audio_stream(ALLEGRO_AUDIO_STREAM *stream)
* bool al_rewind_audio_stream(ALLEGRO_AUDIO_STREAM *stream)
* unsigned int al_get_audio_stream_frequency(const ALLEGRO_AUDIO_STREAM *stream)
* ALLEGRO_CHANNEL_CONF al_get_audio_stream_channels(const ALLEGRO_AUDIO_STREAM *stream)
* ALLEGRO_AUDIO_DEPTH al_get_audio_stream_depth(const ALLEGRO_AUDIO_STREAM *stream)
* unsigned int al_get_audio_stream_length(const ALLEGRO_AUDIO_STREAM *stream)
* float al_get_audio_stream_speed(const ALLEGRO_AUDIO_STREAM *stream)
* bool al_set_audio_stream_speed(ALLEGRO_AUDIO_STREAM *stream, float val)
* float al_get_audio_stream_gain(const ALLEGRO_AUDIO_STREAM *stream)
* bool al_set_audio_stream_gain(ALLEGRO_AUDIO_STREAM *stream, float val)
* float al_get_audio_stream_pan(const ALLEGRO_AUDIO_STREAM *stream)
* bool al_set_audio_stream_pan(ALLEGRO_AUDIO_STREAM *stream, float val)
* bool al_get_audio_stream_playing(const ALLEGRO_AUDIO_STREAM *stream)
* bool al_set_audio_stream_playing(ALLEGRO_AUDIO_STREAM *stream, bool val)
* ALLEGRO_PLAYMODE al_get_audio_stream_playmode(const ALLEGRO_AUDIO_STREAM *stream)
* bool al_set_audio_stream_playmode(ALLEGRO_AUDIO_STREAM *stream,ALLEGRO_PLAYMODE val)
* bool al_get_audio_stream_attached(const ALLEGRO_AUDIO_STREAM *stream)
* bool al_detach_audio_stream(ALLEGRO_AUDIO_STREAM *stream)
* void *al_get_audio_stream_fragment(const ALLEGRO_AUDIO_STREAM *stream)
* bool al_set_audio_stream_fragment(ALLEGRO_AUDIO_STREAM *stream, void *val)
* unsigned int al_get_audio_stream_fragments(const ALLEGRO_AUDIO_STREAM *stream)
* unsigned int al_get_available_audio_stream_fragments(const ALLEGRO_AUDIO_STREAM *stream)
* bool al_seek_audio_stream_secs(ALLEGRO_AUDIO_STREAM *stream, double time)
* double al_get_audio_stream_position_secs(ALLEGRO_AUDIO_STREAM *stream)
* double al_get_audio_stream_length_secs(ALLEGRO_AUDIO_STREAM *stream)
* bool al_set_audio_stream_loop_secs(ALLEGRO_AUDIO_STREAM *stream,double start, double end)
* ALLEGRO_SAMPLE *al_load_sample(const char *filename)
* ALLEGRO_SAMPLE *al_load_sample_f(ALLEGRO_FILE* fp, const char *ident)
* ALLEGRO_AUDIO_STREAM *al_load_audio_stream(const char *filename,size_t buffer_count, unsigned int samples)
* ALLEGRO_AUDIO_STREAM *al_load_audio_stream_f(ALLEGRO_FILE* fp, const char *ident,size_t buffer_count, unsigned int samples)
* bool al_save_sample(const char *filename, ALLEGRO_SAMPLE *spl)
* bool al_save_sample_f(ALLEGRO_FILE *fp, const char *ident, ALLEGRO_SAMPLE *spl)
* bool al_init_acodec_addon(void)
* uint32_t al_get_allegro_acodec_version(void)
* ALLEGRO_COLOR al_color_cmyk(float c, float m, float y, float k)
* void al_color_cmyk_to_rgb(float cyan, float magenta, float yellow,float key, float *red, float *green, float *blue)
* ALLEGRO_COLOR al_color_hsl(float h, float s, float l)
* void al_color_hsl_to_rgb(float hue, float saturation, float lightness,float *red, float *green, float *blue)
* ALLEGRO_COLOR al_color_hsv(float h, float s, float v)
* void al_color_hsv_to_rgb(float hue, float saturation, float value,float *red, float *green, float *blue)
* ALLEGRO_COLOR al_color_html(char const *string)
* void al_color_html_to_rgb(char const *string,float *red, float *green, float *blue)
* void al_color_rgb_to_html(float red, float green, float blue,char *string)
* ALLEGRO_COLOR al_color_name(char const *name)
* bool al_color_name_to_rgb(char const *name, float *r, float *g, float *b)
* void al_color_rgb_to_cmyk(float red, float green, float blue,float *cyan, float *magenta, float *yellow, float *key)
* void al_color_rgb_to_hsl(float red, float green, float blue,float *hue, float *saturation, float *lightness)
* void al_color_rgb_to_hsv(float red, float green, float blue,float *hue, float *saturation, float *value)
* char const *al_color_rgb_to_name(float r, float g, float b)
* void al_color_rgb_to_yuv(float red, float green, float blue,float *y, float *u, float *v)
* ALLEGRO_COLOR al_color_yuv(float y, float u, float v)
* void al_color_yuv_to_rgb(float y, float u, float v,float *red, float *green, float *blue)
* uint32_t al_get_allegro_color_version(void)
* void al_init_font_addon(void)
* void al_shutdown_font_addon(void)
* ALLEGRO_FONT *al_load_font(char const *filename, int size, int flags)
* void al_destroy_font(ALLEGRO_FONT *f)
* int al_get_font_ascent(const ALLEGRO_FONT *f)
* int al_get_font_descent(const ALLEGRO_FONT *f)
* int al_get_text_width(const ALLEGRO_FONT *f, const char *str)
* int al_get_ustr_width(const ALLEGRO_FONT *f, ALLEGRO_USTR const *ustr)
* void al_draw_text(const ALLEGRO_FONT *font,ALLEGRO_COLOR color, float x, float y, int flags,char const *text)
* void al_draw_ustr(const ALLEGRO_FONT *font,ALLEGRO_COLOR color, float x, float y, int flags,const ALLEGRO_USTR *ustr)
* void al_draw_justified_text(const ALLEGRO_FONT *font,ALLEGRO_COLOR color, float x1, float x2,float y, float diff, int flags, const char *text)
* void al_draw_justified_ustr(const ALLEGRO_FONT *font,ALLEGRO_COLOR color, float x1, float x2,float y, float diff, int flags, const ALLEGRO_USTR *ustr)
* void al_get_text_dimensions(const ALLEGRO_FONT *f,char const *text,int *bbx, int *bby, int *bbw, int *bbh)
* void al_get_ustr_dimensions(const ALLEGRO_FONT *f,ALLEGRO_USTR const *ustr,int *bbx, int *bby, int *bbw, int *bbh)
* uint32_t al_get_allegro_font_version(void)
* ALLEGRO_FONT *al_grab_font_from_bitmap(ALLEGRO_BITMAP *bmp,int ranges_n, const int ranges[])
* ALLEGRO_FONT *al_load_bitmap_font(const char *fname)
* ALLEGRO_FONT *al_create_builtin_font(void)
* bool al_init_ttf_addon(void)
* void al_shutdown_ttf_addon(void)
* ALLEGRO_FONT *al_load_ttf_font(char const *filename, int size, int flags)
* ALLEGRO_FONT *al_load_ttf_font_f(ALLEGRO_FILE *file,char const *filename, int size, int flags)
* ALLEGRO_FONT *al_load_ttf_font_stretch(char const *filename, int w, int h,int flags)
* ALLEGRO_FONT *al_load_ttf_font_stretch_f(ALLEGRO_FILE *file,char const *filename, int w, int h, int flags)
* uint32_t al_get_allegro_ttf_version(void)
* bool al_init_image_addon(void)
* void al_shutdown_image_addon(void)
* uint32_t al_get_allegro_image_version(void)
* ALLEGRO_FILE *al_open_memfile(void *mem, int64_t size, const char *mode)
* uint32_t al_get_allegro_memfile_version(void)
* bool al_init_native_dialog_addon(void)
* void al_shutdown_native_dialog_addon(void)
* ALLEGRO_FILECHOOSER *al_create_native_file_dialog(char const *initial_path,char const *title,char const *patterns,int mode)
* bool al_show_native_file_dialog(ALLEGRO_DISPLAY *display,ALLEGRO_FILECHOOSER *dialog)
* int al_get_native_file_dialog_count(const ALLEGRO_FILECHOOSER *dialog)
* const char *al_get_native_file_dialog_path(const ALLEGRO_FILECHOOSER *dialog, size_t i)
* void al_destroy_native_file_dialog(ALLEGRO_FILECHOOSER *dialog)
* int al_show_native_message_box(ALLEGRO_DISPLAY *display,char const *title, char const *heading, char const *text,char const *buttons, int flags)
* ALLEGRO_TEXTLOG *al_open_native_text_log(char const *title, int flags)
* void al_close_native_text_log(ALLEGRO_TEXTLOG *textlog)
* uint32_t al_get_allegro_native_dialog_version(void)
* void al_set_physfs_file_interface(void)
* uint32_t al_get_allegro_physfs_version(void)
* uint32_t al_get_allegro_primitives_version(void)
* bool al_init_primitives_addon(void)
* void al_shutdown_primitives_addon(void)
* void al_draw_line(float x1, float y1, float x2, float y2,ALLEGRO_COLOR color, float thickness)
* void al_draw_triangle(float x1, float y1, float x2, float y2,float x3, float y3, ALLEGRO_COLOR color, float thickness)
* void al_draw_filled_triangle(float x1, float y1, float x2, float y2,float x3, float y3, ALLEGRO_COLOR color)
* void al_draw_rectangle(float x1, float y1, float x2, float y2,ALLEGRO_COLOR color, float thickness)
* void al_draw_filled_rectangle(float x1, float y1, float x2, float y2,ALLEGRO_COLOR color)
* void al_draw_rounded_rectangle(float x1, float y1, float x2, float y2,float rx, float ry, ALLEGRO_COLOR color, float thickness)
* void al_draw_filled_rounded_rectangle(float x1, float y1, float x2, float y2,float rx, float ry, ALLEGRO_COLOR color)
* void al_calculate_arc(float* dest, int stride, float cx, float cy,float rx, float ry, float start_theta, float delta_theta, float thickness,int num_points)
* void al_draw_pieslice(float cx, float cy, float r, float start_theta,float delta_theta, ALLEGRO_COLOR color, float thickness)
* void al_draw_filled_pieslice(float cx, float cy, float r, float start_theta,float delta_theta, ALLEGRO_COLOR color)
* void al_draw_ellipse(float cx, float cy, float rx, float ry,ALLEGRO_COLOR color, float thickness)
* void al_draw_filled_ellipse(float cx, float cy, float rx, float ry,ALLEGRO_COLOR color)
* void al_draw_circle(float cx, float cy, float r, ALLEGRO_COLOR color,float thickness)
* void al_draw_filled_circle(float cx, float cy, float r, ALLEGRO_COLOR color)
* void al_draw_arc(float cx, float cy, float r, float start_theta,float delta_theta, ALLEGRO_COLOR color, float thickness)
* void al_draw_elliptical_arc(float cx, float cy, float rx, float ry, float start_theta,float delta_theta, ALLEGRO_COLOR color, float thickness)
* void al_draw_ribbon(const float *points, int points_stride, ALLEGRO_COLOR color,float thickness, int num_segments)
* int al_draw_prim(const void* vtxs, const ALLEGRO_VERTEX_DECL* decl,ALLEGRO_BITMAP* texture, int start, int end, int type)
* int al_draw_indexed_prim(const void* vtxs, const ALLEGRO_VERTEX_DECL* decl,ALLEGRO_BITMAP* texture, const int* indices, int num_vtx, int type)
* ALLEGRO_VERTEX_DECL* al_create_vertex_decl(const ALLEGRO_VERTEX_ELEMENT* elements, int stride)
* void al_destroy_vertex_decl(ALLEGRO_VERTEX_DECL* decl)
* ALLEGRO_SHADER *al_create_shader(ALLEGRO_SHADER_PLATFORM platform)
* bool al_attach_shader_source(ALLEGRO_SHADER *shader, ALLEGRO_SHADER_TYPE type,const char *source)
* bool al_attach_shader_source_file(ALLEGRO_SHADER *shader,ALLEGRO_SHADER_TYPE type, const char *filename)
* bool al_build_shader(ALLEGRO_SHADER *shader)
* const char *al_get_shader_log(ALLEGRO_SHADER *shader)
* ALLEGRO_SHADER_PLATFORM al_get_shader_platform(ALLEGRO_SHADER *shader)
* bool al_use_shader(ALLEGRO_SHADER *shader)
* void al_destroy_shader(ALLEGRO_SHADER *shader)
* bool al_set_shader_sampler(const char *name,ALLEGRO_BITMAP *bitmap, int unit)
* bool al_set_shader_matrix(const char *name,const ALLEGRO_TRANSFORM *matrix)
* bool al_set_shader_int(const char *name, int i)
* bool al_set_shader_float(const char *name, float f)
* bool al_set_shader_bool(const char *name, bool b)
* bool al_set_shader_int_vector(const char *name,int num_components, const int *i, int num_elems)
* bool al_set_shader_float_vector(const char *name,int num_components, const float *f, int num_elems)
* char const *al_get_default_shader_source(ALLEGRO_SHADER_PLATFORM platform,ALLEGRO_SHADER_TYPE type)
