.. index:: 
     single: RingMurmurHash Functions Reference; Introduction

=====================================
RingMurmurHash Functions Reference
=====================================

Murmurhash extension is an extension written to implement a full implementation for the MurmurHash library.

.. index:: 
     pair: RingMurmurHash Functions Reference; MurmurHash1 functions

MurmurHash1 functions
=====================

uint32_t murmurhash1(string key, int seed, [bool return_type]);

uint32_t murmurhash1_aligned(string key, int seed, [bool return_type]);


.. index:: 
     pair: RingMurmurHash Functions Reference; MurmurHash2 functions

MurmurHash2 functions
=====================

uint32_t murmurhash2(string key, int seed, [bool return_type]);

uint32_t murmurhash2a(string key, int seed, [bool return_type]);

uint64_t murmurhash64a(string key, int seed, [bool return_type]);

uint64_t murmurhash64b(string key, int seed, [bool return_type]);

uint32_t murmurhash_neutral2(string key, int seed, [bool return_type]);

uint32_t murmurhash_aligned2(string key, int seed, [bool return_type]);

.. index:: 
     pair: RingMurmurHash Functions Reference; MurmurHash3 functions

MurmurHash3 functions
=====================

uint32_t murmurhash3_x86_32(string key, int seed, [bool return_type]);

list murmurhash3_x86_128(string key, int seed, [bool return_type]);

list murmurhash3_x64_128(string key, int seed, [bool return_type]);

The third optional parameter is to set the type of the returned value, this parameter accepts a bool value [ true, false ], true will return a Hex value, while false will return a integer value.

.. index:: 
     pair: RingMurmurHash Functions Reference; Example

Example
=======


.. code-block:: ring

	load "murmurhashlib.ring"

	key = "Ring Language"

	see murmurhash3_x86_32(key, 0, 0) + nl // Output: 1894444853
	see murmurhash3_x86_32(key, 0, 1) + nl // Output: 70eaef35

