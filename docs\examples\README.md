# أمثلة الاستخدام

## 1. الترجمة الأساسية

### ترجمة نص واحد
```ring
load "src/core/transformer.ring"

func main
    # إنشاء كائن الترجمة
    transformer = new Transformer
    
    # ترجمة نص من الإنجليزية إلى العربية
    text = "Hello, how are you?"
    translated = transformer.translate(text, "en", "ar")
    
    ? "النص الأصلي: " + text
    ? "الترجمة: " + translated
    
    # ترجمة من العربية إلى الإنجليزية
    arText = "مرحباً، كيف حالك؟"
    enTranslated = transformer.translate(arText, "ar", "en")
    
    ? "النص الأصلي: " + arText
    ? "الترجمة: " + enTranslated

main()
```

### ترجمة مجموعة نصوص
```ring
load "src/core/transformer.ring"

func main
    transformer = new Transformer
    
    texts = [
        "Good morning",
        "How are you today?",
        "The weather is nice"
    ]
    
    ? "ترجمة مجموعة نصوص:"
    ? "-------------------"
    
    for text in texts
        translated = transformer.translate(text, "en", "ar")
        ? "الأصل: " + text
        ? "الترجمة: " + translated
        ? ""
    next

main()
```

## 2. توليد الكود

### توليد دالة بسيطة
```ring
load "src/core/codegen.ring"

func main
    generator = new CodeGenerator
    
    description = "دالة لحساب مجموع مصفوفة من الأرقام"
    code = generator.generate(description)
    
    ? "الكود المولد:"
    ? "------------"
    ? code

```

### توليد كود مع اختبارات
```ring
load "src/core/codegen.ring"

func main
    generator = new CodeGenerator
    
    description = "صنف لتمثيل حساب بنكي مع دوال للإيداع والسحب"
    code = generator.generateWithTests(description)
    
    ? "الكود والاختبارات:"
    ? "-----------------"
    ? code

```

## 3. استخدام API

### الترجمة عبر API
```javascript
// استخدام fetch للاتصال بالـ API
async function translateText(text, sourceLang = 'en', targetLang = 'ar') {
    const response = await fetch('http://localhost:8080/api/v1/translate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer your-token-here'
        },
        body: JSON.stringify({
            text,
            source_lang: sourceLang,
            target_lang: targetLang
        })
    });
    
    return await response.json();
}

// مثال على الاستخدام
translateText('Hello, World!')
    .then(result => console.log(result))
    .catch(error => console.error(error));
```

### توليد الكود عبر API
```javascript
async function generateCode(description, language = 'Ring') {
    const response = await fetch('http://localhost:8080/api/v1/code/generate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer your-token-here'
        },
        body: JSON.stringify({
            description,
            language,
            with_tests: true
        })
    });
    
    return await response.json();
}

// مثال على الاستخدام
generateCode('دالة لفرز مصفوفة تصاعدياً')
    .then(result => console.log(result))
    .catch(error => console.error(error));
```

## 4. استخدام قاعدة البيانات

### إدارة الترجمات
```ring
load "src/data/database/DatabaseManager.ring"
load "src/data/database/QueryBuilder.ring"

func main
    # إنشاء اتصال بقاعدة البيانات
    db = new DatabaseManager
    
    # إنشاء استعلام
    query = new QueryBuilder("translations")
    query.select([
        "source_text",
        "target_text",
        "source_lang",
        "target_lang"
    ])
    query.where("source_lang", "=", "en")
    query.limit(10)
    
    # تنفيذ الاستعلام
    results = db.sqlite_query(db.db, query.build())
    
    # عرض النتائج
    for result in results
        ? "النص الأصلي: " + result[:source_text]
        ? "الترجمة: " + result[:target_text]
        ? ""
    next
    
    # إغلاق الاتصال
    db.close()

```

## 5. استخدام التخزين المؤقت

### تخزين نتائج الترجمة
```ring
load "src/api/services/cache_service.ring"
load "src/core/transformer.ring"

func main
    cache = new CacheService
    transformer = new Transformer
    
    text = "Hello, World!"
    cacheKey = "trans:" + text + ":en:ar"
    
    # محاولة استرجاع الترجمة من التخزين المؤقت
    result = cache.get(cacheKey)
    
    if result = null
        # الترجمة غير موجودة في التخزين المؤقت
        result = transformer.translate(text, "en", "ar")
        # تخزين النتيجة
        cache.set(cacheKey, result, 3600)  # تخزين لمدة ساعة
    ok
    
    ? "الترجمة: " + result

```

## ملاحظات هامة
1. تأكد من تشغيل الخادم قبل استخدام API
2. استبدل `your-token-here` بالتوكن الخاص بك
3. تأكد من تثبيت جميع المكتبات المطلوبة
4. راجع التوثيق للحصول على المزيد من الخيارات والإعدادات
