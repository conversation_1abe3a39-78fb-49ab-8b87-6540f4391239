.. index:: 
     single: RingLibCurl Functions Reference; Introduction

===============================
RingLibCurl Functions Reference
===============================


Introduction
============

In this chapter we have a list of the supported functions by this extension 

Reference
=========

* CURL_GLOBAL_ALL
* CURL_GLOBAL_SSL
* CURL_GLOBAL_WIN32
* CURL_GLOBAL_NOTHING
* CURL_GLOBAL_DEFAULT
* CURL_GLOBAL_ACK_EINTR
* CURLOPT_VERBOSE
* CURLOPT_HEADER
* CURLOPT_NOPROGRESS
* CURLOPT_NOSIGNAL
* CURLOPT_WILDCARDMATCH
* CURLOPT_WRITEFUNCTION
* CURLOPT_WRITEDATA
* CURLOPT_READFUNCTION
* CURLOPT_READDATA
* CURLOPT_IOCTLFUNCTION
* CURLOPT_IOCTLDATA
* CURLOPT_SEEKFUNCTION
* CURLOPT_SEEKDATA
* CURLOPT_SOCKOPTFUNCTION
* CURLOPT_SOCKOPTDATA
* CURLOPT_OPENSOCKETFUNCTION
* CURLOPT_OPENSOCKETDATA
* CURLOPT_CLOSESOCKETFUNCTION
* CURLOPT_CLOSESOCKETDATA
* CURLOPT_PROGRESSFUNCTION
* CURLOPT_PROGRESSDATA
* CURLOPT_HEADERFUNCTION
* CURLOPT_HEADERDATA
* CURLOPT_DEBUGFUNCTION
* CURLOPT_DEBUGDATA
* CURLOPT_SSL_CTX_FUNCTION
* CURLOPT_SSL_CTX_DATA
* CURLOPT_CONV_TO_NETWORK_FUNCTION
* CURLOPT_CONV_FROM_NETWORK_FUNCTION
* CURLOPT_CONV_FROM_UTF8_FUNCTION
* CURLOPT_INTERLEAVEFUNCTION
* CURLOPT_INTERLEAVEDATA
* CURLOPT_CHUNK_BGN_FUNCTION
* CURLOPT_CHUNK_END_FUNCTION
* CURLOPT_CHUNK_DATA
* CURLOPT_FNMATCH_FUNCTION
* CURLOPT_FNMATCH_DATA
* CURLOPT_ERRORBUFFER
* CURLOPT_STDERR
* CURLOPT_FAILONERROR
* CURLOPT_URL
* CURLOPT_PROTOCOLS
* CURLOPT_REDIR_PROTOCOLS
* CURLOPT_PROXY
* CURLOPT_PROXYPORT
* CURLOPT_PROXYTYPE
* CURLOPT_NOPROXY
* CURLOPT_HTTPPROXYTUNNEL
* CURLOPT_SOCKS5_GSSAPI_SERVICE
* CURLOPT_SOCKS5_GSSAPI_NEC
* CURLOPT_INTERFACE
* CURLOPT_LOCALPORT
* CURLOPT_LOCALPORTRANGE
* CURLOPT_DNS_CACHE_TIMEOUT
* CURLOPT_DNS_USE_GLOBAL_CACHE
* CURLOPT_BUFFERSIZE
* CURLOPT_PORT
* CURLOPT_TCP_NODELAY
* CURLOPT_ADDRESS_SCOPE
* CURLOPT_NETRC
* CURLOPT_NETRC_FILE
* CURLOPT_USERPWD
* CURLOPT_PROXYUSERPWD
* CURLOPT_USERNAME
* CURLOPT_PASSWORD
* CURLOPT_PROXYUSERNAME
* CURLOPT_PROXYPASSWORD
* CURLOPT_HTTPAUTH
* CURLOPT_TLSAUTH_USERNAME
* CURLOPT_TLSAUTH_PASSWORD
* CURLOPT_TLSAUTH_TYPE
* CURLOPT_PROXYAUTH
* CURLOPT_AUTOREFERER
* CURLOPT_ACCEPT_ENCODING
* CURLOPT_TRANSFER_ENCODING
* CURLOPT_FOLLOWLOCATION
* CURLOPT_UNRESTRICTED_AUTH
* CURLOPT_MAXREDIRS
* CURLOPT_POSTREDIR
* CURLOPT_PUT
* CURLOPT_POST
* CURLOPT_POSTFIELDS
* CURLOPT_POSTFIELDSIZE
* CURLOPT_POSTFIELDSIZE_LARGE
* CURLOPT_COPYPOSTFIELDS
* CURLOPT_HTTPPOST
* CURLOPT_REFERER
* CURLOPT_USERAGENT
* CURLOPT_HTTPHEADER
* CURLOPT_HTTP200ALIASES
* CURLOPT_COOKIE
* CURLOPT_COOKIEFILE
* CURLOPT_COOKIEJAR
* CURLOPT_COOKIESESSION
* CURLOPT_COOKIELIST
* CURLOPT_HTTPGET
* CURLOPT_HTTP_VERSION
* CURLOPT_IGNORE_CONTENT_LENGTH
* CURLOPT_HTTP_CONTENT_DECODING
* CURLOPT_HTTP_TRANSFER_DECODING
* CURLOPT_MAIL_FROM
* CURLOPT_MAIL_RCPT
* CURLOPT_TFTP_BLKSIZE
* CURLOPT_FTPPORT
* CURLOPT_QUOTE
* CURLOPT_POSTQUOTE
* CURLOPT_PREQUOTE
* CURLOPT_APPEND
* CURLOPT_FTP_USE_EPRT
* CURLOPT_FTP_USE_EPSV
* CURLOPT_FTP_USE_PRET
* CURLOPT_FTP_CREATE_MISSING_DIRS
* CURLOPT_FTP_RESPONSE_TIMEOUT
* CURLOPT_FTP_ALTERNATIVE_TO_USER
* CURLOPT_FTP_SKIP_PASV_IP
* CURLOPT_FTPSSLAUTH
* CURLOPT_FTP_SSL_CCC
* CURLOPT_FTP_ACCOUNT
* CURLOPT_FTP_FILEMETHOD
* CURLOPT_RTSP_REQUEST
* CURLOPT_RTSP_SESSION_ID
* CURLOPT_RTSP_STREAM_URI
* CURLOPT_RTSP_TRANSPORT
* CURLOPT_RTSP_CLIENT_CSEQ
* CURLOPT_RTSP_SERVER_CSEQ
* CURLOPT_TRANSFERTEXT
* CURLOPT_PROXY_TRANSFER_MODE
* CURLOPT_CRLF
* CURLOPT_RANGE
* CURLOPT_RESUME_FROM
* CURLOPT_RESUME_FROM_LARGE
* CURLOPT_CUSTOMREQUEST
* CURLOPT_FILETIME
* CURLOPT_DIRLISTONLY
* CURLOPT_NOBODY
* CURLOPT_INFILESIZE
* CURLOPT_INFILESIZE_LARGE
* CURLOPT_UPLOAD
* CURLOPT_MAXFILESIZE
* CURLOPT_MAXFILESIZE_LARGE
* CURLOPT_TIMECONDITION
* CURLOPT_TIMEVALUE
* CURLOPT_TIMEOUT
* CURLOPT_TIMEOUT_MS
* CURLOPT_LOW_SPEED_LIMIT
* CURLOPT_LOW_SPEED_TIME
* CURLOPT_MAX_SEND_SPEED_LARGE
* CURLOPT_MAX_RECV_SPEED_LARGE
* CURLOPT_MAXCONNECTS
* CURLOPT_FRESH_CONNECT
* CURLOPT_FORBID_REUSE
* CURLOPT_CONNECTTIMEOUT
* CURLOPT_CONNECTTIMEOUT_MS
* CURLOPT_IPRESOLVE
* CURLOPT_CONNECT_ONLY
* CURLOPT_USE_SSL
* CURLOPT_RESOLVE
* CURLOPT_SSLCERT
* CURLOPT_SSLCERTTYPE
* CURLOPT_SSLKEY
* CURLOPT_SSLKEYTYPE
* CURLOPT_KEYPASSWD
* CURLOPT_SSLENGINE
* CURLOPT_SSLENGINE_DEFAULT
* CURLOPT_SSLVERSION
* CURLOPT_SSL_VERIFYHOST
* CURLOPT_SSL_VERIFYPEER
* CURLOPT_CAINFO
* CURLOPT_ISSUERCERT
* CURLOPT_CAPATH
* CURLOPT_CRLFILE
* CURLOPT_CERTINFO
* CURLOPT_RANDOM_FILE
* CURLOPT_EGDSOCKET
* CURLOPT_SSL_CIPHER_LIST
* CURLOPT_SSL_SESSIONID_CACHE
* CURLOPT_KRBLEVEL
* CURLOPT_GSSAPI_DELEGATION
* CURLOPT_SSH_AUTH_TYPES
* CURLOPT_SSH_HOST_PUBLIC_KEY_MD5
* CURLOPT_SSH_PUBLIC_KEYFILE
* CURLOPT_SSH_PRIVATE_KEYFILE
* CURLOPT_SSH_KNOWNHOSTS
* CURLOPT_SSH_KEYFUNCTION
* CURLOPT_SSH_KEYDATA
* CURLOPT_PRIVATE
* CURLOPT_SHARE
* CURLOPT_NEW_FILE_PERMS
* CURLOPT_NEW_DIRECTORY_PERMS
* CURLOPT_TELNETOPTIONS
* CURLE_OK
* CURLE_UNKNOWN_OPTION
* CURLE_NOT_BUILT_IN
* CURLINFO_EFFECTIVE_URL
* CURLINFO_RESPONSE_CODE
* CURLINFO_HTTP_CONNECTCODE
* CURLINFO_FILETIME
* CURLINFO_TOTAL_TIME
* CURLINFO_NAMELOOKUP_TIME
* CURLINFO_CONNECT_TIME
* CURLINFO_APPCONNECT_TIME
* CURLINFO_PRETRANSFER_TIME
* CURLINFO_STARTTRANSFER_TIME
* CURLINFO_REDIRECT_TIME
* CURLINFO_REDIRECT_COUNT
* CURLINFO_REDIRECT_URL
* CURLINFO_SIZE_UPLOAD
* CURLINFO_SIZE_DOWNLOAD
* CURLINFO_SPEED_DOWNLOAD
* CURLINFO_SPEED_UPLOAD
* CURLINFO_HEADER_SIZE
* CURLINFO_REQUEST_SIZE
* CURLINFO_SSL_VERIFYRESULT
* CURLINFO_SSL_ENGINES
* CURLINFO_CONTENT_LENGTH_DOWNLOAD
* CURLINFO_CONTENT_LENGTH_UPLOAD
* CURLINFO_CONTENT_TYPE
* CURLINFO_PRIVATE
* CURLINFO_HTTPAUTH_AVAIL
* CURLINFO_PROXYAUTH_AVAIL
* CURLINFO_OS_ERRNO
* CURLINFO_NUM_CONNECTS
* CURLINFO_PRIMARY_IP
* CURLINFO_PRIMARY_PORT
* CURLINFO_LOCAL_IP
* CURLINFO_LOCAL_PORT
* CURLINFO_COOKIELIST
* CURLINFO_LASTSOCKET
* CURLINFO_FTP_ENTRY_PATH
* CURLINFO_CERTINFO
* CURLINFO_CONDITION_UNMET
* CURLINFO_RTSP_SESSION_ID
* CURLINFO_RTSP_CLIENT_CSEQ
* CURLINFO_RTSP_SERVER_CSEQ
* CURLINFO_RTSP_CSEQ_RECV
* CURLFORM_COPYNAME
* CURLFORM_PTRNAME
* CURLFORM_COPYCONTENTS
* CURLFORM_PTRCONTENTS
* CURLFORM_CONTENTSLENGTH
* CURLFORM_FILECONTENT
* CURLFORM_FILE
* CURLFORM_CONTENTTYPE
* CURLFORM_FILENAME
* CURLFORM_BUFFER
* CURLFORM_BUFFERPTR
* CURLFORM_BUFFERLENGTH
* CURLFORM_STREAM
* CURLFORM_ARRAY
* CURLFORM_CONTENTHEADER
* CURLcode curl_global_init(long flags)
* CURL *curl_easy_init(void)
* void curl_easy_cleanup(CURL * handle )
* CURLcode curl_easy_setopt_1(CURL *handle, CURLoption option, int)
* CURLcode curl_easy_setopt_2(CURL *handle, CURLoption option, const char *)
* CURLcode curl_easy_setopt_3(CURL *handle, CURLoption option, void *)
* CURLcode curl_easy_setopt_4(CURL *handle, CURLoption option, CURLLIST *)
* CURLcode curl_easy_perform(CURL * easy_handle )
* String *curl_easy_perform_silent(CURL * easy_handle )
* CURLcode curl_easy_getinfo_1(CURL *handle, CURLINFO info, char **urlp)
* CURLcode curl_easy_getinfo_2(CURL *handle, CURLINFO info, long *codep)
* CURLcode curl_easy_getinfo_3(CURL *handle, CURLINFO info, double *timep)
* CURLcode curl_easy_getinfo_4(CURL *handle, CURLINFO info, CURLLIST **engine_list)
* CURLcode curl_easy_getinfo_5(CURL *handle, CURLINFO info, struct curl_certinfo *chainp)
* CURLcode curl_easy_getinfo_6(CURL *handle, CURLINFO info, struct curl_tlssessioninfo **session)
* char *curl_version(void)
* time_t curl_getdate(char * datestring , time_t *now )
* CURLFORMcode curl_formadd_1(struct curl_httppost **firstitem, struct curl_httppost **lastitem, CURLformoption, const char *, CURLformoption, const char *, CURLformoption)
* CURLFORMcode curl_formadd_2(struct curl_httppost **firstitem, struct curl_httppost **lastitem, CURLformoption, const char *, CURLformoption, const char *,CURLformoption, const char *, CURLformoption)
* CURLFORMcode curl_formadd_3(struct curl_httppost **firstitem, struct curl_httppost **lastitem, CURLformoption, const char *, CURLformoption, void *, CURLformoption)
* CURLFORMcode curl_formadd_4(struct curl_httppost **firstitem, struct curl_httppost **lastitem, CURLformoption, const char *, CURLformoption, void *,CURLformoption, long , CURLformoption)
* CURLFORMcode curl_formadd_5(struct curl_httppost **firstitem, struct curl_httppost **lastitem, CURLformoption, const char *, CURLformoption, void *,CURLformoption, long , CURLformoption, const char* , CURLformoption)
* CURLFORMcode curl_formadd_6(struct curl_httppost **firstitem, struct curl_httppost **lastitem, CURLformoption, const char *, CURLformoption, const char *,CURLformoption, void * , CURLformoption, long , CURLformoption)
* CURLFORMcode curl_formadd_7(struct curl_httppost **firstitem, struct curl_httppost **lastitem, CURLformoption, const char *, CURLformoption,  struct curl_forms [], CURLformoption)
* void curl_formfree(struct curl_httppost * form)
* CURLLIST *curl_slist_append(CURLLIST * list, const char * string )
* void curl_slist_free_all(CURLLIST * list)
* char *curl_easy_escape( CURL * curl , const char * string , int length )
* char *curl_easy_unescape( CURL * curl , const char * url , int inlength , int * outlength )
