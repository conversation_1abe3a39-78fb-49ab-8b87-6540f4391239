
Class CacheManager {
   
    
    func init {
        cache = []
        hits = 0
        misses = 0
    }

    func getkey(key) {
        if !isString(key) return null ok
        
        try {
            if exists(key) {
                hits++
                return cache[key]
            }
            misses++
            return null
        catch
            misses++
            return null
        }
    }

    func setkey(key, value) {
        if !isString(key) return self ok
        
        try {
            if len(cache) >= maxSize {
                removeOldest()
            }
            cache[key] = value
        catch
            # تجاهل الخطأ
        }
        return self
    }

    func exists(key) {
        return isString(key) and cache[key] != NULL
    }

    func clear {
        cache = []
        hits = 0
        misses = 0
        return self
    }

    func getStats {
        total = hits + misses
        hitRate = total > 0 ? (hits * 100.0) / total : 0
        
        return [
            :size = len(cache),
            :maxSize = maxSize,
            :hits = hits,
            :misses = misses,
            :hitRate = hitRate
        ]
    }

    private 

    cache = []
    maxSize = 1000
    hits = 0
    misses = 0

    func removeOldest {
        if len(cache) > 0 {
            firstKey = cache.keys()[1]
            delete(cache, firstKey)
        }
        return self
    }
    
}
