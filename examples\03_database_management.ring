load "src/data/database/DatabaseManager.ring"
load "src/data/database/DataCleaner.ring"
load "src/data/database/DataUpdater.ring"

? "مثال على إدارة قاعدة البيانات"
? "=========================="

# إنشاء مدير قاعدة البيانات
db = new DatabaseManager

# إضافة بعض الترجمات
translations = [
    ["Hello", "مرحبا", "en", "ar"],
    ["Good morning", "صباح الخير", "en", "ar"],
    ["How are you?", "كيف حالك؟", "en", "ar"]
]

updater = new DataUpdater
? "إضافة ترجمات جديدة..."
updater.batchUpdateTranslations(translations)

# البحث عن ترجمات
query = new QueryBuilder("translations")
query.where("source_lang", "=", "en")
results = db.sqlite_query(db.db, query.build())

? "الترجمات المخزنة:"
? "---------------"
for result in results
    ? "المصدر: " + result[:source_text]
    ? "الترجمة: " + result[:target_text]
    ? ""
next

# تنظيف البيانات
cleaner = new DataCleaner
? "تنظيف البيانات..."
cleaner.cleanAllTables()

# إضافة بيانات أداء
metrics = [
    ["translation", 0.5, 100.5, 0.95],
    ["code_generation", 1.2, 150.3, 0.88]
]

? "إضافة بيانات الأداء..."
updater.updatePerformanceMetrics(metrics)

# عرض إحصائيات الأداء
query = new QueryBuilder("performance_metrics")
query.select([
    "operation_type",
    "AVG(execution_time) as avg_time",
    "AVG(success_rate) as avg_success"
])
query.groupBy("operation_type")
stats = db.sqlite_query(db.db, query.build())

? "إحصائيات الأداء:"
? "-------------"
for stat in stats
    ? "العملية: " + stat[:operation_type]
    ? "متوسط وقت التنفيذ: " + stat[:avg_time]
    ? "متوسط معدل النجاح: " + stat[:avg_success]
    ? ""
next

# عمل نسخة احتياطية
? "إنشاء نسخة احتياطية..."
if updater.createBackup("backup_example.db")
    ? "تم إنشاء النسخة الاحتياطية بنجاح"
ok

# إغلاق الاتصال
db.close()
