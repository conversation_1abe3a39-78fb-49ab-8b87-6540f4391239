load "stdlibcore.ring"
load "logger.ring"

# مسجل الأخطاء
Class ErrorLogger from Logger {
    # المتغيرات
    errors
    
    func init(filename) {
        super.init(filename)
        errors = []
    }
    
    # تسجيل خطأ مع التفاصيل
    func logError(errorType, message, details = "", stackTrace = "") {
        # تخزين الخطأ
        error = {
            "type": errorType,
            "message": message,
            "details": details,
            "stackTrace": stackTrace,
            "timestamp": date() + " " + time()
        }
        errors + error
        
        # تنسيق رسالة الخطأ
        errorMessage = sprintf("نوع الخطأ: %s" + nl +
                             "الرسالة: %s" + nl, [errorType, message])
        
        if details != "" {
            errorMessage += "التفاصيل: " + details + nl
        }
        
        if stackTrace != "" {
            errorMessage += "تتبع المكدس: " + nl + stackTrace + nl
        }
        
        # تسجيل الخطأ
        error(errorMessage)
    }
    
    # تسجيل استثناء
    func logException(e) {
        logError("Exception", e.getMessage(), e.getDetails(), e.getStackTrace())
    }
    
    # توليد تقرير الأخطاء
    func generateErrorReport(filename) {
        try {
            fp = fopen(filename, "w")
            
            # كتابة الترويسة
            fwrite(fp, "تقرير الأخطاء" + nl)
            fwrite(fp, "============" + nl + nl)
            
            # تصنيف الأخطاء حسب النوع
            errorsByType = []
            
            for error in errors {
                if not errorsByType[error["type"]] {
                    errorsByType[error["type"]] = []
                }
                errorsByType[error["type"]] + error
            }
            
            # كتابة الأخطاء
            for type in errorsByType {
                fwrite(fp, "نوع الخطأ: " + type + nl)
                fwrite(fp, "-----------------" + nl)
                
                for error in errorsByType[type] {
                    fwrite(fp, sprintf("الوقت: %s" + nl, error["timestamp"]))
                    fwrite(fp, sprintf("الرسالة: %s" + nl, error["message"]))
                    
                    if error["details"] != "" {
                        fwrite(fp, "التفاصيل:" + nl)
                        fwrite(fp, error["details"] + nl)
                    }
                    
                    if error["stackTrace"] != "" {
                        fwrite(fp, "تتبع المكدس:" + nl)
                        fwrite(fp, error["stackTrace"] + nl)
                    }
                    
                    fwrite(fp, nl)
                }
            }
            
            # كتابة الملخص
            fwrite(fp, "ملخص الأخطاء" + nl)
            fwrite(fp, "------------" + nl)
            for type in errorsByType {
                fwrite(fp, sprintf("%s: %d خطأ" + nl,
                                [type, len(errorsByType[type])])
            }
            
            fclose(fp)
            info("تم توليد تقرير الأخطاء: " + filename)
        catch
            error("خطأ في توليد تقرير الأخطاء: " + cCatchError)
        }
    }
    
    # الحصول على عدد الأخطاء
    func getErrorCount {
        return len(errors)
    }
    
    # الحصول على عدد الأخطاء من نوع معين
    func getErrorCountByType(type) {
        count = 0
        for error in errors {
            if error["type"] = type {
                count++
            }
        }
        return count
    }
    
    # إعادة تعيين الأخطاء
    func resetErrors {
        errors = []
    }
}
