.. index:: 
	single: Building RingQt Applications for WebAssembly; Introduction

============================================
Building RingQt Applications for WebAssembly
============================================

In this chapter we will learn about Building RingQt Applications for WebAssembly.

.. index:: 
	pair: Building RingQt Applications for WebAssembly; Download Requirements

Download Requirements
=====================

Check the next link : https://doc.qt.io/qt-5/wasm.html

Tested using

* Qt (5.15.2)

* Qt Creator (11.0.2)

* Emscripten (1.39.8) : https://emscripten.org/docs/getting_started/index.html

Use Git to have emsdk 

.. code-block:: ring

	# Get the emsdk repo
	git clone https://github.com/emscripten-core/emsdk.git

	# Enter that directory
	cd emsdk

Use emsdk to install and activate the required version for Qt 5.15

.. code-block:: ring

	emsdk install 1.39.8
	emsdk activate --embedded 1.39.8

Check Emscripten installation

.. code-block:: ring

	em++ --version

Output

.. code-block:: ring

	emcc (Emscripten gcc/clang-like replacement) 1.39.8 
	(commit 24d88487f47629fac9d4acd231497a3a412bdee8)
	Copyright (C) 2014 the Emscripten authors (see AUTHORS.txt)
	This is free and open source software under the MIT license.
	There is NO warranty; not even for MERCHANTABILITY or FITNESS FOR A 
	PARTICULAR PURPOSE.

* Run Qt Creator
* Select Tools > Options > Devices > WebAssembly

.. image:: qtwebasm_tools.png
	:alt: Qt for WebAssembly

.. index:: 
	pair: Building RingQt Applications for WebAssembly; Using Ring2EXE

Using Ring2EXE
==============

We can use Ring2EXE to quickly prepare Qt project for our application

Example:

.. code-block:: none

	ring2exe myapp.ring -dist -webassemblyqt

.. note:: We can use the Distribute Menu in Ring Notepad

.. tip:: The option ( Prepare Qt project for WebAssembly ) in the Distribute Menu

.. image:: qtwebasm_shot1.png
	:alt: Qt for WebAssembly shot1

.. index:: 
	pair: Building RingQt Applications for WebAssembly; The Qt project for your Ring application

The Qt project for your Ring application
========================================

After using Ring2EXE or the Distribute Menu in Ring Notepad 

*  Using the Qt Creator Open the generated Qt project 

	Folder : target/webassembly/qtproject

	Project file : project.pro

.. image:: qtwebasm_shot2.png
	:alt: Qt for WebAssembly shot2

* Using Qt Creator, You will find the compiled Ring application in the resources (YourAppName.ringo)

	This file (Ring Object File) is generated by the Ring compiler using

.. code-block:: none

	ring YourAppName.ring -go -norun

* You can build your application using Qt Creator

.. image:: qtwebasm_shot3.png
	:alt: Qt for WebAssembly shot3

The next screen shot for the application during the runtime

.. image:: qtwebasm_shot4.png
	:alt: Qt for WebAssembly shot4

(1) You can add your application images to the resources

	Or You can use any text editor (Notepad) and modify : project.qrc

(2) To find images from your Ring application, You need to use the file name in resources

	Example 

.. code-block:: ring

	if isWebAssembly()
 		mypic = new QPixmap(":/cards.jpg")
	else
    		mypic = new QPixmap("cards.jpg")
	ok

.. index:: 
	pair: Building RingQt Applications for WebAssembly; Comments about developing for WebAssembly using RingQt

Comments about developing for WebAssembly using RingQt
======================================================

(1) The main project file is main.cpp 

	This file load Ring Compiler/Virtual Machine and RingQt 

	Then get the Ring Object File during the runtime from the resources

	Then run the Ring Object File (ringapp.ringo) using the Ring VM 

	Through main.cpp you can extract more files from the resources to temp. folder once you
	add them (create projects with many files).

(2) use if isWebAssembly() when you want to modify the code just for WebAssembly

Example:

.. code-block:: ring

	if isWebAssembly()
		// WebAssembly code
	else
  		// other platforms
	ok

(3) When you deal with Qt Classes you can determine the images from resources (you don't need to copy them using main.cpp)


Example: 

.. code-block:: ring

	if isWebAssembly()
	    mypic = new QPixmap(":/cards.jpg")
	else
	    mypic = new QPixmap("cards.jpg")
	ok

Now RingQt comes with the AppFile() function to determine the file name 

Example:

.. code-block:: ring

	mypic = new QPixmap(AppFile("cards.jpg"))  # Desktop, Android or WebAssembly

(4) When you update your project code, You don't have to use Ring2EXE to generate the Qt project again

Just use the Distribute Menu in Ring Notepad and select (Generate Ring Object File)

Then copy the YourAppName.ringo file to target/webassembly/qtproject folder and accept replacing files.

(5) If your application folder contains a Qt resource file (project.qrc)

Then when you use Ring2EXE or Ring Notepad (Distribute - Prepare Qt project for WebAssembly) the 
resource file will be used 

See ring/applications/cards game as an example.

(6) Use stdlibcore.ring instead of stdlib.ring when using StdLib functions

(7) Use ClocksPerSecond() function instead of typing the value (1000)

(8) Nested events loops are not supported, use events for dialogs instead of calling the exec() method

(9) Using Sleep() or ProcessEvents() doesn't provide the expected results, use Qt Timers.

(10) We don't have a direct access to the File System because the applications are executed in a secure environment

.. tip:: We can use special functions for Uploading/Downloading files (See FileContent sample) 

.. index:: 
	pair: Building RingQt Applications for WebAssembly; Dialogs

Dialogs
=======

See the folder: ring/samples/UsingQtWASM

Folders: 

* ColorDialog
* FontDialog
* FileDialog
* FileContent

.. index:: 
	pair: Building RingQt Applications for WebAssembly; Online Applications

Online Applications
===================


* Hello World   : https://ring-lang.github.io/web/helloworld/project.html
* Matching Game : https://ring-lang.github.io/web/matching/project.html
* Pairs Game    : https://ring-lang.github.io/web/pairs/project.html
* Othello Game  : https://ring-lang.github.io/web/othello/project.html
* Game of Life  : https://ring-lang.github.io/web/gameoflife/project.html
* Form Designer : https://ring-lang.github.io/web/formdesigner/project.html

.. image:: othelloweb.png
	:alt: Othello Game
