load "stdlibcore.ring"
load "logger.ring"

# مسجل الأداء
Class PerformanceLogger from Logger {
    # المتغيرات
    metrics
    startTimes
    
    func init(filename) {
        super.init(filename)
        metrics = []
        startTimes = []
    }
    
    # بدء قياس أداء عملية
    func startOperation(name) {
        startTimes[name] = time()
    }
    
    # إنهاء قياس أداء عملية
    func endOperation(name, extraInfo = "") {
        if startTimes[name] {
            duration = time() - startTimes[name]
            logPerformance(name, duration, extraInfo)
        }
    }
    
    # تسجيل معلومات الأداء
    func logPerformance(operation, duration, extraInfo) {
        # تخزين المقاييس
        metrics + {
            "operation": operation,
            "duration": duration,
            "timestamp": date() + " " + time(),
            "extra": extraInfo
        }
        
        # تسجيل المعلومات
        message = sprintf("العملية: %s، المدة: %.3f ثانية، %s", 
                        [operation, duration, extraInfo])
        info(message)
    }
    
    # تسجيل مقاييس التدريب
    func logTrainingMetrics(epoch, loss, accuracy) {
        metrics + {
            "type": "training",
            "epoch": epoch,
            "loss": loss,
            "accuracy": accuracy,
            "timestamp": date() + " " + time()
        }
        
        message = sprintf("الحلقة: %d، الخسارة: %.4f، الدقة: %.4f",
                        [epoch, loss, accuracy])
        info(message)
    }
    
    # تسجيل إحصائيات الذاكرة
    func logMemoryStats(used, total) {
        metrics + {
            "type": "memory",
            "used": used,
            "total": total,
            "timestamp": date() + " " + time()
        }
        
        message = sprintf("الذاكرة المستخدمة: %d/%d ميجابايت (%.1f%%)",
                        [used, total, (used/total*100)])
        info(message)
    }
    
    # توليد تقرير أداء
    func generateReport(filename) {
        try {
            fp = fopen(filename, "w")
            
            # كتابة الترويسة
            fwrite(fp, "تقرير الأداء" + nl)
            fwrite(fp, "===========" + nl + nl)
            
            # تصنيف المقاييس
            operations = []
            training = []
            memory = []
            
            for metric in metrics {
                if metric["type"] = "training" {
                    training + metric
                }
                else if metric["type"] = "memory" {
                    memory + metric
                }
                else {
                    operations + metric
                }
            }
            
            # كتابة إحصائيات العمليات
            if len(operations) > 0 {
                fwrite(fp, "إحصائيات العمليات" + nl)
                fwrite(fp, "-----------------" + nl)
                for op in operations {
                    fwrite(fp, sprintf("العملية: %s" + nl, op["operation"]))
                    fwrite(fp, sprintf("المدة: %.3f ثانية" + nl, op["duration"]))
                    fwrite(fp, sprintf("الوقت: %s" + nl, op["timestamp"]))
                    if op["extra"] != "" {
                        fwrite(fp, sprintf("معلومات إضافية: %s" + nl, op["extra"]))
                    }
                    fwrite(fp, nl)
                }
            }
            
            # كتابة مقاييس التدريب
            if len(training) > 0 {
                fwrite(fp, "مقاييس التدريب" + nl)
                fwrite(fp, "--------------" + nl)
                for t in training {
                    fwrite(fp, sprintf("الحلقة: %d" + nl, t["epoch"]))
                    fwrite(fp, sprintf("الخسارة: %.4f" + nl, t["loss"]))
                    fwrite(fp, sprintf("الدقة: %.4f" + nl, t["accuracy"]))
                    fwrite(fp, sprintf("الوقت: %s" + nl, t["timestamp"]))
                    fwrite(fp, nl)
                }
            }
            
            # كتابة إحصائيات الذاكرة
            if len(memory) > 0 {
                fwrite(fp, "إحصائيات الذاكرة" + nl)
                fwrite(fp, "----------------" + nl)
                for m in memory {
                    fwrite(fp, sprintf("الذاكرة المستخدمة: %d/%d ميجابايت" + nl,
                                    [m["used"], m["total"]]))
                    fwrite(fp, sprintf("النسبة: %.1f%%" + nl,
                                    [m["used"]/m["total"]*100]))
                    fwrite(fp, sprintf("الوقت: %s" + nl, m["timestamp"]))
                    fwrite(fp, nl)
                }
            }
            
            fclose(fp)
            info("تم توليد تقرير الأداء: " + filename)
        catch
            error("خطأ في توليد تقرير الأداء: " + cCatchError)
        }
    }
    
    # إعادة تعيين المقاييس
    func resetMetrics {
        metrics = []
        startTimes = []
    }
}
