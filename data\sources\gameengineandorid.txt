.. index:: 
	single: Building Games For Android; Introduction

==========================
Building Games For Android
==========================

In this chapter we will learn about Building RingLibSDL Games for Mobile.

So we can create packages (*.apk) for the applications that are developed using Ring Game Engine for 2D Games.


.. index:: 
	pair: Building Games For Android; Download Requirements and Update the Android SDK

Download Requirements and Update the Android SDK
================================================

* The Android SDK Tools

	https://developer.android.com/studio/index.html

* The Android NDK (Tested using android-ndk-r10c)

		https://developer.android.com/ndk/index.html

* Apache Ant v1.8 or later 

		http://ant.apache.org/bindownload.cgi

* Java SE Development Kit (JDK) v6 or later

		http://www.oracle.com/technetwork/java/javase/downloads/jdk7-downloads-1880260.html

* Update the Android SDK to get the API and tools packages required for development

	Tested using Android 4.4.2 (API 19)

* In Windows - Define the next Environment Variables based on your system.

(1) JAVA_HOME

.. code-block:: ring
	
	For Example : C:\Program Files (x86)\Java\jdk1.8.0_05 

(2) ANDROID_HOME	

.. code-block:: ring

	For Example : B:\mahmoud\Tools\Java-Android\adt-bundle-windows-x86-20140702\sdk

.. index:: 
	pair: Building Games For Android; Project Folder

Project Folder
==============

Open the project folder : ring/extensions/android/ringlibsdl/project

.. image:: ringlibsdlandroid_shot1.png
	:alt: RingLibSDL for Android

You can add the source code (*.ring) and Images/Sound Files to the assets folder.

.. image:: ringlibsdlandroid_shot2.png
	:alt: RingLibSDL for Android

You will find the Flappy Bird 3000 Game ready for building.

The execution starts from the start.ring file

.. code-block:: ring

	load "game2.ring"

.. index:: 
	pair: Building Games For Android; Building the project

Building the project
====================

Move to the ring/extensions/android/ringlibsdl/project folder 

We can build using the next command (We need to do this for one time only).

.. code-block:: none

	ndk-build

Then we can create the package (*.apk) using the next command.

.. code-block:: none

	ant debug

We can write a batch file for building the project (file: build.bat)

.. code-block:: none

	rem You will need to modify this batch file based on your environment

	set JAVA_HOME=C:\Program Files (x86)\Java\jdk1.8.0_05
	set ANDROID_HOME=B:\mahmoud\Tools\JavaAndroid\adt-bundle-windows-x86-20140702\sdk
	set NDK_ROOT=B:\mahmoud\Tools\JavaAndroid\android-ndk-r10c

	set path=%path%;B:\mahmoud\Tools\JavaAndroid\android-ndk-r10c
	set path=%path%;B:\mahmoud\Tools\JavaAndroid\apache-ant-1.9.4\bin

	ndk-build