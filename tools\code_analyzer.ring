load "stdlibcore.ring"

# أداة تحليل الكود
Class CodeAnalyzer {
    # المتغيرات
    sourceDir
    metrics
    issues
    
    func init {
        sourceDir = "../src"
        metrics = []
        issues = []
    }
    
    # تحليل المشروع بالكامل
    func analyzeProject {
        ? "=== بدء تحليل المشروع ==="
        
        # تحليل الكود المصدري
        analyzeDirectory(sourceDir)
        
        # طباعة النتائج
        printResults()
        
        ? "=== اكتمل تحليل المشروع ==="
    }
    
    # تحليل مجلد
    func analyzeDirectory(path) {
        files = dir(path)
        for file in files {
            if right(file.name, 5) = ".ring" {
                analyzeFile(path + "/" + file.name)
            }
        }
    }
    
    # تحليل ملف
    func analyzeFile(filename) {
        ? "تحليل: " + filename
        
        # قراءة محتوى الملف
        content = read(filename)
        
        # تحليل التعقيد
        analyzeComplexity(filename, content)
        
        # تحليل جودة الكود
        analyzeCodeQuality(filename, content)
        
        # تحليل الأنماط
        analyzePatterns(filename, content)
    }
    
    # تحليل تعقيد الكود
    func analyzeComplexity(filename, content) {
        # حساب تعقيد McCabe
        complexity = calculateComplexity(content)
        
        # حساب عدد الأسطر
        lines = len(split(content, nl))
        
        # حساب عدد الدوال
        functions = countFunctions(content)
        
        # تخزين المقاييس
        metrics + [
            "filename": filename,
            "complexity": complexity,
            "lines": lines,
            "functions": functions
        ]
    }
    
    # حساب تعقيد McCabe
    func calculateComplexity(content) {
        complexity = 1
        
        # حساب عدد العبارات الشرطية
        complexity += count(content, "if")
        complexity += count(content, "for")
        complexity += count(content, "while")
        complexity += count(content, "switch")
        
        return complexity
    }
    
    # عد عدد الدوال
    func countFunctions(content) {
        return count(content, "func")
    }
    
    # تحليل جودة الكود
    func analyzeCodeQuality(filename, content) {
        # التحقق من طول الدوال
        checkFunctionLength(filename, content)
        
        # التحقق من التعليقات
        checkComments(filename, content)
        
        # التحقق من تنسيق الكود
        checkFormatting(filename, content)
    }
    
    # التحقق من طول الدوال
    func checkFunctionLength(filename, content) {
        maxLength = 50  # الحد الأقصى لطول الدالة
        
        functions = split(content, "func")
        for func in functions {
            lines = len(split(func, nl))
            if lines > maxLength {
                addIssue(filename, "دالة طويلة جداً: " + lines + " سطر")
            }
        }
    }
    
    # التحقق من التعليقات
    func checkComments(filename, content) {
        codeLines = len(split(content, nl))
        comments = count(content, "#")
        
        ratio = comments / codeLines
        if ratio < 0.1 {
            addIssue(filename, "نسبة التعليقات منخفضة: " + (ratio * 100) + "%")
        }
    }
    
    # التحقق من تنسيق الكود
    func checkFormatting(filename, content) {
        # التحقق من المسافات البادئة
        checkIndentation(filename, content)
        
        # التحقق من طول الأسطر
        checkLineLength(filename, content)
    }
    
    # التحقق من المسافات البادئة
    func checkIndentation(filename, content) {
        lines = split(content, nl)
        for line in lines {
            if len(line) > 0 and left(line, 1) != " " and left(line, 1) != "\t" {
                addIssue(filename, "سطر بدون مسافة بادئة: " + line)
            }
        }
    }
    
    # التحقق من طول الأسطر
    func checkLineLength(filename, content) {
        maxLength = 80
        lines = split(content, nl)
        for line in lines {
            if len(line) > maxLength {
                addIssue(filename, "سطر طويل جداً: " + len(line) + " حرف")
            }
        }
    }
    
    # تحليل الأنماط
    func analyzePatterns(filename, content) {
        # البحث عن الأنماط المتكررة
        findDuplicateCode(filename, content)
        
        # البحث عن الممارسات السيئة
        findBadPractices(filename, content)
    }
    
    # البحث عن الكود المتكرر
    func findDuplicateCode(filename, content) {
        # تقسيم الكود إلى كتل
        blocks = split(content, "}")
        
        # مقارنة الكتل
        for i = 1 to len(blocks) {
            for j = i + 1 to len(blocks) {
                similarity = calculateSimilarity(blocks[i], blocks[j])
                if similarity > 0.8 {  # 80% تشابه
                    addIssue(filename, "كود متكرر محتمل")
                }
            }
        }
    }
    
    # حساب نسبة التشابه بين نصين
    func calculateSimilarity(text1, text2) {
        # خوارزمية بسيطة لحساب التشابه
        matches = 0
        words1 = split(text1, " ")
        words2 = split(text2, " ")
        
        for word1 in words1 {
            if find(words2, word1) {
                matches++
            }
        }
        
        return matches / max(len(words1), len(words2))
    }
    
    # البحث عن الممارسات السيئة
    func findBadPractices(filename, content) {
        # التحقق من استخدام المتغيرات العامة
        if count(content, "global") > 0 {
            addIssue(filename, "استخدام متغيرات عامة")
        }
        
        # التحقق من الأرقام السحرية
        checkMagicNumbers(filename, content)
    }
    
    # التحقق من الأرقام السحرية
    func checkMagicNumbers(filename, content) {
        lines = split(content, nl)
        for line in lines {
            if isContainMagicNumber(line) {
                addIssue(filename, "استخدام رقم سحري: " + line)
            }
        }
    }
    
    # التحقق من وجود رقم سحري
    func isContainMagicNumber(line) {
        # تجاهل الأرقام الشائعة مثل 0 و 1
        numbers = ["0", "1"]
        words = split(line, " ")
        
        for word in words {
            if isNumber(word) and not find(numbers, word) {
                return true
            }
        }
        return false
    }
    
    # إضافة مشكلة
    func addIssue(filename, message) {
        issues + [
            "filename": filename,
            "message": message
        ]
    }
    
    # طباعة النتائج
    func printResults {
        ? "\n=== نتائج تحليل الكود ==="
        
        ? "\nمقاييس الكود:"
        for metric in metrics {
            ? sprintf("الملف: %s", metric["filename"])
            ? sprintf("التعقيد: %d", metric["complexity"])
            ? sprintf("عدد الأسطر: %d", metric["lines"])
            ? sprintf("عدد الدوال: %d", metric["functions"])
            ? "-------------------"
        }
        
        ? "\nالمشاكل المكتشفة:"
        for issue in issues {
            ? sprintf("%s: %s", issue["filename"], issue["message"])
        }
    }
    
    # تصدير النتائج إلى ملف
    func exportResults(filename) {
        file = fopen(filename, "w")
        
        write(file, "تقرير تحليل الكود\n")
        write(file, "===============\n\n")
        
        write(file, "مقاييس الكود:\n")
        write(file, "------------\n")
        for metric in metrics {
            write(file, sprintf("الملف: %s\n", metric["filename"]))
            write(file, sprintf("التعقيد: %d\n", metric["complexity"]))
            write(file, sprintf("عدد الأسطر: %d\n", metric["lines"]))
            write(file, sprintf("عدد الدوال: %d\n", metric["functions"]))
            write(file, "-------------------\n")
        }
        
        write(file, "\nالمشاكل المكتشفة:\n")
        write(file, "---------------\n")
        for issue in issues {
            write(file, sprintf("%s: %s\n", issue["filename"], issue["message"]))
        }
        
        fclose(file)
    }
}
