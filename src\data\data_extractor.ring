load "stdlibcore.ring"
load "../utils/data_utils.ring"
load "../utils/config.ring"

# إنشاء كائن من الصنف
extractor = new DataExtractor()


# معالجة ملفات الترجمة
processDirectory(extractor, "../../data/Translation", "translation")

# معالجة ملفات الكود
processDirectory(extractor, "../../data/sources", "code")

# حفظ البيانات المستخرجة
extractor.saveToFile("../../processed/training_data.txt")

# طباعة الإحصائيات
extractor.printStats()

# دالة لاستخراج جميع الملفات في مجلد
func processDirectory(obj, cDir, cType) {
    if ! extractor.utils.isDirectory(cDir) {
        system("mkdir " + cDir)
        extractor.utils.debug("تم إنشاء المجلد: " + cDir)
        return
    }
    
    aFiles = dir(cDir)
    for file in aFiles {
        if right(file[1], 4) = ".txt" {
            if cType = "translation" {
                obj.processTranslationFile(cDir + "/" + file[1])
            else 
                obj.processCodeFile(cDir + "/" + file[1])
            }
        }
    }
}

Class DataExtractor {
    # المتغيرات
    aTranslationPairs    # أزواج الترجمة
    aCodeExamples       # أمثلة الكود
    batchSize          # حجم الدفعة
    config             # إعدادات الترانسفورمر
    utils              # أدوات مساعدة
    
    func init {
        aTranslationPairs = []
        aCodeExamples = []
        batchSize = 32
        config = new TransformerConfig
        utils = new DataUtils
    }
    
    # معالجة ملف ترجمة
    func processTranslationFile(filePath) {
        content = utils.readFile(filePath)
        if content = "" return ok
        
        try {
            lines = str2list(content)
            for line in lines {
                if line != "" {
                    parts = split(line, char(9))  # فصل بالتاب
                    if len(parts) = 2 {
                        source = utils.addSpecialTokens(parts[1], "translation", config)
                        target = utils.addSpecialTokens(parts[2], "", config)
                        aTranslationPairs + [source, target]
                    }
                }
            }
            utils.debug("تمت معالجة ملف الترجمة: " + filePath)
        catch
            utils.debug("خطأ في معالجة ملف الترجمة: " + filePath)
        }
    }
    
    # معالجة ملف كود
    func processCodeFile(filePath) {
        content = utils.readFile(filePath)
        if content = "" return ok
        
        try {
            lines = str2list(content)
            currentCode = ""
            currentComment = ""
            
            for line in lines {
                line = trim(line)
                if left(line, 2) = "//" or left(line, 1) = "#" {
                    if currentComment != "" { 
                        currentComment += nl 
                    }
                    currentComment += substr(line, 3)
                else 
                    if line != "" {
                        if currentCode != "" { 
                            currentCode += nl 
                        }
                        currentCode += line
                    else
                        if currentCode != "" and currentComment != "" {
                            codeWithTokens = utils.addSpecialTokens(currentCode, "code", config)
                            commentWithTokens = utils.addSpecialTokens(currentComment, "", config)
                            aCodeExamples + [codeWithTokens, commentWithTokens]
                            currentCode = ""
                            currentComment = ""
                        }
                    }
                }
            }
            
            # معالجة آخر مثال إذا وجد
            if currentCode != "" and currentComment != "" {
                codeWithTokens = utils.addSpecialTokens(currentCode, "code", config)
                commentWithTokens = utils.addSpecialTokens(currentComment, "", config)
                aCodeExamples + [codeWithTokens, commentWithTokens]
            }
            
            utils.debug("تمت معالجة ملف الكود: " + filePath)
        catch
            utils.debug("خطأ في معالجة ملف الكود: " + filePath)
        }
    }
    
    # حفظ البيانات المستخرجة
    func saveToFile(filePath) {
        try {
            file = fopen(filePath, "w")
            
            # حفظ أزواج الترجمة
            for pair in aTranslationPairs {
                fwrite(file, pair[1] + char(9) + pair[2] + nl)
            }
            
            # حفظ أمثلة الكود
            for example in aCodeExamples {
                fwrite(file, example[1] + char(9) + example[2] + nl)
            }
            
            fclose(file)
            utils.debug("تم حفظ البيانات في: " + filePath)
            return true
        catch
            utils.debug("خطأ في حفظ البيانات: " + filePath)
            return false
        }
    }
    
    # طباعة إحصائيات المعالجة
    func printStats {
        utils.debug("إحصائيات المعالجة:")
        utils.debug("- عدد أزواج الترجمة: " + len(aTranslationPairs))
        utils.debug("- عدد أمثلة الكود: " + len(aCodeExamples))
    }
}
