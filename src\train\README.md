# 🤖 نظام تدريب وتقييم المحول الذكي (Smart Transformer Training System)

## 📝 نظرة عامة
نظام متكامل لتدريب وتقييم نموذج محول (Transformer) متعدد الأغراض، مصمم خصيصاً للترجمة بين العربية والإنجليزية وتوليد الشيفرات البرمجية.

## 🏗️ هيكل المشروع

### 📂 المجلدات الرئيسية
```
src/
├── train/              # نظام التدريب
│   ├── trainer.ring    # المدرب الرئيسي
│   └── optimizer.ring  # محسن Adam
├── core/               # مكونات النموذج الأساسية
│   ├── transformer.ring
│   ├── attention.ring
│   └── advanced_evaluator.ring
├── data/               # معالجة البيانات
│   ├── processed/      # البيانات المعالجة
│   │   ├── train/     # بيانات التدريب
│   │   └── test/      # بيانات الاختبار
│   ├── data_processor.ring
│   └── data_extractor.ring
└── utils/              # أدوات مساعدة
    ├── config.ring
    └── data_utils.ring
```

## 🚀 المكونات الرئيسية

### 1. نظام التدريب (Training System)
- **trainer.ring**: المدرب الرئيسي
  * تحميل وتقسيم البيانات
  * إدارة حلقة التدريب
  * معالجة البيانات على دفعات
  * حفظ النموذج وتتبع التقدم

- **optimizer.ring**: محسن Adam
  * تحديث معلمات النموذج
  * دعم الزخم والتكيف التلقائي
  * تصحيح التحيز في المتوسطات المتحركة

### 2. نظام التقييم (Evaluation System)
- **advanced_evaluator.ring**:
  * تقييم الترجمات باستخدام BLEU
  * تقييم دقة توليد الكود
  * حساب مقاييس الأداء المختلفة
  * حفظ وتوثيق نتائج التقييم

## 💾 البيانات

### بيانات التدريب (processed/train/)
- **translations.txt**: أزواج الترجمة (عربي-إنجليزي)
- **code_examples.txt**: أمثلة الشيفرات البرمجية

### بيانات الاختبار (processed/test/)
- **translations.txt**: عينات اختبار الترجمة
- **code_examples.txt**: عينات اختبار توليد الكود

## 🛠️ كيفية الاستخدام

### 1. تدريب النموذج
```ring
load "trainer.ring"
trainer = new TransformerTrainer
trainer.loadTrainingData()
trainer.train()
```

### 2. تقييم النموذج
```ring
load "advanced_evaluator.ring"
evaluator = new AdvancedEvaluator
evaluator.loadModel("models/latest_model.bin")
evaluator.evaluateOnTestData()
```

## 📊 المقاييس المستخدمة

### للترجمة
- **BLEU Score**: لقياس جودة الترجمة
- **Precision**: دقة الكلمات المترجمة
- **Recall**: تغطية الكلمات المرجعية

### لتوليد الكود
- **Accuracy**: دقة الكود المولد
- **F1 Score**: توازن بين الدقة والتغطية

## ⚙️ الإعدادات

يمكن تعديل الإعدادات في `config.ring`:
- حجم الدفعة (Batch Size)
- معدل التعلم (Learning Rate)
- عدد الحلقات (Epochs)
- أبعاد النموذج (Model Dimensions)

## 🔄 دورة التدريب والتقييم

1. **معالجة البيانات**
   - تنظيف وتوحيد النصوص
   - إضافة رموز خاصة
   - تقسيم إلى تدريب واختبار

2. **التدريب**
   - تحميل البيانات على دفعات
   - حساب الخسارة وتحديث النموذج
   - تتبع تقدم التدريب
   - حفظ أفضل النماذج

3. **التقييم**
   - اختبار على بيانات جديدة
   - حساب المقاييس المختلفة
   - توثيق النتائج

## 🔍 الميزات المتقدمة

- **معالجة متوازية** للبيانات
- **ذاكرة مؤقتة** لتحسين الأداء
- **تقييم شامل** للنموذج
- **توثيق تلقائي** للنتائج

## 📈 التحسينات المستقبلية

1. إضافة دعم للغات جديدة
2. تحسين أداء المعالجة المتوازية
3. إضافة مقاييس تقييم متقدمة
4. تطوير واجهة مستخدم رسومية
5. دعم المزيد من أنواع الشيفرات البرمجية

## 🔒 الأمان والموثوقية

- التحقق من صحة المدخلات
- معالجة آمنة للملفات
- تسجيل الأخطاء والتتبع
- نسخ احتياطية للنماذج

## 📚 المراجع

- نموذج Transformer الأصلي
- خوارزمية Adam للتحسين
- مقياس BLEU للتقييم
- أفضل ممارسات معالجة اللغات الطبيعية

## 👥 المساهمة

نرحب بالمساهمات! يرجى:
1. اتباع أسلوب الكود الموحد
2. إضافة اختبارات للميزات الجديدة
3. توثيق التغييرات بشكل واضح
4. مراجعة التغييرات قبل الدمج
